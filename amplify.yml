version: 1
backend:
  phases:
    build:
      commands:
        - echo "DATABASE_URL=$DATABASE_URL" >> .env
        - echo "WEB_DATABASE_URL=$WEB_DATABASE_URL" >> .env
        - npm ci --cache .npm --prefer-offline
        - npx ampx pipeline-deploy --branch $AWS_BRANCH --app-id $AWS_APP_ID
frontend:
  phases:
    build:
      commands:
        - echo "DATABASE_URL=$DATABASE_URL" >> .env
        - echo "WEB_DATABASE_URL=$WEB_DATABASE_URL" >> .env
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - "**/*"
  cache:
    paths:
      - .next/cache/**/*
      - .npm/**/*
      - node_modules/**/*