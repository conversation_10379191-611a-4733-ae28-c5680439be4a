"use client";

import React, { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  User,
  Calendar,
  FileText,
  ChevronDown,
  ChevronUp,
  CreditCard,
  Check,
  AlertTriangle,
  X,
} from "lucide-react";

export default function FractionPurchaseScreen({
  params,
}: {
  params: { id: string };
}): React.ReactNode {
  const router = useRouter();
  const fractionId = params.id;
  const signatureCanvasRef = useRef<HTMLCanvasElement>(null);
  const [showLegalDetails, setShowLegalDetails] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    string | null
  >("card1");
  const [isDrawing, setIsDrawing] = useState(false);
  const [signature, setSignature] = useState<string | null>(null);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Mock data - in a real app, this would come from an API
  const fraction = {
    id: fractionId,
    vehicleName:
      fractionId === "1"
        ? "Toyota Hilux"
        : fractionId === "2"
        ? "Volkswagen Polo"
        : "Ford Ranger",
    year: 2021,
    image: "/placeholder.svg?height=120&width=200",
    price: fractionId === "1" ? 150000 : fractionId === "2" ? 120000 : 180000,
    ownershipPercentage: 25,
    ownerName: "Jane Cooper",
    usageDays: 7,
    location:
      fractionId === "1"
        ? "Cape Town, Western Cape"
        : fractionId === "2"
        ? "Johannesburg, Gauteng"
        : "Durban, KwaZulu-Natal",
    description:
      fractionId === "1"
        ? "Well-maintained Toyota Hilux in excellent condition. Low kilometers, clean interior, and regularly serviced."
        : fractionId === "2"
        ? "Economical Volkswagen Polo perfect for city driving. Fuel efficient with great handling."
        : "Powerful Ford Ranger with excellent towing capacity. Perfect for work and weekend adventures.",
  };

  const paymentMethods = [
    {
      id: "card1",
      type: "card",
      name: "Visa ending in 4242",
      icon: <CreditCard size={20} className="text-[#0286ff]" />,
      details: "Expires 12/25",
    },
    {
      id: "card2",
      type: "card",
      name: "Mastercard ending in 5678",
      icon: <CreditCard size={20} className="text-[#0286ff]" />,
      details: "Expires 10/24",
    },
  ];

  // Signature pad handlers
  const startDrawing = (
    e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>
  ) => {
    setIsDrawing(true);
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    ctx.beginPath();

    // Get position based on event type
    let clientX, clientY;
    if ("touches" in e) {
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }

    const rect = canvas.getBoundingClientRect();
    ctx.moveTo(clientX - rect.left, clientY - rect.top);
  };

  const draw = (
    e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>
  ) => {
    if (!isDrawing) return;

    const canvas = signatureCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Get position based on event type
    let clientX, clientY;
    if ("touches" in e) {
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }

    const rect = canvas.getBoundingClientRect();
    ctx.lineTo(clientX - rect.left, clientY - rect.top);
    ctx.stroke();
  };

  const stopDrawing = () => {
    if (!isDrawing) return;
    setIsDrawing(false);

    const canvas = signatureCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    ctx.closePath();
    setSignature(canvas.toDataURL());
  };

  const clearSignature = () => {
    setSignature(null);
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
  };

  const handlePaymentMethodSelect = (id: string) => {
    setSelectedPaymentMethod(id);
  };

  const handleCompletePurchase = () => {
    if (!signature || !termsAccepted || !selectedPaymentMethod) return;

    setIsProcessing(true);
    // In a real app, you would make an API call here
    setTimeout(() => {
      router.push(`/purchase-confirmation/${fractionId}`);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-[#eef1f9] pb-24">
      {/* Status Bar */}
      <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#eef1f9"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white px-6 py-4 flex items-center border-b border-[#f2f2f2]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-[#333333]" />
        </button>
        <h1 className="text-xl font-bold text-[#333333]">Purchase Fraction</h1>
      </div>

      {/* Vehicle Details */}
      <div className="px-4 py-4">
        <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-4">
          <div className="h-48 bg-[#f2f2f2] relative">
            <Image
              src={fraction.image || "/placeholder.svg"}
              alt={fraction.vehicleName}
              fill
              className="object-cover"
            />
          </div>

          <div className="p-4">
            <div className="flex justify-between items-center mb-1">
              <h2 className="text-xl font-bold text-[#333333]">
                {fraction.vehicleName}
              </h2>
              <span className="text-sm font-medium bg-[#e6f3ff] text-[#0286ff] px-2 py-1 rounded-full">
                {fraction.ownershipPercentage}% Ownership
              </span>
            </div>

            <p className="text-[#797879] mb-3">
              {fraction.year} • {fraction.location}
            </p>

            <p className="text-[#333333] mb-4">{fraction.description}</p>

            <div className="flex items-center justify-between p-3 bg-[#f9f9f9] rounded-lg">
              <div>
                <span className="text-xs text-[#797879]">Price</span>
                <span className="text-xl font-bold text-[#0286ff]">
                  R{fraction.price.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Legal Implications */}
      <div className="px-4">
        <div className="bg-white rounded-xl shadow-sm p-4 mb-4">
          <button
            className="w-full flex items-center justify-between"
            onClick={() => setShowLegalDetails(!showLegalDetails)}
          >
            <div className="flex items-center">
              <FileText size={20} className="text-[#0286ff] mr-2" />
              <h3 className="text-[#333333] font-medium">Legal Implications</h3>
            </div>
            {showLegalDetails ? (
              <ChevronUp size={20} className="text-[#797879]" />
            ) : (
              <ChevronDown size={20} className="text-[#797879]" />
            )}
          </button>

          {showLegalDetails && (
            <div className="mt-3 p-3 bg-[#f9f9f9] rounded-lg max-h-40 overflow-y-auto">
              <p className="text-sm text-[#797879]">
                By purchasing this vehicle fraction, you understand and agree to
                the following:
                <br />
                <br />
                1. You are purchasing a {fraction.ownershipPercentage}%
                ownership stake in the vehicle.
                <br />
                <br />
                2. This purchase entitles you to use the vehicle for{" "}
                {fraction.usageDays} days per month.
                <br />
                <br />
                3. You will be responsible for a proportional share of
                maintenance, insurance, and other costs.
                <br />
                <br />
                4. All co-owners must adhere to the Pull Co-Ownership Agreement.
                <br />
                <br />
                5. Disputes between co-owners will be resolved according to the
                terms in the agreement.
                <br />
                <br />
                6. You have the right to sell your fraction at any time, subject
                to the terms in the agreement.
              </p>
            </div>
          )}

          <div className="mt-3 flex items-start">
            <input
              type="checkbox"
              id="termsAccepted"
              checked={termsAccepted}
              onChange={(e) => setTermsAccepted(e.target.checked)}
              className="mt-1 h-4 w-4 rounded border-[#d6d9dd] text-[#0286ff] focus:ring-[#0286ff]"
            />
            <label
              htmlFor="termsAccepted"
              className="ml-2 text-sm text-[#797879]"
            >
              I have read and agree to the co-ownership terms and conditions
            </label>
          </div>
        </div>
      </div>

      {/* Payment Method */}
      <div className="px-4">
        <div className="bg-white rounded-xl shadow-sm p-4 mb-4">
          <h3 className="text-[#333333] font-medium mb-3">Payment Method</h3>

          <div className="space-y-3">
            {paymentMethods.map((method) => (
              <div
                key={method.id}
                className={`p-3 rounded-lg border ${
                  selectedPaymentMethod === method.id
                    ? "border-[#0286ff] bg-[#e6f3ff]"
                    : "border-[#d6d9dd]"
                }`}
                onClick={() => handlePaymentMethodSelect(method.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {method.icon}
                    <div className="ml-3">
                      <p className="text-[#333333] font-medium">
                        {method.name}
                      </p>
                      <p className="text-xs text-[#797879]">{method.details}</p>
                    </div>
                  </div>
                  {selectedPaymentMethod === method.id && (
                    <Check size={20} className="text-[#0286ff]" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Digital Signature */}
      <div className="px-4">
        <div className="bg-white rounded-xl shadow-sm p-4 mb-4">
          <h3 className="text-[#333333] font-medium mb-3">Digital Signature</h3>

          {signature ? (
            <div className="relative border border-[#d6d9dd] rounded-lg p-2 h-40 mb-2">
              <Image
                src={signature || "/placeholder.svg"}
                alt="Signature"
                fill
                className="object-contain"
              />
              <button
                className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm"
                onClick={clearSignature}
              >
                <X size={16} className="text-red-500" />
              </button>
            </div>
          ) : (
            <div className="border border-[#d6d9dd] rounded-lg bg-[#f9f9f9] h-40 mb-2 relative">
              <canvas
                ref={signatureCanvasRef}
                width={400}
                height={160}
                className="w-full h-full touch-none"
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={stopDrawing}
                onMouseLeave={stopDrawing}
                onTouchStart={startDrawing}
                onTouchMove={draw}
                onTouchEnd={stopDrawing}
              />
              <p className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-[#797879] pointer-events-none">
                Sign here
              </p>
            </div>
          )}

          <p className="text-xs text-[#797879] text-center">
            By signing, you confirm your agreement to purchase this vehicle
            fraction
          </p>
        </div>
      </div>

      {/* Purchase Summary */}
      <div className="px-4">
        <div className="bg-white rounded-xl shadow-sm p-4 mb-4">
          <h3 className="text-[#333333] font-medium mb-3">Purchase Summary</h3>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-[#797879]">Fraction Price</span>
              <span className="text-[#333333]">
                R{fraction.price.toLocaleString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879]">Service Fee (5%)</span>
              <span className="text-[#333333]">
                R{(fraction.price * 0.05).toLocaleString()}
              </span>
            </div>
            <div className="border-t border-[#f2f2f2] my-2 pt-2 flex justify-between">
              <span className="text-[#333333] font-medium">Total</span>
              <span className="text-[#333333] font-bold">
                R{(fraction.price * 1.05).toLocaleString()}
              </span>
            </div>
          </div>

          <div className="mt-3 p-3 bg-yellow-50 rounded-lg flex items-start">
            <AlertTriangle
              size={18}
              className="text-yellow-500 mr-2 flex-shrink-0 mt-0.5"
            />
            <p className="text-sm text-yellow-700">
              This purchase is binding. You will be charged immediately upon
              confirmation.
            </p>
          </div>
        </div>
      </div>

      {/* Complete Purchase Button */}
      <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2]">
        <button
          className={`w-full py-4 rounded-full text-xl font-semibold ${
            signature && termsAccepted && selectedPaymentMethod && !isProcessing
              ? "bg-[#0286ff] text-white"
              : "bg-[#f2f2f2] text-[#797879]"
          }`}
          onClick={handleCompletePurchase}
          disabled={
            !signature ||
            !termsAccepted ||
            !selectedPaymentMethod ||
            isProcessing
          }
        >
          {isProcessing ? "Processing..." : "Complete Purchase"}
        </button>
      </div>
    </div>
  );
}
