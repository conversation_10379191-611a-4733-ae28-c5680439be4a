"use client";

import { apiErrorFormater } from "@/lib/utils";
import { useEffect } from "react";
export default function PostFormError({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  useEffect(() => {
    console.error("Captured error:", error);
  }, [error]);

  return (
    <div className="h-screen flex items-center justify-center bg-gray-100 text-gray-800">
      <div className="bg-white p-8 rounded-xl shadow-lg text-center max-w-md">
        <h1 className="text-3xl font-bold text-red-600 mb-4">
          Oops! Something went wrong
        </h1>
        <div>
          {apiErrorFormater(error)?.errors && (
            <div className="bg-red-100 text-red-700 p-4 rounded">
              {Object.entries(apiErrorFormater(error).errors).map(
                ([field, messages]) =>
                  Array.isArray(messages)
                    ? messages.map((msg, i) => (
                        <li key={`${field}-${i}`}>{msg}</li>
                      ))
                    : null
              )}
            </div>
          )}
        </div>
        <button
          onClick={reset}
          className="px-4 py-2 bg-[#009639] text-white rounded hover:bg-green-700 transition"
        >
          Try Again
        </button>
      </div>
    </div>
  );
}
