"use client";

import React, { use } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  CheckCircle,
  Calendar,
  Car,
  Share2,
  ChevronRight,
  Wallet,
} from "lucide-react";

export default function PurchaseConfirmationScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}): React.ReactNode {
  const router = useRouter();
  const purchaseId = use(params).id;

  // Mock data - in a real app, this would come from an API
  const purchase = {
    id: purchaseId,
    vehicleName:
      purchaseId === "1"
        ? "Toyota Hilux"
        : purchaseId === "2"
        ? "Volkswagen Polo"
        : "Ford Ranger",
    year: 2021,
    image: "/placeholder.svg?height=120&width=200",
    price: purchaseId === "1" ? 150000 : purchaseId === "2" ? 120000 : 180000,
    ownershipPercentage: 25,
    usageDays: 7,
    location:
      purchaseId === "1"
        ? "Cape Town, Western Cape"
        : purchaseId === "2"
        ? "Johannesburg, Gauteng"
        : "Durban, KwaZulu-Natal",
    transactionDate: new Date().toISOString(),
    transactionId:
      "TRX" +
      Math.floor(Math.random() * 1000000)
        .toString()
        .padStart(6, "0"),
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-ZA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <>
      <div className="min-h-screen bg-white">
        {/* Status Bar */}
        <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
          <div className="font-semibold">9:41</div>
          <div className="flex items-center gap-1">
            <div className="h-3 w-4">
              <svg
                viewBox="0 0 20 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M1 1H19"
                  stroke="#333333"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M4 5H16"
                  stroke="#333333"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M7 9H13"
                  stroke="#333333"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
              </svg>
            </div>
            <div className="h-3 w-4">
              <svg
                viewBox="0 0 16 12"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                  fill="#333333"
                />
                <path
                  d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                  fill="#eef1f9"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Success Message */}
        <div className="bg-[#009639] px-6 py-8 flex flex-col items-center border-b border-[#007A2F]">
          <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
            <CheckCircle size={40} className="text-[#009639]" />
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">
            Purchase Complete!
          </h1>
          <p className="text-white text-center">
            You now own {purchase.ownershipPercentage}% of this vehicle
          </p>
        </div>

        {/* Vehicle Details */}
        <div className="px-4 py-4">
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
            <div className="h-40 bg-[#f2f2f2] relative">
              <Image
                src={purchase.image || "/placeholder.svg"}
                alt={purchase.vehicleName}
                fill
                className="object-cover"
              />
            </div>

            <div className="p-4">
              <div className="flex justify-between items-center mb-1">
                <h2 className="text-lg font-bold text-[#333333]">
                  {purchase.vehicleName}
                </h2>
                <span className="text-sm font-medium bg-[#e6ffe6] text-[#009639] px-2 py-1 rounded-full">
                  {purchase.ownershipPercentage}% Ownership
                </span>
              </div>

              <p className="text-[#797879] my-4">
                {purchase.year} • {purchase.location}
              </p>

              <div className="flex items-center justify-between p-4 bg-[#e6ffe6] rounded-lg">
                <div>
                  <p className="text-sm text-[#007A2F] mb-1 font-medium">
                    Purchase Price
                  </p>
                  <p className="text-2xl font-bold text-[#009639]">
                    R{purchase.price.toLocaleString()}
                  </p>
                </div>
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-sm">
                  <Wallet size={20} className="text-[#009639]" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Transaction Details */}
        <div className="px-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">
              Transaction Details
            </h3>

            <div className="space-y-3">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Calendar size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Date</p>
                  <p className="text-[#333333]">
                    {formatDate(purchase.transactionDate)}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <span className="text-[#009639] font-bold text-xs">#</span>
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Transaction ID</p>
                  <p className="text-[#333333]">{purchase.transactionId}</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Car size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Usage</p>
                  <p className="text-[#333333]">
                    {purchase.usageDays} days per month
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="px-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">Next Steps</h3>

            <div className="space-y-3">
              <div className="p-3 bg-[#f9f9f9] rounded-lg flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                    <Calendar size={12} className="text-[#009639]" />
                  </div>
                  <span className="text-[#333333]">Schedule Usage</span>
                </div>
                <button
                  onClick={() => router.push(`/booking-calendar/${purchaseId}`)}
                >
                  <ChevronRight size={20} className="text-[#009639]" />
                </button>
              </div>

              <div className="p-3 bg-[#f9f9f9] rounded-lg flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                    <Car size={12} className="text-[#009639]" />
                  </div>
                  <span className="text-[#333333]">View Vehicle Details</span>
                </div>
                <button
                  onClick={() => router.push(`/vehicle-status/${purchaseId}`)}
                >
                  <ChevronRight size={20} className="text-[#009639]" />
                </button>
              </div>

              <div className="p-3 bg-[#f9f9f9] rounded-lg flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                    <Share2 size={12} className="text-[#009639]" />
                  </div>
                  <span className="text-[#333333]">Invite Co-owners</span>
                </div>
                <button
                  onClick={() => router.push(`/invite-co-owners/${purchaseId}`)}
                >
                  <ChevronRight size={20} className="text-[#009639]" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Return to Home Button */}
        <div className="px-4 py-4">
          <button
            className="w-full py-4 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm"
            onClick={() => router.push("/home")}
          >
            Return to Home
          </button>
        </div>
      </div>
    </>
  );
}
