"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Shield,
  Lock,
  Eye,
  EyeOff,
  ChevronRight,
  Fingerprint,
} from "lucide-react";

export default function PrivacySecurityScreen() {
  const router = useRouter();
  const [showPasswordOptions, setShowPasswordOptions] = useState(false);

  // Mock data - in a real app, this would come from an API
  const [settings, setSettings] = useState({
    biometricLogin: true,
    locationTracking: true,
    dataSharing: false,
    activityHistory: true,
  });

  const handleToggle = (setting: keyof typeof settings) => {
    setSettings({
      ...settings,
      [setting]: !settings[setting],
    });
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div> */}

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Privacy & Security</h1>
      </div>

      {/* Account Security */}
      <div className="p-4">
        <h3 className="text-[#333333] font-medium mb-3">Account Security</h3>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div
            className="p-4 border-b border-[#f2f2f2] flex items-center"
            onClick={() => setShowPasswordOptions(!showPasswordOptions)}
          >
            <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
              <Lock size={18} className="text-[#009639]" />
            </div>
            <div className="flex-1">
              <h3 className="text-[#333333] font-medium">Password</h3>
              <p className="text-xs text-[#797879]">Last changed 30 days ago</p>
            </div>
            <ChevronRight
              size={20}
              className={`text-[#797879] transition-transform ${
                showPasswordOptions ? "rotate-90" : ""
              }`}
            />
          </div>

          {showPasswordOptions && (
            <div className="bg-[#f9fff9] p-4 border-b border-[#f2f2f2]">
              <button
                className="w-full py-2 mb-2 text-sm bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full shadow-md"
                onClick={() => router.push("/change-password")}
              >
                Change Password
              </button>
              <button
                className="w-full py-2 text-sm border border-[#009639] text-[#009639] rounded-full shadow-sm"
                onClick={() => router.push("/forgot-password")}
              >
                Forgot Password
              </button>
            </div>
          )}

          <div className="p-4 border-b border-[#f2f2f2] flex items-center">
            <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
              <Fingerprint size={18} className="text-[#009639]" />
            </div>
            <div className="flex-1">
              <h3 className="text-[#333333] font-medium">Biometric Login</h3>
              <p className="text-xs text-[#797879]">
                Use Face ID or Touch ID to log in
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.biometricLogin}
                onChange={() => handleToggle("biometricLogin")}
              />
              <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
            </label>
          </div>

          <div className="p-4 flex items-center">
            <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
              <Shield size={18} className="text-[#009639]" />
            </div>
            <div className="flex-1">
              <h3 className="text-[#333333] font-medium">
                Two-Factor Authentication
              </h3>
              <p className="text-xs text-[#797879]">
                Add an extra layer of security
              </p>
            </div>
            <button
              className="text-[#009639]"
              onClick={() => router.push("/two-factor-auth")}
            >
              <ChevronRight size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Privacy Settings */}
      <div className="p-4">
        <h3 className="text-[#333333] font-medium mb-3">Privacy Settings</h3>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2] flex items-center">
            <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
              <Eye size={18} className="text-[#009639]" />
            </div>
            <div className="flex-1">
              <h3 className="text-[#333333] font-medium">Location Tracking</h3>
              <p className="text-xs text-[#797879]">
                Allow app to track your location
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.locationTracking}
                onChange={() => handleToggle("locationTracking")}
              />
              <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
            </label>
          </div>

          <div className="p-4 border-b border-[#f2f2f2] flex items-center">
            <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
              <EyeOff size={18} className="text-[#009639]" />
            </div>
            <div className="flex-1">
              <h3 className="text-[#333333] font-medium">Data Sharing</h3>
              <p className="text-xs text-[#797879]">
                Share usage data to improve services
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.dataSharing}
                onChange={() => handleToggle("dataSharing")}
              />
              <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
            </label>
          </div>

          <div className="p-4 flex items-center">
            <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
              <Shield size={18} className="text-[#009639]" />
            </div>
            <div className="flex-1">
              <h3 className="text-[#333333] font-medium">Activity History</h3>
              <p className="text-xs text-[#797879]">
                Store your activity history
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                className="sr-only peer"
                checked={settings.activityHistory}
                onChange={() => handleToggle("activityHistory")}
              />
              <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Privacy Documents */}
      <div className="p-4">
        <h3 className="text-[#333333] font-medium mb-3">Legal Documents</h3>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div
            className="p-4 border-b border-[#f2f2f2] flex items-center"
            onClick={() => router.push("/privacy-policy")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333] font-medium">
                Privacy Policy (South Africa)
              </h3>
              <p className="text-xs text-[#797879]">POPIA Compliant</p>
            </div>
            <ChevronRight size={20} className="text-[#009639]" />
          </div>

          <div
            className="p-4 border-b border-[#f2f2f2] flex items-center"
            onClick={() => router.push("/terms-of-service")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333] font-medium">
                Terms of Service (South Africa)
              </h3>
              <p className="text-xs text-[#797879]">
                Consumer Protection Act Compliant
              </p>
            </div>
            <ChevronRight size={20} className="text-[#009639]" />
          </div>

          <div
            className="p-4 flex items-center"
            onClick={() => router.push("/data-deletion")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333] font-medium">
                Data Deletion Request (POPIA Compliance)
              </h3>
              <p className="text-xs text-[#797879]">
                As per South African regulations
              </p>
            </div>
            <ChevronRight size={20} className="text-[#009639]" />
          </div>
        </div>
      </div>
    </div>
  );
}
