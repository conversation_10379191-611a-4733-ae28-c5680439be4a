"use client";

import { use } from "react";
import VehicleToGroupForm from "@/components/vehicle-to-group-form";

export default function AddVehicleToGroupPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const groupId = parseInt(use(params).id);

  // TODO: Replace with actual current user party ID from auth context
  const currentUserPartyId = 1; // Placeholder

  return (
    <div className="min-h-screen bg-[#f5f5f5] py-8">
      <div className="container mx-auto px-4">
        <VehicleToGroupForm
          currentUserPartyId={currentUserPartyId}
          preselectedCompanyId={groupId}
          onSuccess={(data) => {
            // Redirect back to group details on success
            window.location.href = `/group-details/${groupId}`;
          }}
          onCancel={() => {
            // Go back to group details
            window.location.href = `/group-details/${groupId}`;
          }}
        />
      </div>
    </div>
  );
} 