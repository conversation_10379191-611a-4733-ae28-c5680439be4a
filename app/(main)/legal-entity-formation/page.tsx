"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Building,
  Users,
  FileText,
  ChevronDown,
  Info,
  Check,
  AlertCircle,
  MapPin,
  Plus,
  Trash2,
  Loader2,
} from "lucide-react";

export default function LegalEntityFormationScreen() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [entityType, setEntityType] = useState("");
  const [showEntityTypeInfo, setShowEntityTypeInfo] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    entityName: "",
    entityType: "",
    businessPurpose: "",
    province: "Western Cape",
    address: "",
    directors: [
      {
        name: "<PERSON>",
        email: "<EMAIL>",
        role: "Director",
        ownership: 33.33,
      },
    ],
    members: [],
    agreementAccepted: false,
    termsAccepted: false,
  });

  const entityTypes = [
    {
      id: "pty-ltd",
      name: "Private Company (Pty) Ltd",
      description:
        "A private company with limited liability. Ideal for small to medium-sized vehicle sharing groups with up to 50 shareholders.",
      advantages: [
        "Limited liability protection",
        "Separate legal entity",
        "Easier to raise capital than sole proprietorships",
        "Professional image and credibility",
      ],
      disadvantages: [
        "Annual CIPC filing requirements",
        "More formalities than sole proprietorships",
        "Requires a Memorandum of Incorporation",
        "Minimum one director required",
      ],
    },
    {
      id: "npc",
      name: "Non-Profit Company (NPC)",
      description:
        "A company formed for public benefit or social purposes. Suitable for community-based vehicle sharing with a social mission.",
      advantages: [
        "Limited liability protection",
        "Tax benefits (if registered as PBO)",
        "Can receive grants and donations",
        "No shareholders or dividends",
      ],
      disadvantages: [
        "Profits must be used for stated objectives",
        "More regulatory oversight",
        "Minimum three directors required",
        "Cannot distribute profits to members",
      ],
    },
    {
      id: "partnership",
      name: "Partnership",
      description:
        "A business arrangement between two or more individuals. Simple to form but offers less liability protection.",
      advantages: [
        "Easy and inexpensive to form",
        "No formal registration required",
        "Simple profit-sharing",
        "Minimal regulatory requirements",
      ],
      disadvantages: [
        "Unlimited personal liability",
        "Each partner is liable for the actions of other partners",
        "Difficult to raise capital",
        "Limited lifespan (dissolves if a partner leaves)",
      ],
    },
    {
      id: "cooperative",
      name: "Cooperative",
      description:
        "A business owned and democratically controlled by its members. Ideal for community-based vehicle sharing.",
      advantages: [
        "Democratic control (one member, one vote)",
        "Profits distributed based on use rather than investment",
        "Community-focused structure",
        "Tax benefits available",
      ],
      disadvantages: [
        "Decision-making can be slower",
        "Must register with CIPC and Department of Trade and Industry",
        "Minimum five members required",
        "Requires active member participation",
      ],
    },
  ];

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData({
      ...formData,
      [name]: checked,
    });
  };

  const handleDirectorChange = (
    index: number,
    field: string,
    value: string | number
  ) => {
    const updatedDirectors = [...formData.directors];
    updatedDirectors[index] = {
      ...updatedDirectors[index],
      [field]: field === "ownership" ? parseFloat(value as string) : value,
    };
    setFormData({
      ...formData,
      directors: updatedDirectors,
    });
  };

  const addDirector = () => {
    setFormData({
      ...formData,
      directors: [
        ...formData.directors,
        {
          name: "",
          email: "",
          role: "Director",
          ownership: 0,
        },
      ],
    });
  };

  const removeDirector = (index: number) => {
    const updatedDirectors = [...formData.directors];
    updatedDirectors.splice(index, 1);
    setFormData({
      ...formData,
      directors: updatedDirectors,
    });
  };

  const handleEntityTypeSelect = (type: string) => {
    setEntityType(type);
    setFormData({
      ...formData,
      entityType: type,
    });
  };

  const handleNextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    } else {
      // Submit form
      console.log("Form submitted:", formData);

      // Simulate processing
      setIsSubmitting(true);

      // Generate a random entity ID (1, 2, or 3) for demo purposes
      const entityId = Math.floor(Math.random() * 3) + 1;

      // Redirect to confirmation page after a short delay
      setTimeout(() => {
        router.push(`/entity-formation-confirmation/${entityId}`);
      }, 1500);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return !!formData.entityType;
      case 2:
        return (
          !!formData.entityName &&
          !!formData.businessPurpose &&
          !!formData.province &&
          !!formData.address
        );
      case 3:
        return (
          formData.directors.length > 0 &&
          formData.directors.every(
            (director) => !!director.name && !!director.email
          ) &&
          Math.abs(
            formData.directors.reduce(
              (sum, director) => sum + director.ownership,
              0
            ) - 100
          ) < 0.01
        );
      case 4:
        return formData.agreementAccepted && formData.termsAccepted;
      default:
        return false;
    }
  };

  const getTotalOwnership = () => {
    return formData.directors.reduce(
      (sum, director) => sum + director.ownership,
      0
    );
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Form Legal Entity</h1>
      </div>

      {/* Progress Steps */}
      <div className="bg-white px-6 py-4 border-b border-[#f2f2f2] shadow-sm">
        <div className="flex items-center justify-between">
          <div
            className={`flex flex-col items-center ${
              currentStep >= 1 ? "text-[#009639]" : "text-[#d6d9dd]"
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${
                currentStep >= 1
                  ? "bg-[#009639] text-white"
                  : "bg-[#f2f2f2] text-[#797879]"
              }`}
            >
              <Building size={16} />
            </div>
            <span className="text-xs">Entity Type</span>
          </div>
          <div
            className={`w-12 h-0.5 ${
              currentStep >= 2 ? "bg-[#009639]" : "bg-[#f2f2f2]"
            }`}
          ></div>
          <div
            className={`flex flex-col items-center ${
              currentStep >= 2 ? "text-[#009639]" : "text-[#d6d9dd]"
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${
                currentStep >= 2
                  ? "bg-[#009639] text-white"
                  : "bg-[#f2f2f2] text-[#797879]"
              }`}
            >
              <FileText size={16} />
            </div>
            <span className="text-xs">Details</span>
          </div>
          <div
            className={`w-12 h-0.5 ${
              currentStep >= 3 ? "bg-[#009639]" : "bg-[#f2f2f2]"
            }`}
          ></div>
          <div
            className={`flex flex-col items-center ${
              currentStep >= 3 ? "text-[#009639]" : "text-[#d6d9dd]"
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${
                currentStep >= 3
                  ? "bg-[#009639] text-white"
                  : "bg-[#f2f2f2] text-[#797879]"
              }`}
            >
              <Users size={16} />
            </div>
            <span className="text-xs">Ownership</span>
          </div>
          <div
            className={`w-12 h-0.5 ${
              currentStep >= 4 ? "bg-[#009639]" : "bg-[#f2f2f2]"
            }`}
          ></div>
          <div
            className={`flex flex-col items-center ${
              currentStep >= 4 ? "text-[#009639]" : "text-[#d6d9dd]"
            }`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${
                currentStep >= 4
                  ? "bg-[#009639] text-white"
                  : "bg-[#f2f2f2] text-[#797879]"
              }`}
            >
              <Check size={16} />
            </div>
            <span className="text-xs">Review</span>
          </div>
        </div>
      </div>

      {/* Step 1: Entity Type */}
      {currentStep === 1 && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-[#333333] font-medium">Select Entity Type</h3>
              <button
                className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center shadow-sm text-[#009639]"
                onClick={() => setShowEntityTypeInfo(!showEntityTypeInfo)}
              >
                <Info size={16} />
              </button>
            </div>

            {showEntityTypeInfo && (
              <div className="bg-[#e6ffe6] p-4 rounded-lg mb-4 text-sm border border-[#c9f0c9]">
                <p className="text-[#007A2F] font-medium mb-2">
                  The legal entity type you choose will affect:
                </p>
                <ul className="list-disc pl-5 text-[#333333] space-y-1.5">
                  <li>Liability protection for members</li>
                  <li>Tax implications</li>
                  <li>Ownership structure</li>
                  <li>Operational flexibility</li>
                  <li>Costs of formation and maintenance</li>
                </ul>
                <p className="text-[#007A2F] mt-3 text-xs font-medium">
                  We recommend consulting with a legal professional before
                  making a final decision.
                </p>
              </div>
            )}

            <div className="space-y-4">
              {entityTypes.map((type) => (
                <div
                  key={type.id}
                  className={`p-4 rounded-xl border shadow-sm ${
                    entityType === type.id
                      ? "border-[#009639] bg-[#e6ffe6]"
                      : "border-gray-100 bg-white"
                  }`}
                  onClick={() => handleEntityTypeSelect(type.id)}
                >
                  <div className="flex items-center justify-between">
                    <h4 className="text-[#333333] font-medium">{type.name}</h4>
                    <div
                      className={`w-6 h-6 rounded-full shadow-sm ${
                        entityType === type.id
                          ? "bg-[#009639]"
                          : "border border-[#d6d9dd] bg-white"
                      } flex items-center justify-center`}
                    >
                      {entityType === type.id && (
                        <Check size={14} className="text-white" />
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-[#797879] mt-2">
                    {type.description}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {entityType && (
            <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
              <h3 className="text-[#333333] font-medium mb-3">
                {entityTypes.find((type) => type.id === entityType)?.name}{" "}
                Details
              </h3>
              <div className="mb-4 p-3 bg-[#e6ffe6] rounded-lg">
                <h4 className="text-[#007A2F] text-sm font-medium mb-2">
                  Advantages
                </h4>
                <ul className="list-disc pl-5 text-sm text-[#333333] space-y-1.5">
                  {entityTypes
                    .find((type) => type.id === entityType)
                    ?.advantages.map((advantage, index) => (
                      <li key={index}>{advantage}</li>
                    ))}
                </ul>
              </div>
              <div className="p-3 bg-[#f9f9f9] rounded-lg">
                <h4 className="text-[#333333] text-sm font-medium mb-2">
                  Disadvantages
                </h4>
                <ul className="list-disc pl-5 text-sm text-[#797879] space-y-1.5">
                  {entityTypes
                    .find((type) => type.id === entityType)
                    ?.disadvantages.map((disadvantage, index) => (
                      <li key={index}>{disadvantage}</li>
                    ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Step 2: Entity Details */}
      {currentStep === 2 && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-4">Entity Details</h3>
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="entityName"
                  className="text-[#333333] text-sm font-medium mb-1 block"
                >
                  Entity Name
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Building size={18} className="text-[#009639]" />
                  </div>
                  <input
                    type="text"
                    id="entityName"
                    name="entityName"
                    value={formData.entityName}
                    onChange={handleInputChange}
                    placeholder="Enter entity name"
                    className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] focus:ring-1 focus:ring-[#009639]"
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="businessPurpose"
                  className="text-[#333333] text-sm font-medium mb-1 block"
                >
                  Business Purpose
                </label>
                <div className="relative">
                  <div className="absolute top-3 left-0 pl-3 flex items-start pointer-events-none">
                    <FileText size={18} className="text-[#009639]" />
                  </div>
                  <textarea
                    id="businessPurpose"
                    name="businessPurpose"
                    value={formData.businessPurpose}
                    onChange={handleInputChange}
                    placeholder="Describe the purpose of this entity"
                    className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] focus:ring-1 focus:ring-[#009639] min-h-[100px]"
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="province"
                  className="text-[#333333] text-sm font-medium mb-1 block"
                >
                  Province of Registration
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MapPin size={18} className="text-[#009639]" />
                  </div>
                  <select
                    id="province"
                    name="province"
                    value={formData.province}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] focus:ring-1 focus:ring-[#009639] appearance-none"
                  >
                    <option value="Western Cape">Western Cape</option>
                    <option value="Gauteng">Gauteng</option>
                    <option value="KwaZulu-Natal">KwaZulu-Natal</option>
                    <option value="Eastern Cape">Eastern Cape</option>
                    <option value="Free State">Free State</option>
                    <option value="Limpopo">Limpopo</option>
                    <option value="North West">North West</option>
                    <option value="Mpumalanga">Mpumalanga</option>
                    <option value="Northern Cape">Northern Cape</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <ChevronDown size={18} className="text-[#797879]" />
                  </div>
                </div>
              </div>

              <div>
                <label
                  htmlFor="address"
                  className="text-[#333333] text-sm font-medium mb-1 block"
                >
                  Business Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <MapPin size={18} className="text-[#009639]" />
                  </div>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    placeholder="Enter business address"
                    className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] focus:ring-1 focus:ring-[#009639]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Step 3: Ownership Structure */}
      {currentStep === 3 && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-[#333333] font-medium">
                Directors & Ownership
              </h3>
              <button
                className="bg-[#009639] text-white text-sm font-medium px-3 py-1.5 rounded-full shadow-sm flex items-center"
                onClick={addDirector}
              >
                <Plus size={14} className="mr-1" /> Add Director
              </button>
            </div>

            <div className="space-y-4">
              {formData.directors.map((director, index) => (
                <div
                  key={index}
                  className="p-4 border border-gray-100 rounded-xl shadow-sm bg-white"
                >
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-[#333333] font-medium">
                      Director {index + 1}
                    </h4>
                    {index > 0 && (
                      <button
                        className="text-red-500 text-sm font-medium px-3 py-1 rounded-full border border-red-200 shadow-sm flex items-center"
                        onClick={() => removeDirector(index)}
                      >
                        <Trash2 size={14} className="mr-1" /> Remove
                      </button>
                    )}
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label
                        htmlFor={`director-name-${index}`}
                        className="text-[#797879] text-xs mb-1 block"
                      >
                        Full Name
                      </label>
                      <input
                        type="text"
                        id={`director-name-${index}`}
                        value={director.name}
                        onChange={(e) =>
                          handleDirectorChange(index, "name", e.target.value)
                        }
                        placeholder="Enter full name"
                        className="ride-input w-full"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor={`director-email-${index}`}
                        className="text-[#797879] text-xs mb-1 block"
                      >
                        Email Address
                      </label>
                      <input
                        type="email"
                        id={`director-email-${index}`}
                        value={director.email}
                        onChange={(e) =>
                          handleDirectorChange(index, "email", e.target.value)
                        }
                        placeholder="Enter email address"
                        className="ride-input w-full"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor={`director-role-${index}`}
                        className="text-[#797879] text-xs mb-1 block"
                      >
                        Role
                      </label>
                      <select
                        id={`director-role-${index}`}
                        value={director.role}
                        onChange={(e) =>
                          handleDirectorChange(index, "role", e.target.value)
                        }
                        className="ride-input w-full"
                      >
                        <option value="Director">Director</option>
                        <option value="President">President</option>
                        <option value="Secretary">Secretary</option>
                        <option value="Treasurer">Treasurer</option>
                        <option value="Member">Member</option>
                      </select>
                    </div>

                    <div>
                      <label
                        htmlFor={`director-ownership-${index}`}
                        className="text-[#797879] text-xs mb-1 block"
                      >
                        Ownership Percentage
                      </label>
                      <input
                        type="number"
                        id={`director-ownership-${index}`}
                        value={director.ownership}
                        onChange={(e) =>
                          handleDirectorChange(
                            index,
                            "ownership",
                            e.target.value
                          )
                        }
                        min="0"
                        max="100"
                        step="0.01"
                        className="ride-input w-full"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 flex items-center justify-between">
              <span className="text-sm text-[#333333]">Total Ownership:</span>
              <span
                className={`text-sm font-medium ${
                  Math.abs(getTotalOwnership() - 100) < 0.01
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {getTotalOwnership().toFixed(2)}%
              </span>
            </div>

            {Math.abs(getTotalOwnership() - 100) >= 0.01 && (
              <div className="mt-2 p-3 bg-red-50 rounded-lg flex items-start">
                <AlertCircle
                  size={16}
                  className="text-red-500 mr-2 flex-shrink-0 mt-0.5"
                />
                <p className="text-xs text-red-700">
                  Total ownership must equal 100%. Please adjust the ownership
                  percentages.
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Step 4: Review & Agreement */}
      {currentStep === 4 && (
        <div className="p-4">
          <div className="ride-card p-4 mb-4">
            <h3 className="text-[#333333] font-medium mb-3">Review Details</h3>

            <div className="space-y-3">
              <div>
                <h4 className="text-[#797879] text-xs">Entity Type</h4>
                <p className="text-[#333333]">
                  {
                    entityTypes.find((type) => type.id === formData.entityType)
                      ?.name
                  }
                </p>
              </div>

              <div>
                <h4 className="text-[#797879] text-xs">Entity Name</h4>
                <p className="text-[#333333]">{formData.entityName}</p>
              </div>

              <div>
                <h4 className="text-[#797879] text-xs">Business Purpose</h4>
                <p className="text-[#333333]">{formData.businessPurpose}</p>
              </div>

              <div>
                <h4 className="text-[#797879] text-xs">
                  Province of Registration
                </h4>
                <p className="text-[#333333]">{formData.province}</p>
              </div>

              <div>
                <h4 className="text-[#797879] text-xs">Business Address</h4>
                <p className="text-[#333333]">{formData.address}</p>
              </div>

              <div>
                <h4 className="text-[#797879] text-xs mb-1">
                  Directors & Ownership
                </h4>
                <div className="space-y-2">
                  {formData.directors.map((director, index) => (
                    <div
                      key={index}
                      className="p-2 bg-[#f9fbff] rounded-lg flex items-center justify-between"
                    >
                      <div>
                        <p className="text-[#333333] text-sm">
                          {director.name}
                        </p>
                        <p className="text-[#797879] text-xs">
                          {director.role}
                        </p>
                      </div>
                      <p className="text-[#009639] font-medium">
                        {director.ownership}%
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">
              Terms & Agreements
            </h3>

            <div className="space-y-4">
              <div className="flex items-start">
                <input
                  type="checkbox"
                  id="agreementAccepted"
                  name="agreementAccepted"
                  checked={formData.agreementAccepted}
                  onChange={handleCheckboxChange}
                  className="mt-1 mr-2 h-4 w-4 rounded border-gray-300 text-[#009639] focus:ring-[#009639]"
                />
                <label
                  htmlFor="agreementAccepted"
                  className="text-sm text-[#333333]"
                >
                  I agree to the Operating Agreement and understand my rights
                  and responsibilities as a member of this entity.
                </label>
              </div>

              <div className="flex items-start">
                <input
                  type="checkbox"
                  id="termsAccepted"
                  name="termsAccepted"
                  checked={formData.termsAccepted}
                  onChange={handleCheckboxChange}
                  className="mt-1 mr-2 h-4 w-4 rounded border-gray-300 text-[#009639] focus:ring-[#009639]"
                />
                <label
                  htmlFor="termsAccepted"
                  className="text-sm text-[#333333]"
                >
                  I certify that all information provided is accurate and
                  complete. I understand that providing false information may
                  result in legal consequences.
                </label>
              </div>

              <div className="p-3 bg-[#e6ffe6] rounded-lg flex items-start">
                <AlertCircle
                  size={16}
                  className="text-[#009639] mr-2 flex-shrink-0 mt-0.5"
                />
                <p className="text-xs text-[#007A2F]">
                  Formation of a legal entity involves legal and tax
                  implications. We recommend consulting with a legal
                  professional before proceeding.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Buttons */}
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] shadow-lg flex space-x-3">
        {currentStep > 1 && (
          <button
            className="flex-1 py-3.5 border border-[#d6d9dd] rounded-full text-[#333333] font-medium shadow-sm"
            onClick={handlePrevStep}
          >
            Back
          </button>
        )}
        <button
          className={`flex-1 py-3.5 rounded-full font-medium shadow-md ${
            isStepValid()
              ? "bg-gradient-to-r from-[#009639] to-[#007A2F] text-white"
              : "bg-[#f2f2f2] text-[#797879]"
          }`}
          onClick={handleNextStep}
          disabled={!isStepValid() || isSubmitting}
        >
          {currentStep < 4 ? (
            "Next"
          ) : isSubmitting ? (
            <span className="flex items-center justify-center">
              <Loader2 size={18} className="animate-spin mr-2" /> Processing...
            </span>
          ) : (
            "Form Entity"
          )}
        </button>
      </div>
    </div>
  );
}
