"use client";

import { useState } from "react";
import {
  ArrowLeft,
  FileText,
  Upload,
  Download,
  Eye,
  CheckCircle,
  AlertCircle,
  Clock,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface LegalDocumentsSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function LegalDocumentsSheet({
  isOpen,
  onClose,
}: LegalDocumentsSheetProps) {
  // Mock data - in a real app, this would come from an API
  const [documents] = useState([
    {
      id: 1,
      name: "Driver's License",
      type: "ID Document",
      status: "verified",
      uploadDate: "2023-05-15",
      expiryDate: "2025-05-15",
      required: true,
    },
    {
      id: 2,
      name: "Vehicle Registration",
      type: "Vehicle Document",
      status: "verified",
      uploadDate: "2023-05-10",
      expiryDate: "2024-12-31",
      required: true,
    },
    {
      id: 3,
      name: "Insurance Certificate",
      type: "Insurance Document",
      status: "pending",
      uploadDate: "2023-05-20",
      expiryDate: "2024-05-20",
      required: true,
    },
    {
      id: 4,
      name: "Bank Statement",
      type: "Financial Document",
      status: "expired",
      uploadDate: "2023-02-15",
      expiryDate: "2023-05-15",
      required: false,
    },
  ]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "verified":
        return <CheckCircle size={18} className="text-green-500" />;
      case "pending":
        return <Clock size={18} className="text-yellow-500" />;
      case "expired":
        return <AlertCircle size={18} className="text-red-500" />;
      default:
        return <FileText size={18} className="text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "verified":
        return "Verified";
      case "pending":
        return "Pending Review";
      case "expired":
        return "Expired";
      default:
        return "Not Uploaded";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "verified":
        return "text-green-600 bg-green-50";
      case "pending":
        return "text-yellow-600 bg-yellow-50";
      case "expired":
        return "text-red-600 bg-red-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  const handleUpload = (documentId: number) => {
    console.log("Upload document:", documentId);
    // In a real app, you would handle file upload
  };

  const handleView = (documentId: number) => {
    console.log("View document:", documentId);
    // In a real app, you would open document viewer
  };

  const handleDownload = (documentId: number) => {
    console.log("Download document:", documentId);
    // In a real app, you would download the document
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Legal Documents
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Manage your legal documents
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Info Banner */}
            <div className="p-4">
              <div className="bg-green-50 rounded-xl p-4 border border-green-200">
                <h4 className="text-green-800 font-medium mb-2">
                  Document Requirements
                </h4>
                <p className="text-green-700 text-sm">
                  Keep your documents up to date to ensure uninterrupted access
                  to Poolly services.
                </p>
              </div>
            </div>

            {/* Documents List */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">
                Your Documents
              </h3>
              <div className="space-y-3">
                {documents.map((document) => (
                  <div
                    key={document.id}
                    className="bg-white rounded-xl shadow-md border border-gray-100 p-4"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                          <FileText size={18} className="text-[#009639]" />
                        </div>
                        <div>
                          <h4 className="text-[#333333] font-medium">
                            {document.name}
                            {document.required && (
                              <span className="text-red-500 ml-1">*</span>
                            )}
                          </h4>
                          <p className="text-xs text-[#797879]">
                            {document.type}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {getStatusIcon(document.status)}
                      </div>
                    </div>

                    <div className="flex items-center justify-between mb-3">
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${getStatusColor(
                          document.status
                        )}`}
                      >
                        {getStatusText(document.status)}
                      </span>
                      {document.expiryDate && (
                        <span className="text-xs text-[#797879]">
                          Expires:{" "}
                          {new Date(document.expiryDate).toLocaleDateString()}
                        </span>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      <button
                        className="flex-1 bg-[#009639] text-white py-2 px-3 rounded-full text-sm font-medium flex items-center justify-center"
                        onClick={() => handleUpload(document.id)}
                      >
                        <Upload size={14} className="mr-1" />
                        Upload
                      </button>
                      {document.status !== "expired" && (
                        <>
                          <button
                            className="bg-gray-100 text-gray-700 py-2 px-3 rounded-full text-sm flex items-center justify-center"
                            onClick={() => handleView(document.id)}
                          >
                            <Eye size={14} />
                          </button>
                          <button
                            className="bg-gray-100 text-gray-700 py-2 px-3 rounded-full text-sm flex items-center justify-center"
                            onClick={() => handleDownload(document.id)}
                          >
                            <Download size={14} />
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Upload Guidelines */}
            <div className="p-4">
              <div className="bg-gray-50 rounded-xl p-4 border border-gray-100">
                <h4 className="text-[#333333] font-medium mb-2">
                  Upload Guidelines
                </h4>
                <ul className="text-sm text-[#797879] space-y-1">
                  <li>• Ensure documents are clear and readable</li>
                  <li>• Accepted formats: PDF, JPG, PNG</li>
                  <li>• Maximum file size: 10MB</li>
                  <li>• Documents must be valid and not expired</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
