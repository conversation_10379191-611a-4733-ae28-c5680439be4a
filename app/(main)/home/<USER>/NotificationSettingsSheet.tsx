"use client";

import { useState } from "react";
import {
  ArrowLeft,
  Bell,
  Calendar,
  Car,
  Wallet,
  MessageSquare,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface NotificationSettingsSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function NotificationSettingsSheet({
  isOpen,
  onClose,
}: NotificationSettingsSheetProps) {
  // Mock data - in a real app, this would come from an API
  const [settings, setSettings] = useState({
    bookingReminders: true,
    paymentReminders: true,
    maintenanceAlerts: true,
    handoverReminders: true,
    groupMessages: true,
    systemUpdates: false,
    marketingEmails: false,
  });

  const handleToggle = (setting: keyof typeof settings) => {
    setSettings({
      ...settings,
      [setting]: !settings[setting],
    });
  };

  const handleSave = () => {
    console.log("Saving notification settings:", settings);
    // In a real app, you would make an API call here
    onClose();
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Notification Settings
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Manage your notification preferences
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* App Notifications */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">App Notifications</h3>
              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Calendar size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Booking Reminders
                      </h3>
                      <p className="text-xs text-[#797879]">
                        Notifications about upcoming bookings
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.bookingReminders}
                        onChange={() => handleToggle("bookingReminders")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>

                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Wallet size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Payment Reminders
                      </h3>
                      <p className="text-xs text-[#797879]">
                        Notifications about upcoming payments
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.paymentReminders}
                        onChange={() => handleToggle("paymentReminders")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>

                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Car size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Maintenance Alerts
                      </h3>
                      <p className="text-xs text-[#797879]">
                        Notifications about vehicle maintenance
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.maintenanceAlerts}
                        onChange={() => handleToggle("maintenanceAlerts")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>

                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Car size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Handover Reminders
                      </h3>
                      <p className="text-xs text-[#797879]">
                        Notifications about vehicle handovers
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.handoverReminders}
                        onChange={() => handleToggle("handoverReminders")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <MessageSquare size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">Group Messages</h3>
                      <p className="text-xs text-[#797879]">
                        Notifications about new messages in groups
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.groupMessages}
                        onChange={() => handleToggle("groupMessages")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Email Notifications */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">Email Notifications</h3>
              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Bell size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">System Updates</h3>
                      <p className="text-xs text-[#797879]">
                        Emails about system updates and new features
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.systemUpdates}
                        onChange={() => handleToggle("systemUpdates")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Bell size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">Marketing Emails</h3>
                      <p className="text-xs text-[#797879]">
                        Promotional emails and special offers
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.marketingEmails}
                        onChange={() => handleToggle("marketingEmails")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="border-t border-gray-200 p-4">
            <button
              className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white w-full py-4 rounded-full text-lg font-semibold shadow-md"
              onClick={handleSave}
            >
              Save Changes
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
