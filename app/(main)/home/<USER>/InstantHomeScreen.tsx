"use client";

import { useState, useEffect } from "react";
import { Suspense } from "react";
import StaticHomeLayout from "./StaticHomeLayout";
import DynamicContent from "./RecentActivity";
import VehicleDiscoveryDrawer from "./lease-application/VehicleDiscoveryDrawer";
import ApplicationProcessDrawer from "./lease-application/ApplicationProcessDrawer";
import ApplicationSubmittedDrawer from "./lease-application/ApplicationSubmittedDrawer";
import ProfileSheet from "./ProfileSheet";
import { useGlobalProfile } from "@/hooks/use-global-profile";
import { useCurrentUser } from "@/hooks/use-current-user";

// Instant loading home screen with background data loading
export default function InstantHomeScreen() {
  // Use the same pattern as PersonalInformationSheet for consistency
  const { profile, isLoading, error } = useGlobalProfile();
  const { userId: externalId, email } = useCurrentUser();

  // Extract data from profile (same as PersonalInformationSheet)
  const individual = profile?.individual || null;
  const profilePic = profile?.profilePic || null;
  const contactPoints = profile?.contactPoints || [];
  const contactPointTypes = profile?.contactPointTypes || [];
  const addressTypes = profile?.addressTypes || [];

  console.log(
    "🌍 FINAL STATE: Using global hooks, data:",
    individual?.first_name
  );

  // Log when profile changes (simplified)
  useEffect(() => {
    if (individual) {
      console.log(
        "🏡 Profile loaded:",
        individual.first_name,
        individual.last_name
      );
    }
  }, [individual]);

  // Drawer and sheet states
  const [showVehicleDrawer, setShowVehicleDrawer] = useState(false);
  const [showApplicationDrawer, setShowApplicationDrawer] = useState(false);
  const [showSubmittedDrawer, setShowSubmittedDrawer] = useState(false);
  const [showProfileSheet, setShowProfileSheet] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null);

  // SWR handles the data loading automatically in the background
  // The hook will revalidate on mount and handle caching

  // Handle profile sheet opening
  const handleProfileSheetOpen = () => {
    setShowProfileSheet(true);
  };

  // Handle vehicle drawer opening
  const handleVehicleDrawerOpen = () => {
    setShowVehicleDrawer(true);
  };

  return (
    <>
      {/* Static layout renders instantly - data loads in background */}
      <StaticHomeLayout
        profilePic={profilePic || null} // Consistent null fallback
        firstName={individual?.first_name || ""} // Consistent empty string fallback
        lastName={individual?.last_name || ""} // Consistent empty string fallback
        onProfileClick={handleProfileSheetOpen}
        onVehicleDrawerOpen={handleVehicleDrawerOpen}
      >
        {/* Dynamic content streams in */}
        <Suspense
          fallback={
            <div className="px-4 py-8 text-center text-gray-500">
              Loading recent activity...
            </div>
          }
        >
          <DynamicContent />
        </Suspense>
      </StaticHomeLayout>

      {/* Interactive Components */}
      <VehicleDiscoveryDrawer
        isOpen={showVehicleDrawer}
        onClose={() => setShowVehicleDrawer(false)}
        onNext={(vehicle) => {
          setSelectedVehicle(vehicle);
          setShowVehicleDrawer(false);
          setShowApplicationDrawer(true);
        }}
      />

      <ApplicationProcessDrawer
        isOpen={showApplicationDrawer}
        onClose={() => setShowApplicationDrawer(false)}
        onSubmit={() => {
          setShowApplicationDrawer(false);
          setShowSubmittedDrawer(true);
        }}
        selectedVehicle={selectedVehicle}
      />

      <ApplicationSubmittedDrawer
        isOpen={showSubmittedDrawer}
        onClose={() => setShowSubmittedDrawer(false)}
        selectedVehicle={selectedVehicle}
      />

      {/* Profile Sheet - uses the same cached data from SWR */}
      <ProfileSheet
        isOpen={showProfileSheet}
        onClose={() => setShowProfileSheet(false)}
        individual={individual || null}
        profilePic={profilePic || null}
        externalId={externalId || ""}
        email={email || ""}
        contactPoints={contactPoints || []}
        contactPointTypes={contactPointTypes || []}
        addressTypes={addressTypes || []}
      />
    </>
  );
}
