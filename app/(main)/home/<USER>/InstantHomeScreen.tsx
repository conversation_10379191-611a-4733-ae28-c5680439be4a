"use client";

import { useState, useEffect } from "react";
import { Suspense } from "react";
import StaticHomeLayout from "./StaticHomeLayout";
import DynamicContent from "./DynamicContent";
import VehicleDiscoveryDrawer from "./VehicleDiscoveryDrawer";
import ApplicationProcessDrawer from "./ApplicationProcessDrawer";
import ApplicationSubmittedDrawer from "./ApplicationSubmittedDrawer";
import ProfileSheet from "./ProfileSheet";
import { useProfile } from "@/hooks/use-global-profile";
import { useProfileData } from "@/hooks/use-profile-data";

// Instant loading home screen with background data loading
export default function InstantHomeScreen({
  dbId,
  externalId,
  email,
}: {
  dbId: number;
  externalId: string;
  email: string;
}) {
  // Fallback to old hook for now until we debug the new one
  const { data: profileData } = useProfileData(dbId);

  console.log(
    "🔍 DEBUG: Using old hook, data:",
    profileData?.individual?.first_name
  );

  // Log when profile changes (simplified)
  useEffect(() => {
    if (profileData?.individual) {
      console.log(
        "🏡 Profile loaded:",
        profileData.individual.first_name,
        profileData.individual.last_name
      );
    }
  }, [profileData]);

  // Drawer and sheet states
  const [showVehicleDrawer, setShowVehicleDrawer] = useState(false);
  const [showApplicationDrawer, setShowApplicationDrawer] = useState(false);
  const [showSubmittedDrawer, setShowSubmittedDrawer] = useState(false);
  const [showProfileSheet, setShowProfileSheet] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null);

  // SWR handles the data loading automatically in the background
  // The hook will revalidate on mount and handle caching

  // Handle profile sheet opening
  const handleProfileSheetOpen = () => {
    setShowProfileSheet(true);
  };

  // Handle vehicle drawer opening
  const handleVehicleDrawerOpen = () => {
    setShowVehicleDrawer(true);
  };

  return (
    <>
      {/* Static layout renders instantly - data loads in background */}
      <StaticHomeLayout
        profilePic={profileData?.profilePic || null} // Consistent null fallback
        firstName={profileData?.individual?.first_name || ""} // Consistent empty string fallback
        lastName={profileData?.individual?.last_name || ""} // Consistent empty string fallback
        onProfileClick={handleProfileSheetOpen}
        onVehicleDrawerOpen={handleVehicleDrawerOpen}
      >
        {/* Dynamic content streams in */}
        <Suspense
          fallback={
            <div className="px-4 py-8 text-center text-gray-500">
              Loading recent activity...
            </div>
          }
        >
          <DynamicContent />
        </Suspense>
      </StaticHomeLayout>

      {/* Interactive Components */}
      <VehicleDiscoveryDrawer
        isOpen={showVehicleDrawer}
        onClose={() => setShowVehicleDrawer(false)}
        onNext={(vehicle) => {
          setSelectedVehicle(vehicle);
          setShowVehicleDrawer(false);
          setShowApplicationDrawer(true);
        }}
      />

      <ApplicationProcessDrawer
        isOpen={showApplicationDrawer}
        onClose={() => setShowApplicationDrawer(false)}
        onSubmit={() => {
          setShowApplicationDrawer(false);
          setShowSubmittedDrawer(true);
        }}
        selectedVehicle={selectedVehicle}
      />

      <ApplicationSubmittedDrawer
        isOpen={showSubmittedDrawer}
        onClose={() => setShowSubmittedDrawer(false)}
        selectedVehicle={selectedVehicle}
      />

      {/* Profile Sheet - uses the same cached data from SWR */}
      <ProfileSheet
        isOpen={showProfileSheet}
        onClose={() => setShowProfileSheet(false)}
        individual={profileData?.individual || null}
        profilePic={profileData?.profilePic || null}
        externalId={externalId}
        email={email}
        contactPoints={profileData?.contactPoints || []}
        contactPointTypes={profileData?.contactPointTypes || []}
        addressTypes={profileData?.addressTypes || []}
      />
    </>
  );
}
