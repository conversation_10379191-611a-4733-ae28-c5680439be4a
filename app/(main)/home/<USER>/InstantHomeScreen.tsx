"use client";

import { useState, useEffect } from "react";
import { Suspense } from "react";
import StaticHomeLayout from "./StaticHomeLayout";
import DynamicContent from "./DynamicContent";
import VehicleDiscoveryDrawer from "./VehicleDiscoveryDrawer";
import ApplicationProcessDrawer from "./ApplicationProcessDrawer";
import ApplicationSubmittedDrawer from "./ApplicationSubmittedDrawer";
import ProfileSheet from "./ProfileSheet";
import { IndividualRead } from "@/types/individuals";
import {
  getIndividualData,
  getProfileImageData,
  getStaticFormData,
  getContactPoints,
} from "../actions";

// Instant loading home screen with background data loading
export default function InstantHomeScreen({
  dbId,
  externalId,
  email,
}: {
  dbId: number;
  externalId: string;
  email: string;
}) {
  // Profile data cache state
  const [profileDataCache, setProfileDataCache] = useState<{
    individual: IndividualRead | null;
    profilePic: string | null;
    contactPoints: any[];
    contactPointTypes: any[];
    addressTypes: any[];
    loaded: boolean;
    loading: boolean;
    error: string | null;
  }>({
    individual: null,
    profilePic: null,
    contactPoints: [],
    contactPointTypes: [],
    addressTypes: [],
    loaded: false,
    loading: false,
    error: null,
  });

  // Drawer and sheet states
  const [showVehicleDrawer, setShowVehicleDrawer] = useState(false);
  const [showApplicationDrawer, setShowApplicationDrawer] = useState(false);
  const [showSubmittedDrawer, setShowSubmittedDrawer] = useState(false);
  const [showProfileSheet, setShowProfileSheet] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null);

  // Background data loading - starts immediately but doesn't block render
  useEffect(() => {
    const loadProfileDataInBackground = async () => {
      if (profileDataCache.loading || profileDataCache.loaded) return;

      setProfileDataCache((prev) => ({ ...prev, loading: true, error: null }));

      try {
        // Load all profile data in parallel
        const [individual, profilePic] = await Promise.all([
          getIndividualData(dbId),
          getProfileImageData(dbId),
        ]);

        if (!individual) {
          throw new Error("Individual not found");
        }

        // Load additional data after we have the individual
        const [staticData, contactPoints] = await Promise.all([
          getStaticFormData(),
          getContactPoints(individual.party_id),
        ]);

        const { contactPointTypes, addressTypes } = staticData;

        // Cache all the data
        setProfileDataCache({
          individual,
          profilePic,
          contactPoints,
          contactPointTypes,
          addressTypes,
          loaded: true,
          loading: false,
          error: null,
        });

        console.log("✅ Profile data loaded in background");
      } catch (error) {
        console.error("❌ Failed to load profile data:", error);
        setProfileDataCache((prev) => ({
          ...prev,
          loading: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to load profile data",
        }));
      }
    };

    // Start loading immediately
    loadProfileDataInBackground();
  }, [dbId]);

  // Handle profile sheet opening
  const handleProfileSheetOpen = () => {
    setShowProfileSheet(true);

    if (!profileDataCache.loaded && !profileDataCache.loading) {
      // Trigger loading if not already started (fallback)
      console.log("🔄 Profile clicked before background loading completed");
    }
  };

  // Handle vehicle drawer opening
  const handleVehicleDrawerOpen = () => {
    setShowVehicleDrawer(true);
  };

  return (
    <>
      {/* Static layout renders instantly - no data dependencies */}
      <StaticHomeLayout
        profilePic={profileDataCache.profilePic} // null initially, updates when loaded
        firstName={profileDataCache.individual?.first_name}
        lastName={profileDataCache.individual?.last_name}
        onProfileClick={handleProfileSheetOpen}
        onVehicleDrawerOpen={handleVehicleDrawerOpen}
        profileDataReady={profileDataCache.loaded}
        profileDataLoading={profileDataCache.loading}
      >
        {/* Dynamic content streams in */}
        <Suspense
          fallback={
            <div className="px-4 py-8 text-center text-gray-500">
              Loading recent activity...
            </div>
          }
        >
          <DynamicContent />
        </Suspense>
      </StaticHomeLayout>

      {/* Interactive Components */}
      <VehicleDiscoveryDrawer
        isOpen={showVehicleDrawer}
        onClose={() => setShowVehicleDrawer(false)}
        onNext={(vehicle) => {
          setSelectedVehicle(vehicle);
          setShowVehicleDrawer(false);
          setShowApplicationDrawer(true);
        }}
      />

      <ApplicationProcessDrawer
        isOpen={showApplicationDrawer}
        onClose={() => setShowApplicationDrawer(false)}
        onSubmit={() => {
          setShowApplicationDrawer(false);
          setShowSubmittedDrawer(true);
        }}
        selectedVehicle={selectedVehicle}
      />

      <ApplicationSubmittedDrawer
        isOpen={showSubmittedDrawer}
        onClose={() => setShowSubmittedDrawer(false)}
        selectedVehicle={selectedVehicle}
      />

      {/* Profile Sheet - only render when data is available */}
      {profileDataCache.individual && (
        <ProfileSheet
          isOpen={showProfileSheet}
          onClose={() => setShowProfileSheet(false)}
          individual={profileDataCache.individual}
          profilePic={profileDataCache.profilePic}
          externalId={externalId}
          email={email}
          contactPoints={profileDataCache.contactPoints}
          contactPointTypes={profileDataCache.contactPointTypes}
          addressTypes={profileDataCache.addressTypes}
        />
      )}

      {/* Loading state for profile sheet when data not ready */}
      {showProfileSheet && !profileDataCache.individual && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
            <p className="text-[#333333]">
              {profileDataCache.loading
                ? "Loading profile..."
                : "Profile data unavailable"}
            </p>
            {profileDataCache.error && (
              <p className="text-red-500 text-sm mt-2">
                {profileDataCache.error}
              </p>
            )}
            <button
              onClick={() => setShowProfileSheet(false)}
              className="mt-4 px-4 py-2 bg-[#009639] text-white rounded-full text-sm"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </>
  );
}
