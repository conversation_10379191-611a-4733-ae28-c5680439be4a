"use client";

import { useState } from "react";
import {
  ArrowLeft,
  CreditCard,
  Plus,
  ChevronRight,
  CheckCircle,
  Trash2,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface PaymentMethodsSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function PaymentMethodsSheet({
  isOpen,
  onClose,
}: PaymentMethodsSheetProps) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedCardId, setSelectedCardId] = useState<number | null>(null);

  // Mock data - in a real app, this would come from an API
  const [paymentMethods, setPaymentMethods] = useState([
    {
      id: 1,
      type: "credit",
      cardNumber: "**** **** **** 4242",
      expiryDate: "05/25",
      cardHolder: "Thandi Dlamini",
      isDefault: true,
      brand: "Visa",
    },
    {
      id: 2,
      type: "credit",
      cardNumber: "**** **** **** 5555",
      expiryDate: "08/24",
      cardHolder: "Thandi Dlamini",
      isDefault: false,
      brand: "Mastercard",
    },
  ]);

  const handleSetDefault = (id: number) => {
    setPaymentMethods(
      paymentMethods.map((method) => ({
        ...method,
        isDefault: method.id === id,
      }))
    );
  };

  const handleDeleteCard = (id: number) => {
    setSelectedCardId(id);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = () => {
    if (selectedCardId) {
      setPaymentMethods(
        paymentMethods.filter((method) => method.id !== selectedCardId)
      );
    }
    setShowDeleteConfirm(false);
    setSelectedCardId(null);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setSelectedCardId(null);
  };

  const getCardLogo = (brand: string) => {
    switch (brand.toLowerCase()) {
      case "visa":
        return (
          <div className="w-10 h-6 bg-[#1434CB] rounded-md flex items-center justify-center text-white font-bold text-xs shadow-sm">
            VISA
          </div>
        );
      case "mastercard":
        return (
          <div className="w-10 h-6 bg-[#EB001B] rounded-md flex items-center justify-center text-white font-bold text-xs shadow-sm">
            MC
          </div>
        );
      default:
        return (
          <div className="w-10 h-6 bg-[#333333] rounded-md flex items-center justify-center text-white font-bold text-xs shadow-sm">
            CARD
          </div>
        );
    }
  };

  const handleAddCard = () => {
    console.log("Add new card clicked");
    // In a real app, you would navigate to add card form
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Payment Methods
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Manage your payment cards
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Payment Methods */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">Your Cards</h3>
              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                {paymentMethods.map((method) => (
                  <div
                    key={method.id}
                    className="p-4 border-b border-[#f2f2f2] last:border-b-0"
                  >
                    <div className="flex items-center">
                      <div className="mr-3">{getCardLogo(method.brand)}</div>
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h3 className="text-[#333333] font-medium">
                            {method.cardNumber}
                          </h3>
                          {method.isDefault && (
                            <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#009639]">
                              Default
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-[#797879]">
                          Expires {method.expiryDate}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        {!method.isDefault && (
                          <button
                            className="text-[#009639]"
                            onClick={() => handleSetDefault(method.id)}
                          >
                            <CheckCircle size={20} />
                          </button>
                        )}
                        <button
                          className="text-red-500 ml-2"
                          onClick={() => handleDeleteCard(method.id)}
                        >
                          <Trash2 size={20} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <button
                className="bg-white rounded-xl shadow-md w-full mt-4 p-4 flex items-center justify-center text-[#009639] border border-gray-100"
                onClick={handleAddCard}
              >
                <Plus size={20} className="mr-2" />
                <span className="font-medium">Add New Card</span>
              </button>
            </div>

            {/* Transaction History */}
            <div className="p-4">
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-[#333333] font-medium">
                  Recent Transactions
                </h3>
                <button className="text-[#009639] text-sm font-medium flex items-center">
                  View All <ChevronRight size={16} className="text-[#009639]" />
                </button>
              </div>

              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                      <CreditCard size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Monthly Contribution
                      </h3>
                      <p className="text-xs text-[#797879]">May 15, 2023</p>
                    </div>
                    <div className="text-[#333333] font-bold">R1,200.00</div>
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                      <CreditCard size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Vehicle Maintenance
                      </h3>
                      <p className="text-xs text-[#797879]">May 3, 2023</p>
                    </div>
                    <div className="text-[#333333] font-bold">R450.00</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 px-4">
            <div className="bg-white rounded-xl p-6 w-full max-w-sm shadow-lg border border-gray-100">
              <h3 className="text-[#333333] font-bold text-lg mb-2">
                Remove Card
              </h3>
              <p className="text-[#797879] mb-6">
                Are you sure you want to remove this card? This action cannot be
                undone.
              </p>
              <div className="flex space-x-3">
                <button
                  className="flex-1 py-3 px-4 border border-[#d6d9dd] rounded-full text-[#333333] font-medium shadow-sm"
                  onClick={cancelDelete}
                >
                  Cancel
                </button>
                <button
                  className="flex-1 py-3 px-4 bg-red-500 text-white rounded-full font-medium shadow-md"
                  onClick={confirmDelete}
                >
                  Remove
                </button>
              </div>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
