import { Suspense } from "react";
import { getIndividualData } from "../actions";
import { Skeleton } from "@/components/ui/skeleton";

// Fast-loading user profile component
async function UserProfile({ dbId }: { dbId: number }) {
  const individual = await getIndividualData(dbId);

  if (!individual) {
    return (
      <div className="bg-white px-6 py-4 shadow-sm">
        <div className="text-center text-gray-500">
          Unable to load profile information
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white px-6 py-4 shadow-sm">
      <div className="flex items-center space-x-4">
        <div className="w-12 h-12 rounded-full bg-[#009639] flex items-center justify-center">
          <span className="text-white font-semibold text-lg">
            {individual.first_name?.[0]}{individual.last_name?.[0]}
          </span>
        </div>
        <div>
          <h1 className="text-xl font-semibold text-gray-900">
            Welcome back, {individual.first_name}!
          </h1>
          <p className="text-sm text-gray-600">
            {individual.username || "User"}
          </p>
        </div>
      </div>
    </div>
  );
}

// Loading skeleton for user profile
function UserProfileSkeleton() {
  return (
    <div className="bg-white px-6 py-4 shadow-sm">
      <div className="flex items-center space-x-4">
        <Skeleton className="w-12 h-12 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-24" />
        </div>
      </div>
    </div>
  );
}

// Main component with Suspense
export default function UserProfileSection({ dbId }: { dbId: number }) {
  return (
    <Suspense fallback={<UserProfileSkeleton />}>
      <UserProfile dbId={dbId} />
    </Suspense>
  );
}
