"use server";

import { db } from "@/db";
import {
  individual,
  contactPoint,
  contactPointType,
  addressType,
  party,
} from "@/drizzle/schema";
import { eq } from "drizzle-orm";

/**
 * Profile Data Queries
 * Fast, optimized queries for fetching profile-related data
 */

// Fast query - Essential user data only (for immediate display)
export async function getIndividualData(dbId: number) {
  try {
    const individualData = await db
      .select({
        id: individual.id,
        party_id: individual.partyId,
        first_name: individual.firstName,
        last_name: individual.lastName,
        birth_date: individual.birthDate,
        middle_name: individual.middleName,
        created_at: individual.createdAt,
        username: party.externalId,
      })
      .from(individual)
      .innerJoin(party, eq(individual.partyId, party.id))
      .where(eq(individual.partyId, dbId))
      .limit(1);

    if (!individualData.length) {
      return null;
    }

    const rawIndividual = individualData[0];
    return {
      id: rawIndividual.id,
      party_id: rawIndividual.party_id,
      first_name: rawIndividual.first_name,
      last_name: rawIndividual.last_name,
      birth_date: rawIndividual.birth_date || "",
      middle_name: rawIndividual.middle_name,
      created_at: rawIndividual.created_at || "",
      username: rawIndividual.username || "",
      email: "", // Will be populated from contact points
    };
  } catch (error) {
    console.error("Error fetching individual data:", error);
    return null;
  }
}

// Contact points - Can load after initial render (streaming)
export async function getContactPoints(partyId: number) {
  try {
    const contactPoints = await db
      .select({
        id: contactPoint.id,
        contact_point_type_id: contactPoint.contactPointTypeId,
        value: contactPoint.value,
        is_primary: contactPoint.isPrimary,
        address_type_id: contactPoint.addressTypeId,
      })
      .from(contactPoint)
      .where(eq(contactPoint.partyId, partyId));

    return contactPoints;
  } catch (error) {
    console.error("Error fetching contact points:", error);
    return [];
  }
}

// Profile image data (placeholder for future implementation)
export async function getProfileImageData(_dbId: number) {
  try {
    // For now, return null since profile image logic is complex
    // We can implement this later using direct database queries
    // This avoids API calls and potential failures
    return null;
  } catch (error) {
    console.warn("Failed to fetch profile image:", error);
    return null;
  }
}
