"use client";

import { useState, useActionState } from "react";
import { useFormStatus } from "react-dom";
import Image from "next/image";
import { ArrowLeft, Camera, Mail, Phone, MapPin, Calendar } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { IndividualRead } from "@/types/individuals";
import { formatDateForInput } from "@/lib/utils";
import { updateProfile } from "@/actions/individuals";

// Submit button component with loading state
function SubmitButton() {
  const { pending } = useFormStatus();

  return (
    <button
      type="submit"
      disabled={pending}
      className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white w-full py-4 rounded-full text-lg font-semibold shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {pending ? (
        <div className="flex items-center justify-center">
          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
          Saving...
        </div>
      ) : (
        "Save Changes"
      )}
    </button>
  );
}

interface PersonalInformationSheetProps {
  isOpen: boolean;
  onClose: () => void;
  individual: IndividualRead;
  profilePic: string | null;
  userEmail: string;
  contactPoints?: any[];
  contactPointTypes?: any[];
  addressTypes?: any[];
}

export default function PersonalInformationSheet({
  isOpen,
  onClose,
  individual,
  profilePic,
  userEmail,
  contactPoints = [],
  contactPointTypes = [],
  addressTypes = [],
}: PersonalInformationSheetProps) {
  // Debug: Log the received data
  console.log("PersonalInformationSheet received data:", {
    contactPoints,
    contactPointTypes,
    addressTypes,
    individual,
  });

  // Extract contact points similar to the original page by Heartman
  const contactTypeMap = Object.fromEntries(
    contactPointTypes.map((c: any) => [c.name, c.id])
  );

  const emailId = contactTypeMap["email"];
  const phoneId = contactTypeMap["phone"];
  const addressId = contactTypeMap["address"];

  const firstEmail = contactPoints.find(
    (contact: any) => contact.contact_point_type_id === emailId
  );
  const firstPhone = contactPoints.find(
    (contact: any) => contact.contact_point_type_id === phoneId
  );
  const firstAddress = contactPoints.find(
    (contact: any) => contact.contact_point_type_id === addressId
  );

  // Real server action integration
  const [state, formAction] = useActionState(updateProfile, { errors: {} });

  // Form data state with real values from contact points
  const [formData, setFormData] = useState({
    firstName: individual.first_name,
    lastName: individual.last_name,
    email: firstEmail?.value || userEmail,
    phone: firstPhone?.value || "",
    address: firstAddress?.value || "",
    dateOfBirth: formatDateForInput(individual.birth_date) || "",
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Personal Information
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Update your personal details
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Profile Photo Section */}
            <div className="bg-white px-6 py-6 flex flex-col items-center">
              <div className="relative mb-2">
                <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-[#009639] shadow-md">
                  <Image
                    src={profilePic || "/images/Poolly.White.Svg.svg"}
                    alt="Profile"
                    width={96}
                    height={96}
                    className="object-cover"
                  />
                </div>
                <button className="absolute bottom-0 right-0 bg-gradient-to-r from-[#009639] to-[#007A2F] p-2 rounded-full shadow-md">
                  <Camera size={16} className="text-white" />
                </button>
              </div>
              <p className="text-[#009639] text-sm font-medium">Change Photo</p>
            </div>

            {/* Error Display */}
            {state?.errors && Object.keys(state.errors).length > 0 && (
              <div className="p-4">
                <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                  <h4 className="text-red-800 font-medium mb-2">
                    Please fix the following errors:
                  </h4>
                  <ul className="text-red-700 text-sm space-y-1">
                    {Object.entries(state.errors).map(([field, messages]) =>
                      Array.isArray(messages)
                        ? messages.map((msg, i) => (
                            <li key={`${field}-${i}`}>• {msg}</li>
                          ))
                        : null
                    )}
                  </ul>
                </div>
              </div>
            )}

            {/* Success Message */}
            {state?.success && (
              <div className="p-4">
                <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                  <h4 className="text-green-800 font-medium">
                    ✓ Personal information updated successfully!
                  </h4>
                </div>
              </div>
            )}

            {/* Form Fields */}
            <div className="p-4">
              <div className="bg-white rounded-xl shadow-md p-4 space-y-4 border border-gray-100">
                <div>
                  <label className="text-[#797879] text-xs mb-1 flex">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) =>
                      handleInputChange("firstName", e.target.value)
                    }
                    className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  />
                </div>

                <div>
                  <label className="text-[#797879] text-xs mb-1 flex">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) =>
                      handleInputChange("lastName", e.target.value)
                    }
                    className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  />
                </div>

                <div>
                  <label className="text-[#797879] text-xs mb-1 flex items-center">
                    <Mail size={14} className="mr-1 text-[#009639]" /> Email
                    Address
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  />
                </div>

                <div>
                  <label className="text-[#797879] text-xs mb-1 flex items-center">
                    <Phone size={14} className="mr-1 text-[#009639]" /> Phone
                    Number
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    placeholder="+27 11 123 4567"
                    className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  />
                </div>

                <div>
                  <label className="text-[#797879] text-xs mb-1 flex items-center">
                    <MapPin size={14} className="mr-1 text-[#009639]" /> Address
                  </label>
                  <input
                    type="text"
                    value={formData.address}
                    onChange={(e) =>
                      handleInputChange("address", e.target.value)
                    }
                    placeholder="123 Main Street, City, Province"
                    className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  />
                </div>

                <div>
                  <label className="text-[#797879] text-xs mb-1 flex items-center">
                    <Calendar size={14} className="mr-1 text-[#009639]" /> Date
                    of Birth
                  </label>
                  <input
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) =>
                      handleInputChange("dateOfBirth", e.target.value)
                    }
                    className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  />
                </div>
              </div>
            </div>

            {/* Additional Info */}
            <div className="p-4">
              <div className="bg-green-50 rounded-xl p-4 border border-green-200">
                <h4 className="text-green-800 font-medium mb-2">Information</h4>
                <p className="text-green-700 text-sm">
                  Your personal information is used to verify your identity and
                  improve your experience on Poolly.
                </p>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="border-t border-gray-200 p-4">
            <form action={formAction}>
              {/* Hidden inputs for form data */}
              <input
                type="hidden"
                name="firstName"
                value={formData.firstName}
              />
              <input type="hidden" name="lastName" value={formData.lastName} />
              <input type="hidden" name="email" value={formData.email} />
              <input type="hidden" name="phone" value={formData.phone} />
              <input type="hidden" name="address" value={formData.address} />
              <input
                type="hidden"
                name="dateOfBirth"
                value={formData.dateOfBirth}
              />

              {/* Hidden inputs for contact point data */}
              <input type="hidden" name="id" value={individual.id || ""} />
              <input
                type="hidden"
                name="emailId"
                value={firstEmail?.id || ""}
              />
              <input
                type="hidden"
                name="phoneId"
                value={firstPhone?.id || ""}
              />
              <input
                type="hidden"
                name="addressId"
                value={firstAddress?.id || ""}
              />
              <input
                type="hidden"
                name="contactPointEmailId"
                value={
                  firstEmail?.contact_point_type_id ||
                  contactTypeMap["email"] ||
                  ""
                }
              />
              <input
                type="hidden"
                name="contactPointPhoneId"
                value={
                  firstPhone?.contact_point_type_id ||
                  contactTypeMap["phone"] ||
                  ""
                }
              />
              <input
                type="hidden"
                name="contactPointAddressId"
                value={
                  firstAddress?.contact_point_type_id ||
                  contactTypeMap["address"] ||
                  ""
                }
              />
              <input
                type="hidden"
                name="addressTypeId"
                value={
                  firstAddress?.address_type_id ||
                  addressTypes.find((a: any) => a.name === "Main")?.id ||
                  ""
                }
              />

              <SubmitButton />
            </form>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
