"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Image from "next/image";
import { ArrowLeft, Camera, Mail, Phone, MapPin, Calendar } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { IndividualRead } from "@/types/individuals";
import { formatDateForInput, formatDateForHtmlInputDisplay } from "@/lib/utils";
import { updateProfileFromHome } from "../actions";
import { useGlobalProfile } from "@/hooks/use-global-profile";

// Zod schema for form validation
const personalInfoSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(8, "Phone number must be at least 8 digits"),
  address: z.string().min(1, "Address is required"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
});

type PersonalInfoFormData = z.infer<typeof personalInfoSchema>;

// Submit button component with loading state
function SubmitButton({
  isSubmitting,
  onClick,
}: {
  isSubmitting: boolean;
  onClick: () => void;
}) {
  return (
    <button
      type="button"
      disabled={isSubmitting}
      onClick={onClick}
      className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white w-full py-4 rounded-full text-lg font-semibold shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {isSubmitting ? (
        <div className="flex items-center justify-center">
          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
          Saving...
        </div>
      ) : (
        "Save Changes"
      )}
    </button>
  );
}

interface PersonalInformationSheetProps {
  isOpen: boolean;
  onClose: () => void;
  dbId: number;
  partyId: number;
  profilePic: string | null;
  userEmail: string;
}

// We now use the centralized useProfileData hook instead of a local fetcher function

export default function PersonalInformationSheet({
  isOpen,
  onClose,
  dbId,
  partyId,
  profilePic,
  userEmail,
}: PersonalInformationSheetProps) {
  // Use the global profile hook - single source of truth
  const {
    profile: profileData,
    error,
    isLoading,
    updateProfile: optimisticUpdate,
    refreshProfile,
    mutate,
  } = useGlobalProfile(partyId);

  // Extract data from profile data
  const individual = profileData?.individual || null;
  const contactPoints = profileData?.contactPoints || [];
  const contactPointTypes = profileData?.contactPointTypes || [];
  const addressTypes = profileData?.addressTypes || [];

  const contactTypeMap = Object.fromEntries(
    (contactPointTypes || []).map((c: any) => [c.name, c.id])
  );

  const emailId = contactTypeMap["email"];
  const phoneId = contactTypeMap["phone"];
  const addressId = contactTypeMap["address"];

  const firstEmail = (contactPoints || []).find(
    (contact: any) => contact.contact_point_type_id === emailId
  );
  const firstPhone = (contactPoints || []).find(
    (contact: any) => contact.contact_point_type_id === phoneId
  );
  const firstAddress = (contactPoints || []).find(
    (contact: any) => contact.contact_point_type_id === addressId
  );

  // React Hook Form setup
  const {
    register,
    handleSubmit: onSubmit,
    formState: { errors: formErrors, isSubmitting },
    reset,
  } = useForm<PersonalInfoFormData>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: userEmail,
      phone: "",
      address: "",
      dateOfBirth: "",
    },
  });

  // Update form when data loads
  useEffect(() => {
    if (individual && !isLoading) {
      reset({
        firstName: individual.first_name,
        lastName: individual.last_name,
        email: firstEmail?.value || userEmail,
        phone: firstPhone?.value || "",
        address: firstAddress?.value || "",
        dateOfBirth: formatDateForHtmlInputDisplay(individual.birth_date) || "",
      });
    }
  }, [
    individual,
    firstEmail,
    firstPhone,
    firstAddress,
    userEmail,
    reset,
    isLoading,
  ]);

  // Server response state
  const [submitState, setSubmitState] = useState<any>({ errors: {} });

  // Handle form submission
  const handleFormSubmit = async (data: PersonalInfoFormData) => {
    console.log("🚀 FORM SUBMISSION STARTED");
    console.log("📝 Form data:", data);
    console.log("👤 Current individual:", individual);
    console.log("💾 Current profileData:", profileData);

    if (!individual) {
      console.error("❌ Cannot submit form: individual data not loaded");
      return;
    }

    // Build FormData with validated form data
    const formDataToSubmit = new FormData();

    // Set form field values from validated data
    formDataToSubmit.set("firstName", data.firstName);
    formDataToSubmit.set("lastName", data.lastName);
    formDataToSubmit.set("email", data.email);
    formDataToSubmit.set("phone", data.phone);
    formDataToSubmit.set("address", data.address);
    formDataToSubmit.set("dateOfBirth", data.dateOfBirth);

    // Get default contact point type IDs
    const defaultContactPointEmailId = contactPointTypes?.find(
      (c) => c.name === "email"
    )?.id;
    const defaultPhoneId = contactPointTypes?.find(
      (c) => c.name === "phone"
    )?.id;
    const defaultAddressId = contactPointTypes?.find(
      (c) => c.name === "address"
    )?.id;
    const mainAddressId = (addressTypes || []).find(
      (c) => c.name === "Main"
    )?.id;

    // Set contact point data
    if (individual.id) formDataToSubmit.set("id", String(individual.id));
    if (firstEmail?.id) formDataToSubmit.set("emailId", String(firstEmail.id));
    if (firstPhone?.id) formDataToSubmit.set("phoneId", String(firstPhone.id));
    if (firstAddress?.id)
      formDataToSubmit.set("addressId", String(firstAddress.id));

    formDataToSubmit.set(
      "contactPointEmailId",
      String(firstEmail?.contact_point_type_id || defaultContactPointEmailId)
    );
    formDataToSubmit.set(
      "contactPointPhoneId",
      String(firstPhone?.contact_point_type_id || defaultPhoneId)
    );
    formDataToSubmit.set(
      "contactPointAddressId",
      String(firstAddress?.contact_point_type_id || defaultAddressId)
    );
    formDataToSubmit.set(
      "addressTypeId",
      String(firstAddress?.address_type_id || mainAddressId)
    );

    // Debug: Log all form data
    console.log("Form submission data:");
    for (const [key, value] of formDataToSubmit.entries()) {
      console.log(`${key}: ${value}`);
    }

    // Special debug for dateOfBirth
    console.log("Date of Birth Debug:");
    console.log("- Original individual.birth_date:", individual.birth_date);
    console.log(
      "- Formatted for display:",
      formatDateForInput(individual.birth_date)
    );
    console.log(
      "- Formatted for HTML input:",
      formatDateForHtmlInputDisplay(individual.birth_date)
    );
    console.log("- Form data dateOfBirth:", data.dateOfBirth);
    console.log("- FormData dateOfBirth:", formDataToSubmit.get("dateOfBirth"));

    // Optimistic update: Update the cache immediately with form data
    console.log("🔄 CREATING OPTIMISTIC DATA");
    console.log("📊 Original profileData:", profileData);
    console.log("🆔 Contact type IDs:", { emailId, phoneId, addressId });

    const optimisticData = {
      ...profileData,
      individual: {
        ...profileData?.individual!,
        first_name: data.firstName,
        last_name: data.lastName,
        birth_date: data.dateOfBirth,
        middle_name: profileData?.individual?.middle_name || null,
      },
      contactPoints: [
        // Update existing contact points or add new ones
        ...(profileData?.contactPoints || []).filter(
          (cp: any) =>
            cp.contact_point_type_id !== emailId &&
            cp.contact_point_type_id !== phoneId &&
            cp.contact_point_type_id !== addressId
        ),
        // Add updated contact points
        ...(data.email
          ? [
              {
                contact_point_type_id: emailId,
                value: data.email,
              },
            ]
          : []),
        ...(data.phone
          ? [
              {
                contact_point_type_id: phoneId,
                value: data.phone,
              },
            ]
          : []),
        ...(data.address
          ? [
              {
                contact_point_type_id: addressId,
                value: data.address,
              },
            ]
          : []),
      ],
    };

    console.log("✨ OPTIMISTIC DATA CREATED:");
    console.log("👤 New individual:", optimisticData.individual);
    console.log("📞 New contact points:", optimisticData.contactPoints);
    console.log("🔄 Applying optimistic update to cache...");

    // Apply optimistic update immediately using SWR's mutate with optimistic data
    const cacheKey = [`profile-data`, partyId];
    console.log("🔑 Cache key:", cacheKey);
    console.log("🔑 Current partyId:", partyId);
    console.log(
      "📊 Current profileData before optimistic update:",
      profileData
    );
    console.log("⏰ Starting SWR mutate with optimistic update...");

    try {
      // Step 1: Apply optimistic update immediately
      console.log("⚡ Applying optimistic update");
      await optimisticUpdate({
        first_name: data.firstName,
        last_name: data.lastName,
        birth_date: data.dateOfBirth,
        email: data.email,
      });

      // Step 2: Update database
      console.log("🏠 HOME ACTION: Starting database update");
      const result = await updateProfileFromHome(submitState, formDataToSubmit);

      console.log("📥 DATABASE RESPONSE:");
      console.log("✅ Result:", result);
      setSubmitState(result);

      if (result.success) {
        console.log("🎉 Profile updated successfully");

        // Step 3: Refresh cache to get latest data
        console.log("🔄 Refreshing cache with latest data");
        await mutate();

        console.log("✅ Cache refreshed - all components should update");
      } else {
        console.log("❌ Database update failed - reverting optimistic update");
        await mutate(); // Revert optimistic update
      }

      console.log("✅ Update process completed");
    } catch (error) {
      console.error("❌ ERROR in form submission:", error);
      setSubmitState({
        errors: { general: ["An error occurred while updating your profile."] },
      });

      // Revert optimistic update on error
      await mutate();
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Personal Information
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Update your personal details
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Profile Photo Section */}
            <div className="bg-white px-6 py-6 flex flex-col items-center">
              <div className="relative mb-2">
                <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-[#009639] shadow-md">
                  <Image
                    src={profilePic || "/images/Poolly.White.Svg.svg"}
                    alt="Profile"
                    width={96}
                    height={96}
                    className="object-cover"
                  />
                </div>
                <button className="absolute bottom-0 right-0 bg-gradient-to-r from-[#009639] to-[#007A2F] p-2 rounded-full shadow-md">
                  <Camera size={16} className="text-white" />
                </button>
              </div>
              <p className="text-[#009639] text-sm font-medium">Change Photo</p>
            </div>

            {/* Error Display */}
            {submitState?.errors &&
              Object.keys(submitState.errors).length > 0 && (
                <div className="p-4">
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <h4 className="text-red-800 font-medium mb-2">
                      Please fix the following errors:
                    </h4>
                    <ul className="text-red-700 text-sm space-y-1">
                      {Object.entries(submitState.errors).map(
                        ([field, messages]) =>
                          Array.isArray(messages)
                            ? messages.map((msg, i) => (
                                <li key={`${field}-${i}`}>• {msg}</li>
                              ))
                            : null
                      )}
                    </ul>
                  </div>
                </div>
              )}

            {/* Loading Error Display */}
            {error && (
              <div className="p-4">
                <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                  <h4 className="text-red-800 font-medium mb-2">
                    Failed to load personal information
                  </h4>
                  <p className="text-red-700 text-sm">
                    {error instanceof Error ? error.message : "Unknown error"}
                  </p>
                  <button
                    onClick={() => mutate()}
                    className="mt-2 text-sm text-red-600 underline"
                  >
                    Try again
                  </button>
                </div>
              </div>
            )}

            {/* Cache indicator */}
            {profileData && !isLoading && (
              <div className="px-4 pb-2">
                <div className="text-xs text-gray-500 text-center">
                  Data loaded from cache
                </div>
              </div>
            )}

            {/* Form Fields */}
            <div className="p-4">
              <div className="bg-white rounded-xl shadow-md p-4 space-y-4 border border-gray-100">
                {isLoading || dbId === 0 || partyId === 0 ? (
                  // Loading skeleton for form fields
                  <>
                    {[1, 2, 3, 4, 5, 6].map((index) => (
                      <div key={index}>
                        <div className="h-3 bg-gray-200 rounded animate-pulse w-20 mb-2"></div>
                        <div className="h-12 bg-gray-200 rounded-xl animate-pulse"></div>
                      </div>
                    ))}
                    {(dbId === 0 || partyId === 0) && (
                      <div className="text-center py-4">
                        <p className="text-gray-500 text-sm">
                          Waiting for profile data to load...
                        </p>
                      </div>
                    )}
                  </>
                ) : (
                  // Real form fields
                  <>
                    <div>
                      <label className="text-[#797879] text-xs mb-1 flex">
                        First Name
                      </label>
                      <input
                        type="text"
                        {...register("firstName")}
                        disabled={!individual}
                        className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm disabled:bg-gray-100"
                      />
                      {formErrors.firstName && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.firstName.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="text-[#797879] text-xs mb-1 flex">
                        Last Name
                      </label>
                      <input
                        type="text"
                        {...register("lastName")}
                        disabled={!individual}
                        className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm disabled:bg-gray-100"
                      />
                      {formErrors.lastName && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.lastName.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="text-[#797879] text-xs mb-1 flex items-center">
                        <Mail size={14} className="mr-1 text-[#009639]" /> Email
                        Address
                      </label>
                      <input
                        type="email"
                        {...register("email")}
                        disabled={!individual}
                        className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm disabled:bg-gray-100"
                      />
                      {formErrors.email && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.email.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="text-[#797879] text-xs mb-1 flex items-center">
                        <Phone size={14} className="mr-1 text-[#009639]" />{" "}
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        {...register("phone")}
                        placeholder="+27 11 123 4567"
                        disabled={!individual}
                        className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm disabled:bg-gray-100"
                      />
                      {formErrors.phone && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.phone.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="text-[#797879] text-xs mb-1 flex items-center">
                        <MapPin size={14} className="mr-1 text-[#009639]" />{" "}
                        Address
                      </label>
                      <input
                        type="text"
                        {...register("address")}
                        placeholder="123 Main Street, City, Province"
                        disabled={!individual}
                        className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm disabled:bg-gray-100"
                      />
                      {formErrors.address && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.address.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="text-[#797879] text-xs mb-1 flex items-center">
                        <Calendar size={14} className="mr-1 text-[#009639]" />{" "}
                        Date of Birth
                      </label>
                      <input
                        type="date"
                        {...register("dateOfBirth")}
                        disabled={!individual}
                        className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm disabled:bg-gray-100"
                      />
                      {formErrors.dateOfBirth && (
                        <p className="text-red-500 text-xs mt-1">
                          {formErrors.dateOfBirth.message}
                        </p>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Additional Info */}
            <div className="p-4">
              {/* Success Message */}
              {submitState?.success && (
                <div className="mb-4">
                  <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                    <h4 className="text-green-800 font-medium">
                      ✓ Personal information updated successfully!
                    </h4>
                  </div>
                </div>
              )}
              <div className="bg-[#ffd700]/10 rounded-xl p-4 border border-[#ffd700]/30">
                <h4 className="text-[#b8860b] font-medium mb-2">Information</h4>
                <p className="text-[#9a7209] text-sm">
                  Your personal information is used to verify your identity and
                  improve your experience on Poolly.
                </p>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="border-t border-gray-200 p-4">
            <SubmitButton
              isSubmitting={isSubmitting}
              onClick={onSubmit(handleFormSubmit)}
            />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
