import { Suspense } from "react";
import { getContactPoints, getStaticFormData } from "../actions";
import { Skeleton } from "@/components/ui/skeleton";

// Contact data that streams in after initial load
async function ContactData({ 
  partyId, 
  children 
}: { 
  partyId: number;
  children: (data: {
    contactPoints: any[];
    contactPointTypes: any[];
    addressTypes: any[];
  }) => React.ReactNode;
}) {
  // Load contact data and static form data in parallel
  const [contactPoints, staticData] = await Promise.all([
    getContactPoints(partyId),
    getStaticFormData(),
  ]);

  const { contactPointTypes, addressTypes } = staticData;

  return (
    <>
      {children({
        contactPoints,
        contactPointTypes,
        addressTypes,
      })}
    </>
  );
}

// Loading skeleton for contact-dependent components
function ContactDataSkeleton() {
  return (
    <div className="space-y-4 p-4">
      <div className="bg-white rounded-xl shadow-md p-4 space-y-3">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-10 w-full" />
      </div>
      <div className="bg-white rounded-xl shadow-md p-4 space-y-3">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-10 w-full" />
      </div>
      <div className="bg-white rounded-xl shadow-md p-4 space-y-3">
        <Skeleton className="h-4 w-28" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  );
}

// Main provider component with Suspense
export default function ContactDataProvider({ 
  partyId, 
  children 
}: { 
  partyId: number;
  children: (data: {
    contactPoints: any[];
    contactPointTypes: any[];
    addressTypes: any[];
  }) => React.ReactNode;
}) {
  return (
    <Suspense fallback={<ContactDataSkeleton />}>
      <ContactData partyId={partyId}>
        {children}
      </ContactData>
    </Suspense>
  );
}
