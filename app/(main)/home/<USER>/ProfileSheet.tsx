"use client";

import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  User,
  CreditCard,
  FileText,
  Bell,
  Settings,
  HelpCircle,
  Shield,
  Star,
  LogOut,
  ChevronRight,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { IndividualRead } from "@/types/individuals";

interface ProfileSheetProps {
  isOpen: boolean;
  onClose: () => void;
  individual: IndividualRead;
  profilePic: string | null;
  externalId: string;
}

export default function ProfileSheet({
  isOpen,
  onClose,
  individual,
  profilePic,
  externalId,
}: ProfileSheetProps) {
  const router = useRouter();

  const profileSections = [
    {
      id: "personal",
      title: "Personal Information",
      icon: <User size={20} className="text-[#009639]" />,
      route: "/profile/personal",
    },
    {
      id: "payment",
      title: "Payment Methods",
      icon: <CreditCard size={20} className="text-[#009639]" />,
      route: "/profile/payment",
    },
    {
      id: "documents",
      title: "Legal Documents",
      icon: <FileText size={20} className="text-[#009639]" />,
      route: `/profile/documents/${externalId}`,
    },
    {
      id: "notifications",
      title: "Notification Settings",
      icon: <Bell size={20} className="text-[#009639]" />,
      route: "/profile/notifications",
    },
    {
      id: "preferences",
      title: "App Preferences",
      icon: <Settings size={20} className="text-[#009639]" />,
      route: "/profile/preferences",
    },
  ];

  const supportSections = [
    {
      id: "help",
      title: "Help Center",
      icon: <HelpCircle size={20} className="text-[#009639]" />,
      route: "/help",
    },
    {
      id: "privacy",
      title: "Privacy & Security",
      icon: <Shield size={20} className="text-[#009639]" />,
      route: "/privacy",
    },
    {
      id: "feedback",
      title: "Give Feedback",
      icon: <Star size={20} className="text-[#009639]" />,
      route: "/feedback",
    },
  ];

  const handleNavigation = (route: string) => {
    onClose();
    router.push(route);
  };

  const handleSignOut = () => {
    // TODO: Implement sign out logic
    console.log("Sign out clicked");
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Profile
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Manage your account settings
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Profile Info Card */}
            <div className="p-4">
              <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-gray-200">
                    <Image
                      src={profilePic || "/placeholder.svg?height=64&width=64"}
                      alt="Profile"
                      width={64}
                      height={64}
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-[#333333]">
                      {individual.first_name} {individual.last_name}
                    </h3>
                    <p className="text-sm text-[#797879]">Member since 2024</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Account Settings */}
            <div className="px-4 pb-4">
              <h4 className="text-[#333333] font-semibold mb-3">
                Account Settings
              </h4>
              <div className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                {profileSections.map((section, index) => (
                  <button
                    key={section.id}
                    className={`w-full p-4 flex items-center hover:bg-gray-50 transition-colors ${
                      index < profileSections.length - 1
                        ? "border-b border-[#f2f2f2]"
                        : ""
                    }`}
                    onClick={() => handleNavigation(section.route)}
                  >
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      {section.icon}
                    </div>
                    <div className="flex-1 text-left">
                      <h3 className="text-[#333333] font-medium">
                        {section.title}
                      </h3>
                    </div>
                    <ChevronRight size={20} className="text-[#797879]" />
                  </button>
                ))}
              </div>
            </div>

            {/* Support */}
            <div className="px-4 pb-4">
              <h4 className="text-[#333333] font-semibold mb-3">Support</h4>
              <div className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                {supportSections.map((section, index) => (
                  <button
                    key={section.id}
                    className={`w-full p-4 flex items-center hover:bg-gray-50 transition-colors ${
                      index < supportSections.length - 1
                        ? "border-b border-[#f2f2f2]"
                        : ""
                    }`}
                    onClick={() => handleNavigation(section.route)}
                  >
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      {section.icon}
                    </div>
                    <div className="flex-1 text-left">
                      <h3 className="text-[#333333] font-medium">
                        {section.title}
                      </h3>
                    </div>
                    <ChevronRight size={20} className="text-[#797879]" />
                  </button>
                ))}
              </div>
            </div>

            {/* Sign Out */}
            <div className="px-4 pb-8">
              <button
                onClick={handleSignOut}
                className="w-full bg-red-50 border border-red-200 rounded-xl p-4 flex items-center hover:bg-red-100 transition-colors"
              >
                <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                  <LogOut size={20} className="text-red-600" />
                </div>
                <div className="flex-1 text-left">
                  <h3 className="text-red-600 font-medium">Sign Out</h3>
                </div>
                <ChevronRight size={20} className="text-red-400" />
              </button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
