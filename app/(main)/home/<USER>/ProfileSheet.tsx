"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  User,
  CreditCard,
  FileText,
  Bell,
  Settings,
  HelpCircle,
  Shield,
  Star,
  LogOut,
  ChevronRight,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { IndividualRead } from "@/types/individuals";
import NotificationSettingsSheet from "./NotificationSettingsSheet";
import AppPreferencesSheet from "./AppPreferencesSheet";
import HelpCenterSheet from "./HelpCenterSheet";
import PersonalInformationSheet from "./PersonalInformationSheet";
import PaymentMethodsSheet from "./PaymentMethodsSheet";
import LegalDocumentsSheet from "./LegalDocumentsSheet";
import PrivacySecuritySheet from "./PrivacySecuritySheet";
import GiveFeedbackSheet from "./GiveFeedbackSheet";

interface ProfileSheetProps {
  isOpen: boolean;
  onClose: () => void;
  individual: IndividualRead | null;
  profilePic: string | null;
  externalId: string;
  email: string;
  contactPoints?: any[];
  contactPointTypes?: any[];
  addressTypes?: any[];
}

export default function ProfileSheet({
  isOpen,
  onClose,
  individual,
  profilePic,
  externalId,
  email,
  contactPoints = [],
  contactPointTypes = [],
  addressTypes = [],
}: ProfileSheetProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("account");
  const [activeSubSheet, setActiveSubSheet] = useState<string | null>(null);

  const profileSections = [
    {
      id: "personal",
      title: "Personal Information",
      icon: <User size={20} className="text-[#009639]" />,
      route: "sheet", // Use sheet instead of route
    },
    {
      id: "payment",
      title: "Payment Methods",
      icon: <CreditCard size={20} className="text-[#009639]" />,
      route: "sheet", // Use sheet instead of route
    },
    {
      id: "documents",
      title: "Legal Documents",
      icon: <FileText size={20} className="text-[#009639]" />,
      route: "sheet", // Use sheet instead of route
    },
    {
      id: "notifications",
      title: "Notification Settings",
      icon: <Bell size={20} className="text-[#009639]" />,
      route: "sheet", // Use sheet instead of route
    },
    {
      id: "preferences",
      title: "App Preferences",
      icon: <Settings size={20} className="text-[#009639]" />,
      route: "sheet", // Use sheet instead of route
    },
  ];

  const supportSections = [
    {
      id: "help",
      title: "Help Center",
      icon: <HelpCircle size={20} className="text-[#009639]" />,
      route: "/help",
    },
    {
      id: "privacy",
      title: "Privacy & Security",
      icon: <Shield size={20} className="text-[#009639]" />,
      route: "/privacy",
    },
    {
      id: "feedback",
      title: "Give Feedback",
      icon: <Star size={20} className="text-[#009639]" />,
      route: "/feedback",
    },
  ];

  const handleNavigation = (route: string, sectionId: string) => {
    // For profile sections and support sections, open sub-sheets
    if (
      route === "sheet" ||
      route === "/help" ||
      route === "/privacy" ||
      route === "/feedback"
    ) {
      setActiveSubSheet(sectionId);
    } else {
      // For other routes, close profile and navigate
      onClose();
      router.push(route);
    }
  };

  const handleSignOut = () => {
    onClose();
    router.push("/logout");
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Profile
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Manage your account settings
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Profile Info Section - matching original */}
            <div className="bg-white px-6 py-6 flex items-center space-x-6 shadow-sm">
              <div className="w-20 h-20 rounded-full overflow-hidden border-2 border-gray-300">
                <Image
                  src={profilePic || "/images/Poolly.White.Svg.svg"}
                  alt="Profile"
                  width={80}
                  height={80}
                  className="object-cover"
                  priority
                />
              </div>
              <div className="space-y-1">
                <p className="text-lg font-medium text-gray-800">
                  {individual
                    ? `${individual.first_name} ${individual.last_name}`
                    : "Loading..."}
                </p>
                <p className="text-sm text-gray-600">{email}</p>
              </div>
            </div>

            {/* Tabs - matching original */}
            <div className="px-4 mt-4">
              <div className="flex border-b border-[#f2f2f2] mb-4">
                <button
                  className={`py-2 px-4 text-sm font-medium ${
                    activeTab === "account"
                      ? "text-[#009639] border-b-2 border-[#009639]"
                      : "text-[#797879]"
                  }`}
                  onClick={() => setActiveTab("account")}
                >
                  Account
                </button>
                <button
                  className={`py-2 px-4 text-sm font-medium ${
                    activeTab === "support"
                      ? "text-[#009639] border-b-2 border-[#009639]"
                      : "text-[#797879]"
                  }`}
                  onClick={() => setActiveTab("support")}
                >
                  Support
                </button>
              </div>
            </div>

            {/* Account Settings */}
            {activeTab === "account" && (
              <div className="px-4 pb-8">
                <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                  {profileSections.map((section, index) => (
                    <div
                      key={section.id}
                      className={`p-4 flex items-center ${
                        index < profileSections.length - 1
                          ? "border-b border-[#f2f2f2]"
                          : ""
                      }`}
                      onClick={() =>
                        handleNavigation(section.route, section.id)
                      }
                    >
                      <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                        {section.icon}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-[#333333] font-medium">
                          {section.title}
                        </h3>
                      </div>
                      <ChevronRight size={20} className="text-[#797879]" />
                    </div>
                  ))}
                </div>

                <button
                  className="bg-white rounded-xl shadow-md w-full mt-4 p-4 flex items-center text-red-500"
                  onClick={handleSignOut}
                >
                  <div className="flex-1">
                    <h3 className="text-red-500 font-medium">Log Out</h3>
                  </div>
                  <LogOut size={20} className="text-red-500 ml-2" />
                </button>
              </div>
            )}

            {/* Support Tab */}
            {activeTab === "support" && (
              <div className="px-4 pb-8">
                <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                  {supportSections.map((section, index) => (
                    <div
                      key={section.id}
                      className={`p-4 flex items-center ${
                        index < supportSections.length - 1
                          ? "border-b border-[#f2f2f2]"
                          : ""
                      }`}
                      onClick={() =>
                        handleNavigation(section.route, section.id)
                      }
                    >
                      <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                        {section.icon}
                      </div>
                      <div className="flex-1">
                        <h3 className="text-[#333333] font-medium">
                          {section.title}
                        </h3>
                      </div>
                      <ChevronRight size={20} className="text-[#797879]" />
                    </div>
                  ))}
                </div>

                <div className="bg-white rounded-xl shadow-md p-4 mt-4 border border-gray-100">
                  <div className="text-center">
                    <p className="text-[#797879] text-sm mb-1">App Version</p>
                    <p className="text-[#333333] font-medium">
                      1.0.0 (Build 42)
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </SheetContent>

      {/* Sub-sheets */}
      <NotificationSettingsSheet
        isOpen={activeSubSheet === "notifications"}
        onClose={() => setActiveSubSheet(null)}
      />
      <AppPreferencesSheet
        isOpen={activeSubSheet === "preferences"}
        onClose={() => setActiveSubSheet(null)}
      />
      <HelpCenterSheet
        isOpen={activeSubSheet === "help"}
        onClose={() => setActiveSubSheet(null)}
      />
      <PersonalInformationSheet
        isOpen={activeSubSheet === "personal"}
        onClose={() => setActiveSubSheet(null)}
      />
      <PaymentMethodsSheet
        isOpen={activeSubSheet === "payment"}
        onClose={() => setActiveSubSheet(null)}
      />
      <LegalDocumentsSheet
        isOpen={activeSubSheet === "documents"}
        onClose={() => setActiveSubSheet(null)}
      />
      <PrivacySecuritySheet
        isOpen={activeSubSheet === "privacy"}
        onClose={() => setActiveSubSheet(null)}
      />
      <GiveFeedbackSheet
        isOpen={activeSubSheet === "feedback"}
        onClose={() => setActiveSubSheet(null)}
      />
    </Sheet>
  );
}
