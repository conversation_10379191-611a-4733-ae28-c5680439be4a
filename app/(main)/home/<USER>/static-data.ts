"use server";

import { db } from "@/db";
import { contactPointType, addressType } from "@/drizzle/schema";
import { eq } from "drizzle-orm";

/**
 * Static Data Queries
 * Reference data that can be cached aggressively (loads once, reused)
 */

// Static data - Can be cached aggressively (loads once, reused)
export async function getStaticFormData() {
  try {
    const [contactPointTypes, addressTypes] = await Promise.all([
      db
        .select({
          id: contactPointType.id,
          name: contactPointType.name,
          description: contactPointType.description,
        })
        .from(contactPointType)
        .where(eq(contactPointType.isActive, true)),

      db
        .select({
          id: addressType.id,
          name: addressType.name,
          description: addressType.description,
        })
        .from(addressType)
        .where(eq(addressType.isActive, true)),
    ]);

    return {
      contactPointTypes,
      addressTypes,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching static form data:", error);
    return {
      contactPointTypes: [],
      addressTypes: [],
      success: false,
    };
  }
}
