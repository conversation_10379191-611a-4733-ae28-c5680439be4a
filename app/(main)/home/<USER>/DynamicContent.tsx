import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronRight } from "lucide-react";

// Recent Activity Component (streams in when data is available)
async function RecentActivity() {
  // Simulate data loading - replace with actual data fetching later
  await new Promise(resolve => setTimeout(resolve, 100));
  
  const recentActivities = [
    {
      id: 1,
      type: "booking",
      title: "You booked Toyota Hilux",
      time: "2 hours ago",
      image: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 2,
      type: "payment",
      title: "Monthly payment processed",
      time: "Yesterday",
      image: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 3,
      type: "group",
      title: "<PERSON> joined Family SUV group",
      time: "2 days ago",
      image: "/placeholder.svg?height=40&width=40",
    },
  ];

  return (
    <div className="px-4 mt-12">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-[#333333] font-semibold">Recent Activity</h3>
        <button className="text-[#009639] text-sm font-medium flex items-center">
          View All <ChevronRight size={14} />
        </button>
      </div>

      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-4">
        <div className="space-y-4">
          {recentActivities.map((activity) => (
            <div key={activity.id} className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-[#f2f2f2] flex items-center justify-center">
                <div className="w-6 h-6 bg-[#009639] rounded-full"></div>
              </div>
              <div className="flex-1">
                <p className="text-[#333333] text-sm font-medium">{activity.title}</p>
                <p className="text-[#797879] text-xs">{activity.time}</p>
              </div>
              <ChevronRight size={16} className="text-[#797879]" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Loading skeleton for Recent Activity
function RecentActivitySkeleton() {
  return (
    <div className="px-4 mt-12">
      <div className="flex justify-between items-center mb-4">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-4 w-16" />
      </div>

      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-4">
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center space-x-3">
              <Skeleton className="w-10 h-10 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-3 w-24" />
              </div>
              <Skeleton className="w-4 h-4" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Quick Stats Component (streams in when data is available)
async function QuickStats() {
  // Simulate data loading - replace with actual data fetching later
  await new Promise(resolve => setTimeout(resolve, 200));
  
  return (
    <div className="px-4 mt-8">
      <h3 className="text-[#333333] font-semibold mb-4">Quick Stats</h3>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-[#797879] text-xs">Total Bookings</p>
              <p className="text-[#333333] text-xl font-bold">12</p>
            </div>
            <div className="w-10 h-10 bg-[#009639] rounded-full flex items-center justify-center">
              <span className="text-white text-xs">📅</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-[#797879] text-xs">This Month</p>
              <p className="text-[#333333] text-xl font-bold">R2,400</p>
            </div>
            <div className="w-10 h-10 bg-[#FFD700] rounded-full flex items-center justify-center">
              <span className="text-[#333333] text-xs">💰</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Loading skeleton for Quick Stats
function QuickStatsSkeleton() {
  return (
    <div className="px-4 mt-8">
      <Skeleton className="h-6 w-24 mb-4" />
      
      <div className="grid grid-cols-2 gap-4">
        {[1, 2].map((i) => (
          <div key={i} className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-3 w-20" />
                <Skeleton className="h-6 w-12" />
              </div>
              <Skeleton className="w-10 h-10 rounded-full" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Main Dynamic Content Component
export default function DynamicContent() {
  return (
    <>
      {/* Quick Stats - Streams in */}
      <Suspense fallback={<QuickStatsSkeleton />}>
        <QuickStats />
      </Suspense>

      {/* Recent Activity - Streams in */}
      <Suspense fallback={<RecentActivitySkeleton />}>
        <RecentActivity />
      </Suspense>

      {/* Bottom spacing */}
      <div className="h-20"></div>
    </>
  );
}
