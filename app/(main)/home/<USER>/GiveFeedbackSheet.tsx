"use client";

import { useState } from "react";
import {
  ArrowLeft,
  Star,
  MessageSquare,
  Send,
  Camera,
  Paperclip,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface GiveFeedbackSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function GiveFeedbackSheet({
  isOpen,
  onClose,
}: GiveFeedbackSheetProps) {
  const [feedbackType, setFeedbackType] = useState("general");
  const [rating, setRating] = useState(0);
  const [feedback, setFeedback] = useState("");
  const [email, setEmail] = useState("");
  const [attachments, setAttachments] = useState<string[]>([]);

  const feedbackTypes = [
    { id: "general", label: "General Feedback", icon: "💬" },
    { id: "bug", label: "Report a Bug", icon: "🐛" },
    { id: "feature", label: "Feature Request", icon: "💡" },
    { id: "improvement", label: "Improvement Suggestion", icon: "⚡" },
  ];

  const handleRatingClick = (selectedRating: number) => {
    setRating(selectedRating);
  };

  const handleSubmit = () => {
    const feedbackData = {
      type: feedbackType,
      rating,
      feedback,
      email,
      attachments,
    };
    
    console.log("Feedback submitted:", feedbackData);
    
    // Reset form
    setFeedbackType("general");
    setRating(0);
    setFeedback("");
    setEmail("");
    setAttachments([]);
    
    // Show success message and close
    alert("Thank you for your feedback! We'll review it and get back to you soon.");
    onClose();
  };

  const handleAttachment = () => {
    console.log("Add attachment clicked");
    // In a real app, you would handle file selection
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Give Feedback
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Help us improve Poolly
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Welcome Message */}
            <div className="p-4">
              <div className="bg-gradient-to-r from-[#009639] to-[#007A2F] rounded-xl p-4 text-white mb-4">
                <h3 className="font-semibold mb-2">We value your feedback! 💚</h3>
                <p className="text-sm text-green-100">
                  Your input helps us make Poolly better for everyone. Share your thoughts, report issues, or suggest improvements.
                </p>
              </div>
            </div>

            {/* Feedback Type */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">What type of feedback?</h3>
              <div className="grid grid-cols-2 gap-3">
                {feedbackTypes.map((type) => (
                  <button
                    key={type.id}
                    className={`p-3 rounded-xl border-2 transition-colors ${
                      feedbackType === type.id
                        ? "border-[#009639] bg-[#e6ffe6]"
                        : "border-gray-200 bg-white"
                    }`}
                    onClick={() => setFeedbackType(type.id)}
                  >
                    <div className="text-2xl mb-1">{type.icon}</div>
                    <p className="text-sm font-medium text-[#333333]">{type.label}</p>
                  </button>
                ))}
              </div>
            </div>

            {/* Rating */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">How would you rate your experience?</h3>
              <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
                <div className="flex justify-center space-x-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      className="transition-colors"
                      onClick={() => handleRatingClick(star)}
                    >
                      <Star
                        size={32}
                        className={
                          star <= rating
                            ? "text-yellow-400 fill-yellow-400"
                            : "text-gray-300"
                        }
                      />
                    </button>
                  ))}
                </div>
                {rating > 0 && (
                  <p className="text-center text-sm text-[#797879] mt-2">
                    {rating === 1 && "Poor"}
                    {rating === 2 && "Fair"}
                    {rating === 3 && "Good"}
                    {rating === 4 && "Very Good"}
                    {rating === 5 && "Excellent"}
                  </p>
                )}
              </div>
            </div>

            {/* Feedback Text */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">Tell us more</h3>
              <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="Share your thoughts, suggestions, or describe any issues you've encountered..."
                  className="w-full h-32 resize-none border-none outline-none text-[#333333] placeholder-[#797879]"
                />
                <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
                  <div className="flex space-x-2">
                    <button
                      className="p-2 text-[#009639] hover:bg-[#e6ffe6] rounded-lg transition-colors"
                      onClick={handleAttachment}
                    >
                      <Camera size={18} />
                    </button>
                    <button
                      className="p-2 text-[#009639] hover:bg-[#e6ffe6] rounded-lg transition-colors"
                      onClick={handleAttachment}
                    >
                      <Paperclip size={18} />
                    </button>
                  </div>
                  <span className="text-xs text-[#797879]">
                    {feedback.length}/500
                  </span>
                </div>
              </div>
            </div>

            {/* Contact Email */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">Contact Email (Optional)</h3>
              <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full border-none outline-none text-[#333333] placeholder-[#797879]"
                />
              </div>
              <p className="text-xs text-[#797879] mt-2">
                We'll only use this to follow up on your feedback if needed.
              </p>
            </div>

            {/* Quick Feedback Options */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">Quick Feedback</h3>
              <div className="flex flex-wrap gap-2">
                {[
                  "App is too slow",
                  "Love the design",
                  "Need more features",
                  "Great user experience",
                  "Found a bug",
                  "Confusing navigation",
                ].map((quickFeedback) => (
                  <button
                    key={quickFeedback}
                    className="px-3 py-2 bg-gray-100 text-[#333333] text-sm rounded-full hover:bg-[#e6ffe6] hover:text-[#009639] transition-colors"
                    onClick={() => setFeedback(quickFeedback)}
                  >
                    {quickFeedback}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="border-t border-gray-200 p-4">
            <button
              className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white w-full py-4 rounded-full text-lg font-semibold shadow-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={handleSubmit}
              disabled={!feedback.trim()}
            >
              <Send size={18} className="mr-2" />
              Send Feedback
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
