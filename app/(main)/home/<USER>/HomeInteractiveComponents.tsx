"use client";

import { useState } from "react";
import VehicleDiscoveryDrawer from "./VehicleDiscoveryDrawer";
import ApplicationProcessDrawer from "./ApplicationProcessDrawer";
import ApplicationSubmittedDrawer from "./ApplicationSubmittedDrawer";
import ProfileSheet from "./ProfileSheet";
import { IndividualRead } from "@/types/individuals";
import { getStaticFormData, getContactPoints } from "../actions";

// This component handles all the interactive elements that need contact data
// It loads the data it needs and manages the drawer/sheet states
export default function HomeInteractiveComponents({
  individual,
  profilePic,
  externalId,
  email,
}: {
  individual: IndividualRead;
  profilePic: string | null;
  externalId: string;
  email: string;
}) {
  // State for drawers and sheets
  const [showVehicleDrawer, setShowVehicleDrawer] = useState(false);
  const [showApplicationDrawer, setShowApplicationDrawer] = useState(false);
  const [showSubmittedDrawer, setShowSubmittedDrawer] = useState(false);
  const [showProfileSheet, setShowProfileSheet] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null);

  // Contact data state - will be loaded when needed
  const [contactData, setContactData] = useState<{
    contactPoints: any[];
    contactPointTypes: any[];
    addressTypes: any[];
    loaded: boolean;
  }>({
    contactPoints: [],
    contactPointTypes: [],
    addressTypes: [],
    loaded: false,
  });

  // Load contact data when profile sheet is opened
  const handleProfileSheetOpen = async () => {
    setShowProfileSheet(true);

    if (!contactData.loaded) {
      try {
        const [staticData, contactPoints] = await Promise.all([
          getStaticFormData(),
          getContactPoints(individual.party_id),
        ]);

        setContactData({
          contactPoints,
          contactPointTypes: staticData.contactPointTypes,
          addressTypes: staticData.addressTypes,
          loaded: true,
        });
      } catch (error) {
        console.error("Failed to load contact data:", error);
        // Set empty data as fallback
        setContactData({
          contactPoints: [],
          contactPointTypes: [],
          addressTypes: [],
          loaded: true,
        });
      }
    }
  };

  // Load contact data when vehicle drawer is opened (for application process)
  const handleVehicleDrawerOpen = async () => {
    setShowVehicleDrawer(true);

    if (!contactData.loaded) {
      try {
        const [staticData, contactPoints] = await Promise.all([
          getStaticFormData(),
          getContactPoints(individual.party_id),
        ]);

        setContactData({
          contactPoints,
          contactPointTypes: staticData.contactPointTypes,
          addressTypes: staticData.addressTypes,
          loaded: true,
        });
      } catch (error) {
        console.error("Failed to load contact data:", error);
        setContactData({
          contactPoints: [],
          contactPointTypes: [],
          addressTypes: [],
          loaded: true,
        });
      }
    }
  };

  return (
    <>
      {/* Vehicle Discovery Drawer */}
      <VehicleDiscoveryDrawer
        isOpen={showVehicleDrawer}
        onClose={() => setShowVehicleDrawer(false)}
        onVehicleSelect={(vehicle) => {
          setSelectedVehicle(vehicle);
          setShowVehicleDrawer(false);
          setShowApplicationDrawer(true);
        }}
      />

      {/* Application Process Drawer */}
      <ApplicationProcessDrawer
        isOpen={showApplicationDrawer}
        onClose={() => setShowApplicationDrawer(false)}
        onSubmit={() => {
          setShowApplicationDrawer(false);
          setShowSubmittedDrawer(true);
        }}
        selectedVehicle={selectedVehicle}
        individual={individual}
        contactPoints={contactData.contactPoints}
        contactPointTypes={contactData.contactPointTypes}
        addressTypes={contactData.addressTypes}
      />

      {/* Application Submitted Drawer */}
      <ApplicationSubmittedDrawer
        isOpen={showSubmittedDrawer}
        onClose={() => setShowSubmittedDrawer(false)}
        selectedVehicle={selectedVehicle}
      />

      {/* Profile Sheet */}
      <ProfileSheet
        isOpen={showProfileSheet}
        onClose={() => setShowProfileSheet(false)}
        individual={individual}
        profilePic={profilePic}
        externalId={externalId}
        email={email}
        contactPoints={contactData.contactPoints}
        contactPointTypes={contactData.contactPointTypes}
        addressTypes={contactData.addressTypes}
      />

      {/* Expose handlers to parent component */}
      <div style={{ display: "none" }}>
        {/* This component will expose its handlers to the parent */}
      </div>
    </>
  );
}
