"use server";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import MissingParty from "@/components/missing-party";
import HomeScreen from "./home";
import { getHomePageData, getProfileImageData } from "./actions";

export default async function Home() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  // Use the new consolidated server action for reliable data fetching
  const [profilePic, homeData] = await Promise.all([
    getProfileImageData(+dbId),
    getHomePageData(+dbId),
  ]);

  const {
    individual,
    contactPointTypes,
    addressTypes,
    contactPoints,
    success,
  } = homeData;

  if (!individual || !success) return <MissingParty />;

  return (
    <HomeScreen
      individual={individual}
      profilePic={profilePic}
      externalId={externalId}
      email={email}
      contactPoints={contactPoints}
      contactPointTypes={contactPointTypes}
      addressTypes={addressTypes}
    />
  );
}
