"use server";

import { db } from "@/db";
import {
  individual,
  contactPoint,
  contactPointType,
  addressType,
  party,
} from "@/drizzle/schema";
import { eq } from "drizzle-orm";

// Direct database queries using Drizzle - NO API calls for now
// This completely bypasses API endpoints for maximum reliability for now

export async function getHomePageData(dbId: number) {
  try {
    // Query individual data directly from database
    const individualData = await db
      .select({
        id: individual.id,
        party_id: individual.partyId,
        first_name: individual.firstName,
        last_name: individual.lastName,
        birth_date: individual.birthDate,
        middle_name: individual.middleName,
        created_at: individual.createdAt,
        username: party.externalId, // Get username from party table
        email: party.externalId, // Temporary - we'll get real email from contact points
      })
      .from(individual)
      .innerJoin(party, eq(individual.partyId, party.id))
      .where(eq(individual.partyId, dbId))
      .limit(1);

    if (!individualData.length) {
      return {
        individual: null,
        contactPointTypes: [],
        addressTypes: [],
        contactPoints: [],
        success: false,
        error: "Individual not found",
      };
    }

    const individualRecord = individualData[0];

    // Query contact point types directly from database
    const contactPointTypes = await db
      .select({
        id: contactPointType.id,
        name: contactPointType.name,
        description: contactPointType.description,
      })
      .from(contactPointType)
      .where(eq(contactPointType.isActive, true));

    // Query address types directly from database
    const addressTypes = await db
      .select({
        id: addressType.id,
        name: addressType.name,
        description: addressType.description,
      })
      .from(addressType)
      .where(eq(addressType.isActive, true));

    // Query contact points for this individual directly from database
    const contactPoints = await db
      .select({
        id: contactPoint.id,
        contact_point_type_id: contactPoint.contactPointTypeId,
        value: contactPoint.value,
        is_primary: contactPoint.isPrimary,
        address_type_id: contactPoint.addressTypeId,
      })
      .from(contactPoint)
      .where(eq(contactPoint.partyId, individualRecord.party_id));

    return {
      individual: individualRecord,
      contactPointTypes,
      addressTypes,
      contactPoints,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching home page data from database:", error);
    return {
      individual: null,
      contactPointTypes: [],
      addressTypes: [],
      contactPoints: [],
      success: false,
      error: error instanceof Error ? error.message : "Database query failed",
    };
  }
}

export async function getProfileImageData(_dbId: number) {
  try {
    // For now, return null since profile image logic is complex
    // We can implement this later using direct database queries
    // This avoids API calls and potential failures
    return null;
  } catch (error) {
    console.warn("Failed to fetch profile image:", error);
    return null;
  }
}
