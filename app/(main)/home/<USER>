"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";

// Direct server actions for home page data fetching
// This bypasses the API endpoints to avoid inconsistent fetch issues

export async function getHomePageData(dbId: number) {
  try {
    const authorizedFetch = await getAuthorizedFetch();
    
    // Fetch all required data in parallel with proper error handling
    const [
      individualResponse,
      contactPointTypesResponse,
      addressTypesResponse,
    ] = await Promise.allSettled([
      authorizedFetch(`/api/individuals/party/${dbId}`, { method: "GET" }),
      authorizedFetch("/api/contact-point-types/", { method: "GET" }),
      authorizedFetch("/api/address-types/", { method: "GET" }),
    ]);

    // Parse individual data
    let individual = null;
    if (individualResponse.status === "fulfilled" && individualResponse.value.ok) {
      individual = await individualResponse.value.json();
    }

    // Parse contact point types
    let contactPointTypes = [];
    if (contactPointTypesResponse.status === "fulfilled" && contactPointTypesResponse.value.ok) {
      contactPointTypes = await contactPointTypesResponse.value.json();
    }

    // Parse address types
    let addressTypes = [];
    if (addressTypesResponse.status === "fulfilled" && addressTypesResponse.value.ok) {
      addressTypes = await addressTypesResponse.value.json();
    }

    // If we have an individual, fetch their contact points
    let contactPoints = [];
    if (individual?.party_id) {
      try {
        const contactPointsResponse = await authorizedFetch(
          `/api/contact-points/party/${individual.party_id}`,
          { method: "GET" }
        );
        if (contactPointsResponse.ok) {
          contactPoints = await contactPointsResponse.json();
        }
      } catch (error) {
        console.warn("Failed to fetch contact points:", error);
        // contactPoints remains empty array
      }
    }

    return {
      individual,
      contactPointTypes,
      addressTypes,
      contactPoints,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching home page data:", error);
    return {
      individual: null,
      contactPointTypes: [],
      addressTypes: [],
      contactPoints: [],
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

export async function getProfileImageData(dbId: number) {
  try {
    const authorizedFetch = await getAuthorizedFetch();
    const response = await authorizedFetch(`/api/profile-image/${dbId}`, {
      method: "GET",
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.url || null;
    }
    
    return null;
  } catch (error) {
    console.warn("Failed to fetch profile image:", error);
    return null;
  }
}
