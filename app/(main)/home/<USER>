"use server";
import { Suspense } from "react";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import MissingParty from "@/components/missing-party";
import HomeScreen from "./home";
import UserProfileSection from "./_components/UserProfileSection";
import { getIndividualData, getProfileImageData } from "./actions";

export default async function Home() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  // Fast initial load - only get essential user data
  const [individual, profilePic] = await Promise.all([
    getIndividualData(+dbId),
    getProfileImageData(+dbId),
  ]);

  if (!individual) return <MissingParty />;

  return (
    <>
      {/* Fast-loading user profile section */}
      <UserProfileSection dbId={+dbId} />

      {/* Main content with streaming data */}
      <Suspense
        fallback={
          <div className="p-4 text-center">Loading your dashboard...</div>
        }
      >
        <HomeScreen
          individual={individual}
          profilePic={profilePic}
          externalId={externalId}
          email={email}
          dbId={+dbId}
        />
      </Suspense>
    </>
  );
}
