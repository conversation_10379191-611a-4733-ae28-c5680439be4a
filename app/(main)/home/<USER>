"use server";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import MissingParty from "@/components/missing-party";
import HomeScreen from "./home";
import {
  getIndividualData,
  getProfileImageData,
  getStaticFormData,
  getContactPoints,
} from "./actions";

export default async function Home() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  // Optimized parallel loading - fast essential data first
  const [individual, profilePic] = await Promise.all([
    getIndividualData(+dbId),
    getProfileImageData(+dbId),
  ]);

  if (!individual) return <MissingParty />;

  // Load additional data in parallel after we have the individual
  const [staticData, contactPoints] = await Promise.all([
    getStaticFormData(),
    getContactPoints(individual.party_id),
  ]);

  const { contactPointTypes, addressTypes } = staticData;

  return (
    <HomeScreen
      individual={individual}
      profilePic={profilePic}
      externalId={externalId}
      email={email}
      contactPoints={contactPoints}
      contactPointTypes={contactPointTypes}
      addressTypes={addressTypes}
    />
  );
}
