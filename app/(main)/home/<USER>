"use server";

import { db } from "@/db";
import {
  individual,
  contactPoint,
  contactPointType,
  addressType,
  party,
} from "@/drizzle/schema";
import { eq } from "drizzle-orm";

// Optimized database queries with performance improvements
// Split into separate functions for better caching and streaming

// Fast query - Essential user data only (for immediate display)
export async function getIndividualData(dbId: number) {
  try {
    const individualData = await db
      .select({
        id: individual.id,
        party_id: individual.partyId,
        first_name: individual.firstName,
        last_name: individual.lastName,
        birth_date: individual.birthDate,
        middle_name: individual.middleName,
        created_at: individual.createdAt,
        username: party.externalId,
      })
      .from(individual)
      .innerJoin(party, eq(individual.partyId, party.id))
      .where(eq(individual.partyId, dbId))
      .limit(1);

    if (!individualData.length) {
      return null;
    }

    const rawIndividual = individualData[0];
    return {
      id: rawIndividual.id,
      party_id: rawIndividual.party_id,
      first_name: rawIndividual.first_name,
      last_name: rawIndividual.last_name,
      birth_date: rawIndividual.birth_date || "",
      middle_name: rawIndividual.middle_name,
      created_at: rawIndividual.created_at || "",
      username: rawIndividual.username || "",
      email: "", // Will be populated from contact points
    };
  } catch (error) {
    console.error("Error fetching individual data:", error);
    return null;
  }
}

// Static data - Can be cached aggressively (loads once, reused)
export async function getStaticFormData() {
  try {
    const [contactPointTypes, addressTypes] = await Promise.all([
      db
        .select({
          id: contactPointType.id,
          name: contactPointType.name,
          description: contactPointType.description,
        })
        .from(contactPointType)
        .where(eq(contactPointType.isActive, true)),

      db
        .select({
          id: addressType.id,
          name: addressType.name,
          description: addressType.description,
        })
        .from(addressType)
        .where(eq(addressType.isActive, true)),
    ]);

    return {
      contactPointTypes,
      addressTypes,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching static form data:", error);
    return {
      contactPointTypes: [],
      addressTypes: [],
      success: false,
    };
  }
}

// Contact points - Can load after initial render (streaming)
export async function getContactPoints(partyId: number) {
  try {
    const contactPoints = await db
      .select({
        id: contactPoint.id,
        contact_point_type_id: contactPoint.contactPointTypeId,
        value: contactPoint.value,
        is_primary: contactPoint.isPrimary,
        address_type_id: contactPoint.addressTypeId,
      })
      .from(contactPoint)
      .where(eq(contactPoint.partyId, partyId));

    return contactPoints;
  } catch (error) {
    console.error("Error fetching contact points:", error);
    return [];
  }
}

// Legacy function for backward compatibility - now optimized
export async function getHomePageData(dbId: number) {
  try {
    // Query individual data directly from database
    const individualData = await db
      .select({
        id: individual.id,
        party_id: individual.partyId,
        first_name: individual.firstName,
        last_name: individual.lastName,
        birth_date: individual.birthDate,
        middle_name: individual.middleName,
        created_at: individual.createdAt,
        username: party.externalId, // Get username from party table
        email: party.externalId, // Temporary - we'll get real email from contact points
      })
      .from(individual)
      .innerJoin(party, eq(individual.partyId, party.id))
      .where(eq(individual.partyId, dbId))
      .limit(1);

    if (!individualData.length) {
      return {
        individual: null,
        contactPointTypes: [],
        addressTypes: [],
        contactPoints: [],
        success: false,
        error: "Individual not found",
      };
    }

    const rawIndividual = individualData[0];

    // Transform to match IndividualRead type with proper defaults
    const individualRecord = {
      id: rawIndividual.id,
      party_id: rawIndividual.party_id,
      first_name: rawIndividual.first_name,
      last_name: rawIndividual.last_name,
      birth_date: rawIndividual.birth_date || "", // Default to empty string if null
      middle_name: rawIndividual.middle_name,
      created_at: rawIndividual.created_at || "", // Default to empty string if null
      username: rawIndividual.username || "", // Default to empty string if null
      email: rawIndividual.email || "", // Default to empty string if null
    };

    // Query contact point types directly from database
    const contactPointTypes = await db
      .select({
        id: contactPointType.id,
        name: contactPointType.name,
        description: contactPointType.description,
      })
      .from(contactPointType)
      .where(eq(contactPointType.isActive, true));

    // Query address types directly from database
    const addressTypes = await db
      .select({
        id: addressType.id,
        name: addressType.name,
        description: addressType.description,
      })
      .from(addressType)
      .where(eq(addressType.isActive, true));

    // Query contact points for this individual directly from database
    const contactPoints = await db
      .select({
        id: contactPoint.id,
        contact_point_type_id: contactPoint.contactPointTypeId,
        value: contactPoint.value,
        is_primary: contactPoint.isPrimary,
        address_type_id: contactPoint.addressTypeId,
      })
      .from(contactPoint)
      .where(eq(contactPoint.partyId, individualRecord.party_id));

    // Find the email contact point to get the real email
    const emailTypeId = contactPointTypes.find((ct) => ct.name === "email")?.id;
    const emailContact = contactPoints.find(
      (cp) => cp.contact_point_type_id === emailTypeId
    );

    // Update the individual record with the real email if found
    if (emailContact?.value) {
      individualRecord.email = emailContact.value;
    }

    return {
      individual: individualRecord,
      contactPointTypes,
      addressTypes,
      contactPoints,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching home page data from database:", error);
    return {
      individual: null,
      contactPointTypes: [],
      addressTypes: [],
      contactPoints: [],
      success: false,
      error: error instanceof Error ? error.message : "Database query failed",
    };
  }
}

export async function getProfileImageData(_dbId: number) {
  try {
    // For now, return null since profile image logic is complex
    // We can implement this later using direct database queries
    // This avoids API calls and potential failures
    return null;
  } catch (error) {
    console.warn("Failed to fetch profile image:", error);
    return null;
  }
}
