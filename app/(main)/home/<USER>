"use server";
import { getIndividualByPartyId } from "@/actions/individuals";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import MissingParty from "@/components/missing-party";
import HomeScreen from "./home";
import { getProfileImageUrl } from "@/lib/profile";

export default async function Home() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;
  const [profilePic, individual] = await Promise.all([
    getProfileImageUrl(+dbId),
    getIndividualByPartyId(+dbId),
  ]);

  return (
    <HomeScreen
      individual={individual}
      profilePic={profilePic}
      externalId={externalId}
      email={email}
    />
  );
}
