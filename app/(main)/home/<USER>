"use server";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import MissingParty from "@/components/missing-party";
import InstantHomeScreen from "./_components/InstantHomeScreen";

export default async function Home() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  // Only pass essential auth data - no database queries block the render
  return (
    <InstantHomeScreen dbId={+dbId} externalId={externalId} email={email} />
  );
}
