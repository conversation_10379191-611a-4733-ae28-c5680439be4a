"use server";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import MissingParty from "@/components/missing-party";
import InstantHomeScreen from "./_components/InstantHomeScreen";

export default async function Home() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  // No props needed - components get user data automatically via hooks
  return <InstantHomeScreen />;
}
