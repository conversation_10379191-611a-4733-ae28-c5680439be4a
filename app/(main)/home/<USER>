"use server";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import MissingParty from "@/components/missing-party";
import OptimizedHomeScreen from "./_components/OptimizedHomeScreen";
import { getIndividualData, getProfileImageData } from "./actions";

export default async function Home() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  // Only get the absolute minimum data needed for initial render
  const [individual, profilePic] = await Promise.all([
    getIndividualData(+dbId),
    getProfileImageData(+dbId),
  ]);

  if (!individual) return <MissingParty />;

  return (
    <OptimizedHomeScreen
      individual={individual}
      profilePic={profilePic}
      externalId={externalId}
      email={email}
    />
  );
}
