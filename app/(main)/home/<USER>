"use server";

import { db } from "@/db";
import {
  individual,
  contactPoint,
  contactPointType,
  addressType,
  party,
} from "@/drizzle/schema";
import { eq } from "drizzle-orm";

// Optimized database queries with performance improvements
// Split into separate functions for better caching and streaming

// Fast query - Essential user data only (for immediate display)
export async function getIndividualData(dbId: number) {
  try {
    const individualData = await db
      .select({
        id: individual.id,
        party_id: individual.partyId,
        first_name: individual.firstName,
        last_name: individual.lastName,
        birth_date: individual.birthDate,
        middle_name: individual.middleName,
        created_at: individual.createdAt,
        username: party.externalId,
      })
      .from(individual)
      .innerJoin(party, eq(individual.partyId, party.id))
      .where(eq(individual.partyId, dbId))
      .limit(1);

    if (!individualData.length) {
      return null;
    }

    const rawIndividual = individualData[0];
    return {
      id: rawIndividual.id,
      party_id: rawIndividual.party_id,
      first_name: rawIndividual.first_name,
      last_name: rawIndividual.last_name,
      birth_date: rawIndividual.birth_date || "",
      middle_name: rawIndividual.middle_name,
      created_at: rawIndividual.created_at || "",
      username: rawIndividual.username || "",
      email: "", // Will be populated from contact points
    };
  } catch (error) {
    console.error("Error fetching individual data:", error);
    return null;
  }
}

// Static data - Can be cached aggressively (loads once, reused)
export async function getStaticFormData() {
  try {
    const [contactPointTypes, addressTypes] = await Promise.all([
      db
        .select({
          id: contactPointType.id,
          name: contactPointType.name,
          description: contactPointType.description,
        })
        .from(contactPointType)
        .where(eq(contactPointType.isActive, true)),

      db
        .select({
          id: addressType.id,
          name: addressType.name,
          description: addressType.description,
        })
        .from(addressType)
        .where(eq(addressType.isActive, true)),
    ]);

    return {
      contactPointTypes,
      addressTypes,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching static form data:", error);
    return {
      contactPointTypes: [],
      addressTypes: [],
      success: false,
    };
  }
}

// Contact points - Can load after initial render (streaming)
export async function getContactPoints(partyId: number) {
  try {
    const contactPoints = await db
      .select({
        id: contactPoint.id,
        contact_point_type_id: contactPoint.contactPointTypeId,
        value: contactPoint.value,
        is_primary: contactPoint.isPrimary,
        address_type_id: contactPoint.addressTypeId,
      })
      .from(contactPoint)
      .where(eq(contactPoint.partyId, partyId));

    return contactPoints;
  } catch (error) {
    console.error("Error fetching contact points:", error);
    return [];
  }
}

// Legacy function for backward compatibility - now optimized a bit more
// export async function getHomePageData(dbId: number) {
//   try {
//     // Query individual data directly from database
//     const individualData = await db
//       .select({
//         id: individual.id,
//         party_id: individual.partyId,
//         first_name: individual.firstName,
//         last_name: individual.lastName,
//         birth_date: individual.birthDate,
//         middle_name: individual.middleName,
//         created_at: individual.createdAt,
//         username: party.externalId, // Get username from party table
//         email: party.externalId, // Temporary - we'll get real email from contact points
//       })
//       .from(individual)
//       .innerJoin(party, eq(individual.partyId, party.id))
//       .where(eq(individual.partyId, dbId))
//       .limit(1);

//     if (!individualData.length) {
//       return {
//         individual: null,
//         contactPointTypes: [],
//         addressTypes: [],
//         contactPoints: [],
//         success: false,
//         error: "Individual not found",
//       };
//     }

//     const rawIndividual = individualData[0];

//     // Transform to match IndividualRead type with proper defaults
//     const individualRecord = {
//       id: rawIndividual.id,
//       party_id: rawIndividual.party_id,
//       first_name: rawIndividual.first_name,
//       last_name: rawIndividual.last_name,
//       birth_date: rawIndividual.birth_date || "", // Default to empty string if null
//       middle_name: rawIndividual.middle_name,
//       created_at: rawIndividual.created_at || "", // Default to empty string if null
//       username: rawIndividual.username || "", // Default to empty string if null
//       email: rawIndividual.email || "", // Default to empty string if null
//     };

//     // Query contact point types directly from database
//     const contactPointTypes = await db
//       .select({
//         id: contactPointType.id,
//         name: contactPointType.name,
//         description: contactPointType.description,
//       })
//       .from(contactPointType)
//       .where(eq(contactPointType.isActive, true));

//     // Query address types directly from database
//     const addressTypes = await db
//       .select({
//         id: addressType.id,
//         name: addressType.name,
//         description: addressType.description,
//       })
//       .from(addressType)
//       .where(eq(addressType.isActive, true));

//     // Query contact points for this individual directly from database
//     const contactPoints = await db
//       .select({
//         id: contactPoint.id,
//         contact_point_type_id: contactPoint.contactPointTypeId,
//         value: contactPoint.value,
//         is_primary: contactPoint.isPrimary,
//         address_type_id: contactPoint.addressTypeId,
//       })
//       .from(contactPoint)
//       .where(eq(contactPoint.partyId, individualRecord.party_id));

//     // Find the email contact point to get the real email
//     const emailTypeId = contactPointTypes.find((ct) => ct.name === "email")?.id;
//     const emailContact = contactPoints.find(
//       (cp) => cp.contact_point_type_id === emailTypeId
//     );

//     // Update the individual record with the real email if found
//     if (emailContact?.value) {
//       individualRecord.email = emailContact.value;
//     }

//     return {
//       individual: individualRecord,
//       contactPointTypes,
//       addressTypes,
//       contactPoints,
//       success: true,
//     };
//   } catch (error) {
//     console.error("Error fetching home page data from database:", error);
//     return {
//       individual: null,
//       contactPointTypes: [],
//       addressTypes: [],
//       contactPoints: [],
//       success: false,
//       error: error instanceof Error ? error.message : "Database query failed",
//     };
//   }
// }

export async function getProfileImageData(_dbId: number) {
  try {
    // For now, return null since profile image logic is complex
    // We can implement this later using direct database queries
    // This avoids API calls and potential failures
    return null;
  } catch (error) {
    console.warn("Failed to fetch profile image:", error);
    return null;
  }
}

// Fast profile update for home route - direct database, no revalidatePath
export async function updateProfileFromHome(_: any, formData: FormData) {
  console.log("🏠 HOME ACTION: updateProfileFromHome started");

  try {
    // Extract and validate form data
    const firstName = (formData.get("firstName") as string)?.trim();
    const lastName = (formData.get("lastName") as string)?.trim();
    const email = (formData.get("email") as string)?.trim();
    const phone = (formData.get("phone") as string)?.trim();
    const address = (formData.get("address") as string)?.trim();
    const dateOfBirth = (formData.get("dateOfBirth") as string)?.trim();

    const individualId = Number(formData.get("id"));
    const emailId = Number(formData.get("emailId"));
    const phoneId = Number(formData.get("phoneId"));
    const addressId = Number(formData.get("addressId"));
    const addressTypeId = Number(formData.get("addressTypeId"));

    // Basic validation
    if (!firstName || !lastName || !email || !individualId) {
      return {
        errors: {
          firstName: !firstName ? ["First name is required"] : [],
          lastName: !lastName ? ["Last name is required"] : [],
          email: !email ? ["Email is required"] : [],
          general: !individualId ? ["Invalid profile ID"] : [],
        },
      };
    }

    console.log("📝 Updating individual record...");

    // Update individual record first
    await db
      .update(individual)
      .set({
        firstName: firstName,
        lastName: lastName,
        birthDate: dateOfBirth || null,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(individual.id, individualId));

    console.log("📞 Updating contact points in parallel...");

    // Update contact points in parallel for speed
    const updatePromises = [];

    if (email && emailId) {
      updatePromises.push(
        db
          .update(contactPoint)
          .set({
            value: email,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(contactPoint.id, emailId))
      );
    }

    if (phone && phoneId) {
      updatePromises.push(
        db
          .update(contactPoint)
          .set({
            value: phone,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(contactPoint.id, phoneId))
      );
    }

    if (address && addressId) {
      updatePromises.push(
        db
          .update(contactPoint)
          .set({
            value: address,
            addressTypeId: addressTypeId || null,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(contactPoint.id, addressId))
      );
    }

    // Execute all contact point updates in parallel
    if (updatePromises.length > 0) {
      await Promise.all(updatePromises);
    }

    console.log("✅ Profile updated successfully via direct database");

    // No revalidatePath - let SWR handle cache invalidation
    return { success: true };
  } catch (error: any) {
    console.error("❌ Database update failed:", error);
    return {
      errors: {
        general: [
          error?.message || "Could not update profile. Please try again later.",
        ],
      },
    };
  }
}

// Direct database update function - bypasses API for better reliability
export async function updateProfileDirect(_: any, formData: FormData) {
  console.log("🔧 DIRECT DB UPDATE: updateProfileDirect started");
  console.log("📥 Received FormData entries:");
  for (const [key, value] of formData.entries()) {
    console.log(`  ${key}: ${value}`);
  }

  try {
    // Extract form data
    const dateOfBirth = (formData.get("dateOfBirth") as string)?.trim();
    const address = (formData.get("address") as string)?.trim();
    const email = (formData.get("email") as string)?.trim();
    const phone = (formData.get("phone") as string)?.trim();
    const firstName = (formData.get("firstName") as string)?.trim();
    const lastName = (formData.get("lastName") as string)?.trim();

    console.log("📝 Extracted form values:");
    console.log("  firstName:", firstName);
    console.log("  lastName:", lastName);
    console.log("  email:", email);
    console.log("  phone:", phone);
    console.log("  address:", address);
    console.log("  dateOfBirth:", dateOfBirth);

    // Extract IDs
    const emailId = Number(formData.get("emailId"));
    const phoneId = Number(formData.get("phoneId"));
    const addressId = Number(formData.get("addressId"));
    const addressTypeId = Number(formData.get("addressTypeId"));
    const contactPointEmailId = Number(formData.get("contactPointEmailId"));
    const contactPointPhoneId = Number(formData.get("contactPointPhoneId"));
    const contactPointAddressId = Number(formData.get("contactPointAddressId"));
    const individualId = Number(formData.get("id"));

    console.log("🆔 Extracted IDs:");
    console.log("  individualId:", individualId);
    console.log(
      "  emailId:",
      emailId,
      "contactPointEmailId:",
      contactPointEmailId
    );
    console.log(
      "  phoneId:",
      phoneId,
      "contactPointPhoneId:",
      contactPointPhoneId
    );
    console.log(
      "  addressId:",
      addressId,
      "contactPointAddressId:",
      contactPointAddressId
    );

    // Basic validation
    if (!firstName || !lastName || !email) {
      console.log("❌ Validation failed: Missing required fields");
      return {
        errors: {
          firstName: !firstName ? ["First name is required"] : [],
          lastName: !lastName ? ["Last name is required"] : [],
          email: !email ? ["Email is required"] : [],
        },
      };
    }

    console.log("✅ Basic validation passed");

    // Use simple queries instead of transactions for better performance
    console.log("🔄 Starting direct database updates...");

    try {
      // Update individual record
      console.log("📝 Updating individual record...");
      await db
        .update(individual)
        .set({
          firstName: firstName,
          lastName: lastName,
          birthDate: dateOfBirth || null,
          updatedAt: new Date().toISOString(),
        })
        .where(eq(individual.id, individualId));
      console.log("✅ Individual record updated");

      // Update contact points in parallel for better performance
      console.log("📞 Updating contact points in parallel...");

      const updatePromises = [];

      // Update email contact point
      if (email && emailId) {
        console.log("📧 Queuing email contact point update...");
        updatePromises.push(
          db
            .update(contactPoint)
            .set({
              value: email,
              updatedAt: new Date().toISOString(),
            })
            .where(eq(contactPoint.id, emailId))
        );
      }

      // Update phone contact point
      if (phone && phoneId) {
        console.log("📱 Queuing phone contact point update...");
        updatePromises.push(
          db
            .update(contactPoint)
            .set({
              value: phone,
              updatedAt: new Date().toISOString(),
            })
            .where(eq(contactPoint.id, phoneId))
        );
      }

      // Update address contact point
      if (address && addressId) {
        console.log("🏠 Queuing address contact point update...");
        updatePromises.push(
          db
            .update(contactPoint)
            .set({
              value: address,
              addressTypeId: addressTypeId || null,
              updatedAt: new Date().toISOString(),
            })
            .where(eq(contactPoint.id, addressId))
        );
      }

      // Execute all contact point updates in parallel
      if (updatePromises.length > 0) {
        console.log(
          `🚀 Executing ${updatePromises.length} contact point updates in parallel...`
        );
        await Promise.all(updatePromises);
        console.log("✅ All contact point updates completed");
      }

      console.log("🎉 All database updates completed successfully");
      return { success: true };
    } catch (updateError) {
      console.error("❌ Database update error:", updateError);
      throw updateError;
    }
  } catch (error: any) {
    console.error("❌ Database update failed:", error);
    return {
      errors: {
        general: [
          error?.message || "Could not update profile. Please try again later.",
        ],
      },
    };
  }
}
