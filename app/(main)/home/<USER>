"use server";
import { getIndividualByPartyId } from "@/actions/individuals";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import MissingParty from "@/components/missing-party";
import HomeScreen from "./home";
import { getProfileImageUrl } from "@/lib/profile";
import { getContactPointTypes } from "@/actions/contact-point-types";
import { getContactPointsByPartyId } from "@/actions/contact-points";
import { getAddressTypes } from "@/actions/address-types";

export default async function Home() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  const [profilePic, individual, contactPointTypes, addressTypes] =
    await Promise.all([
      getProfileImageUrl(+dbId),
      getIndividualByPartyId(+dbId),
      getContactPointTypes(),
      getAddressTypes(),
    ]);

  if (!individual) return <MissingParty />;

  const contactPoints = await getContactPointsByPartyId(individual.party_id);

  return (
    <HomeScreen
      individual={individual}
      profilePic={profilePic}
      externalId={externalId}
      email={email}
      contactPoints={contactPoints}
      contactPointTypes={contactPointTypes}
      addressTypes={addressTypes}
    />
  );
}
