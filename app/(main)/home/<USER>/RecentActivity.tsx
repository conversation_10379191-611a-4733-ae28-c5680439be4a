"use client";

import { Suspense, useEffect, useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronRight, Car, Wallet, Users, Activity } from "lucide-react";
import { useRouter } from "next/navigation";

// Recent Activity Component (streams in when data is available)
function RecentActivity() {
  const router = useRouter();
  const [activities, setActivities] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate data loading
    const loadActivities = async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));

      const recentActivities = [
        {
          id: 1,
          type: "booking",
          title: "You booked Toyota Hilux",
          time: "2 hours ago",
          image: "/placeholder.svg?height=40&width=40",
        },
        {
          id: 2,
          type: "payment",
          title: "Monthly payment processed",
          time: "Yesterday",
          image: "/placeholder.svg?height=40&width=40",
        },
        {
          id: 3,
          type: "group",
          title: "<PERSON> joined Family SUV group",
          time: "2 days ago",
          image: "/placeholder.svg?height=40&width=40",
        },
      ];

      setActivities(recentActivities);
      setLoading(false);
    };

    loadActivities();
  }, []);

  if (loading) {
    return <RecentActivitySkeleton />;
  }

  return (
    <div className="px-4 mt-12 mb-8">
      <h3 className="text-[#333333] font-semibold mb-4">Recent Activity</h3>
      <div className="p-1 bg-transparent">
        {activities.length > 3 ? (
          activities.map((activity) => (
            <div
              key={activity.id}
              className="p-4 flex items-center bg-white rounded-lg drop-shadow-md mb-3 mx-1 border border-gray-100"
            >
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                {activity.type === "booking" ? (
                  <Car size={20} className="text-[#009639]" />
                ) : activity.type === "payment" ? (
                  <Wallet size={20} className="text-[#009639]" />
                ) : activity.type === "group" ? (
                  <Users size={20} className="text-[#009639]" />
                ) : (
                  <Activity size={20} className="text-[#009639]" />
                )}
              </div>
              <div className="flex-1">
                <p className="text-[#333333] font-medium text-sm">
                  {activity.title}
                </p>
                <p className="text-[#797879] text-xs">{activity.time}</p>
              </div>
              <ChevronRight size={16} className="text-[#797879]" />
            </div>
          ))
        ) : (
          /* Empty State */
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 text-center">
            <div className="w-16 h-16 bg-[#f8f8f8] rounded-full flex items-center justify-center mx-auto mb-4">
              <Activity size={24} className="text-[#797879]" />
            </div>
            <h4 className="text-[#333333] font-medium mb-2">No Activity Yet</h4>
            <p className="text-[#797879] text-sm mb-4 leading-relaxed">
              Your recent bookings, payments, and group activities will appear
              here.
            </p>
            <button
              className="bg-[#009639] text-white px-6 py-2 rounded-full text-sm font-medium"
              onClick={() => router.push("/opportunities")}
            >
              Explore Opportunities
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// Loading skeleton for Recent Activity
function RecentActivitySkeleton() {
  return (
    <div className="px-4 mt-12">
      <div className="flex justify-between items-center mb-4">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-4 w-16" />
      </div>

      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-4">
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center space-x-3">
              <Skeleton className="w-10 h-10 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-3 w-24" />
              </div>
              <Skeleton className="w-4 h-4" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Main Dynamic Content Component - Only original sections
export default function DynamicContent() {
  return (
    <>
      {/* Recent Activity - Only original component that needs data */}
      <Suspense fallback={<RecentActivitySkeleton />}>
        <RecentActivity />
      </Suspense>

      {/* Bottom spacing */}
      <div className="h-20"></div>
    </>
  );
}
