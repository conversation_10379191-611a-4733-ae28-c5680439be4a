"use server";

import { db } from "@/db";
import { individual, contactPoint } from "@/drizzle/schema";
import { eq } from "drizzle-orm";

/**
 * Profile Update Actions
 * Fast, reliable profile update operations with direct database access
 */

// Fast profile update for home route - direct database, no revalidatePath
export async function updateProfileFromHome(_: any, formData: FormData) {
  console.log("🏠 HOME ACTION: updateProfileFromHome started");

  try {
    // Extract and validate form data
    const firstName = (formData.get("firstName") as string)?.trim();
    const lastName = (formData.get("lastName") as string)?.trim();
    const email = (formData.get("email") as string)?.trim();
    const phone = (formData.get("phone") as string)?.trim();
    const address = (formData.get("address") as string)?.trim();
    const dateOfBirth = (formData.get("dateOfBirth") as string)?.trim();

    const individualId = Number(formData.get("id"));
    const emailId = Number(formData.get("emailId"));
    const phoneId = Number(formData.get("phoneId"));
    const addressId = Number(formData.get("addressId"));
    const addressTypeId = Number(formData.get("addressTypeId"));

    // Basic validation
    if (!firstName || !lastName || !email || !individualId) {
      return {
        errors: {
          firstName: !firstName ? ["First name is required"] : [],
          lastName: !lastName ? ["Last name is required"] : [],
          email: !email ? ["Email is required"] : [],
          general: !individualId ? ["Invalid profile ID"] : [],
        },
      };
    }

    console.log("📝 Updating individual record...");

    // Update individual record first
    await db
      .update(individual)
      .set({
        firstName: firstName,
        lastName: lastName,
        birthDate: dateOfBirth || null,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(individual.id, individualId));

    console.log("📞 Updating contact points in parallel...");

    // Update contact points in parallel for speed
    const updatePromises = [];

    if (email && emailId) {
      updatePromises.push(
        db
          .update(contactPoint)
          .set({
            value: email,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(contactPoint.id, emailId))
      );
    }

    if (phone && phoneId) {
      updatePromises.push(
        db
          .update(contactPoint)
          .set({
            value: phone,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(contactPoint.id, phoneId))
      );
    }

    if (address && addressId) {
      updatePromises.push(
        db
          .update(contactPoint)
          .set({
            value: address,
            addressTypeId: addressTypeId || null,
            updatedAt: new Date().toISOString(),
          })
          .where(eq(contactPoint.id, addressId))
      );
    }

    // Execute all contact point updates in parallel
    if (updatePromises.length > 0) {
      await Promise.all(updatePromises);
    }

    console.log("✅ Profile updated successfully via direct database");

    // No revalidatePath - let SWR handle cache invalidation
    return { success: true };
  } catch (error: any) {
    console.error("❌ Database update failed:", error);
    return {
      errors: {
        general: [
          error?.message || "Could not update profile. Please try again later.",
        ],
      },
    };
  }
}
