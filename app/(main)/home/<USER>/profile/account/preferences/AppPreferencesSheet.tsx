"use client";

import { useState } from "react";
import {
  ArrowLeft,
  Globe,
  Moon,
  Smartphone,
  Volume2,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface AppPreferencesSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AppPreferencesSheet({
  isOpen,
  onClose,
}: AppPreferencesSheetProps) {
  const [preferences, setPreferences] = useState({
    language: "English",
    theme: "Light",
    soundEffects: true,
    hapticFeedback: true,
    autoSync: true,
  });

  const handleToggle = (setting: keyof typeof preferences) => {
    if (typeof preferences[setting] === 'boolean') {
      setPreferences({
        ...preferences,
        [setting]: !preferences[setting],
      });
    }
  };

  const handleSave = () => {
    console.log("Saving app preferences:", preferences);
    onClose();
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  App Preferences
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Customize your app experience
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* General Settings */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">General</h3>
              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Globe size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">Language</h3>
                      <p className="text-xs text-[#797879]">
                        Choose your preferred language
                      </p>
                    </div>
                    <select 
                      className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#009639] focus:border-[#009639] p-2"
                      value={preferences.language}
                      onChange={(e) => setPreferences({...preferences, language: e.target.value})}
                    >
                      <option value="English">English</option>
                      <option value="Afrikaans">Afrikaans</option>
                      <option value="Zulu">Zulu</option>
                      <option value="Xhosa">Xhosa</option>
                    </select>
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Moon size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">Theme</h3>
                      <p className="text-xs text-[#797879]">
                        Choose light or dark mode
                      </p>
                    </div>
                    <select 
                      className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-[#009639] focus:border-[#009639] p-2"
                      value={preferences.theme}
                      onChange={(e) => setPreferences({...preferences, theme: e.target.value})}
                    >
                      <option value="Light">Light</option>
                      <option value="Dark">Dark</option>
                      <option value="Auto">Auto</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Sound & Haptics */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">Sound & Haptics</h3>
              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Volume2 size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">Sound Effects</h3>
                      <p className="text-xs text-[#797879]">
                        Play sounds for app interactions
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={preferences.soundEffects}
                        onChange={() => handleToggle("soundEffects")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Smartphone size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">Haptic Feedback</h3>
                      <p className="text-xs text-[#797879]">
                        Feel vibrations for app interactions
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={preferences.hapticFeedback}
                        onChange={() => handleToggle("hapticFeedback")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Data & Sync */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">Data & Sync</h3>
              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                <div className="p-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Smartphone size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">Auto Sync</h3>
                      <p className="text-xs text-[#797879]">
                        Automatically sync data when connected
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={preferences.autoSync}
                        onChange={() => handleToggle("autoSync")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="border-t border-gray-200 p-4">
            <button
              className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white w-full py-4 rounded-full text-lg font-semibold shadow-md"
              onClick={handleSave}
            >
              Save Changes
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
