"use client";

import {
  ArrowLeft,
  HelpCircle,
  MessageCircle,
  Phone,
  Mail,
  FileText,
  ChevronRight,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";

interface HelpCenterSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function HelpCenterSheet({
  isOpen,
  onClose,
}: HelpCenterSheetProps) {
  const helpSections = [
    {
      id: "faq",
      title: "Frequently Asked Questions",
      description: "Find answers to common questions",
      icon: <HelpCircle size={18} className="text-[#009639]" />,
    },
    {
      id: "guides",
      title: "User Guides",
      description: "Step-by-step tutorials and guides",
      icon: <FileText size={18} className="text-[#009639]" />,
    },
    {
      id: "chat",
      title: "Live Chat Support",
      description: "Chat with our support team",
      icon: <MessageCircle size={18} className="text-[#009639]" />,
    },
    {
      id: "phone",
      title: "Phone Support",
      description: "Call us at +27 11 123 4567",
      icon: <Phone size={18} className="text-[#009639]" />,
    },
    {
      id: "email",
      title: "Email Support",
      description: "Send us an <NAME_EMAIL>",
      icon: <Mail size={18} className="text-[#009639]" />,
    },
  ];

  const handleSectionClick = (sectionId: string) => {
    console.log("Help section clicked:", sectionId);
    // In a real app, you would handle navigation to specific help sections
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Help Center
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Get help and support
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Welcome Message */}
            <div className="p-4">
              <div className="bg-gradient-to-r from-[#009639] to-[#007A2F] rounded-xl p-4 text-white mb-4">
                <h3 className="font-semibold mb-2">How can we help you?</h3>
                <p className="text-sm text-green-100">
                  Our support team is here to assist you with any questions or issues you may have.
                </p>
              </div>
            </div>

            {/* Help Sections */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">Support Options</h3>
              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                {helpSections.map((section, index) => (
                  <div
                    key={section.id}
                    className={`p-4 flex items-center cursor-pointer hover:bg-gray-50 transition-colors ${
                      index < helpSections.length - 1
                        ? "border-b border-[#f2f2f2]"
                        : ""
                    }`}
                    onClick={() => handleSectionClick(section.id)}
                  >
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      {section.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        {section.title}
                      </h3>
                      <p className="text-xs text-[#797879]">
                        {section.description}
                      </p>
                    </div>
                    <ChevronRight size={20} className="text-[#797879]" />
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">Quick Actions</h3>
              <div className="grid grid-cols-2 gap-3">
                <button
                  className="bg-white rounded-xl shadow-md border border-gray-100 p-4 text-center hover:bg-gray-50 transition-colors"
                  onClick={() => handleSectionClick("report-issue")}
                >
                  <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mx-auto mb-2">
                    <HelpCircle size={20} className="text-[#009639]" />
                  </div>
                  <p className="text-sm font-medium text-[#333333]">Report Issue</p>
                </button>
                
                <button
                  className="bg-white rounded-xl shadow-md border border-gray-100 p-4 text-center hover:bg-gray-50 transition-colors"
                  onClick={() => handleSectionClick("feedback")}
                >
                  <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mx-auto mb-2">
                    <MessageCircle size={20} className="text-[#009639]" />
                  </div>
                  <p className="text-sm font-medium text-[#333333]">Send Feedback</p>
                </button>
              </div>
            </div>

            {/* Contact Info */}
            <div className="p-4">
              <div className="bg-gray-50 rounded-xl p-4 border border-gray-100">
                <h4 className="text-[#333333] font-medium mb-2">Contact Information</h4>
                <div className="space-y-2 text-sm text-[#797879]">
                  <p>📞 Phone: +27 11 123 4567</p>
                  <p>📧 Email: <EMAIL></p>
                  <p>🕒 Hours: Mon-Fri 8AM-6PM SAST</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
