"use client";

import { useState } from "react";
import StaticHomeLayout from "./StaticHomeLayout";
import DynamicContent from "./DynamicContent";
import VehicleDiscoveryDrawer from "./VehicleDiscoveryDrawer";
import ApplicationProcessDrawer from "./ApplicationProcessDrawer";
import ApplicationSubmittedDrawer from "./ApplicationSubmittedDrawer";
import ProfileSheet from "./ProfileSheet";
import { IndividualRead } from "@/types/individuals";
import { getStaticFormData, getContactPoints } from "../actions";
import { Suspense } from "react";

// Optimized home screen that renders static content immediately
// and streams in dynamic content as it becomes available
export default function OptimizedHomeScreen({
  individual,
  profilePic,
  externalId,
  email,
}: {
  individual: IndividualRead;
  profilePic: string | null;
  externalId: string;
  email: string;
}) {
  // State for drawers and sheets
  const [showVehicleDrawer, setShowVehicleDrawer] = useState(false);
  const [showApplicationDrawer, setShowApplicationDrawer] = useState(false);
  const [showSubmittedDrawer, setShowSubmittedDrawer] = useState(false);
  const [showProfileSheet, setShowProfileSheet] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null);

  // Contact data state - loaded on demand
  const [contactData, setContactData] = useState<{
    contactPoints: any[];
    contactPointTypes: any[];
    addressTypes: any[];
    loaded: boolean;
    loading: boolean;
  }>({
    contactPoints: [],
    contactPointTypes: [],
    addressTypes: [],
    loaded: false,
    loading: false,
  });

  // Load contact data when needed
  const loadContactData = async () => {
    if (contactData.loaded || contactData.loading) return;

    setContactData((prev) => ({ ...prev, loading: true }));

    try {
      const [staticData, contactPoints] = await Promise.all([
        getStaticFormData(),
        getContactPoints(individual.party_id),
      ]);

      setContactData({
        contactPoints,
        contactPointTypes: staticData.contactPointTypes,
        addressTypes: staticData.addressTypes,
        loaded: true,
        loading: false,
      });
    } catch (error) {
      console.error("Failed to load contact data:", error);
      setContactData({
        contactPoints: [],
        contactPointTypes: [],
        addressTypes: [],
        loaded: true,
        loading: false,
      });
    }
  };

  // Handle profile sheet opening
  const handleProfileSheetOpen = () => {
    setShowProfileSheet(true);
    loadContactData();
  };

  // Handle vehicle drawer opening
  const handleVehicleDrawerOpen = () => {
    setShowVehicleDrawer(true);
    loadContactData();
  };

  return (
    <>
      {/* Static layout renders immediately */}
      <StaticHomeLayout
        profilePic={profilePic}
        firstName={individual.first_name}
        lastName={individual.last_name}
        onProfileClick={handleProfileSheetOpen}
        onVehicleDrawerOpen={handleVehicleDrawerOpen}
      >
        {/* Dynamic content streams in */}
        <Suspense
          fallback={
            <div className="px-4 py-8 text-center text-gray-500">
              Loading additional content...
            </div>
          }
        >
          <DynamicContent />
        </Suspense>
      </StaticHomeLayout>

      {/* Interactive Components */}
      <VehicleDiscoveryDrawer
        isOpen={showVehicleDrawer}
        onClose={() => setShowVehicleDrawer(false)}
        onNext={(vehicle) => {
          setSelectedVehicle(vehicle);
          setShowVehicleDrawer(false);
          setShowApplicationDrawer(true);
        }}
      />

      <ApplicationProcessDrawer
        isOpen={showApplicationDrawer}
        onClose={() => setShowApplicationDrawer(false)}
        onSubmit={() => {
          setShowApplicationDrawer(false);
          setShowSubmittedDrawer(true);
        }}
        selectedVehicle={selectedVehicle}
      />

      <ApplicationSubmittedDrawer
        isOpen={showSubmittedDrawer}
        onClose={() => setShowSubmittedDrawer(false)}
        selectedVehicle={selectedVehicle}
      />

      <ProfileSheet
        isOpen={showProfileSheet}
        onClose={() => setShowProfileSheet(false)}
        individual={individual}
        profilePic={profilePic}
        externalId={externalId}
        email={email}
        contactPoints={contactData.contactPoints}
        contactPointTypes={contactData.contactPointTypes}
        addressTypes={contactData.addressTypes}
      />
    </>
  );
}
