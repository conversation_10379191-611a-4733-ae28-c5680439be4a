"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Users,
  MapPin,
  FileText,
  Building,
  Mail,
  Plus,
  X,
  Percent,
} from "lucide-react";
import {
  createGroup,
  getCities,
  getCountries,
  getCurrentUserPartyId,
  inviteMembersToGroup,
} from "../../../drizzle-actions/community";

interface City {
  id: number;
  name: string;
  province: string;
  country: string;
}

interface MemberInvitation {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fraction: number;
}

export default function CreateGroupScreen() {
  const router = useRouter();
  const [groupName, setGroupName] = useState("");
  const [description, setDescription] = useState("");
  const [purpose, setPurpose] = useState("");
  const [registrationNumber, setRegistrationNumber] = useState("");
  const [registrationCountry, setRegistrationCountry] = useState("");
  const [selectedCityId, setSelectedCityId] = useState<number | undefined>();
  const [cities, setCities] = useState<City[]>([]);
  const [countries, setCountries] = useState<string[]>([]);
  const [memberInvitations, setMemberInvitations] = useState<
    MemberInvitation[]
  >([]);
  const [newMemberEmail, setNewMemberEmail] = useState("");
  const [newMemberFirstName, setNewMemberFirstName] = useState("");
  const [newMemberLastName, setNewMemberLastName] = useState("");
  const [newMemberFraction, setNewMemberFraction] = useState(10);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [cityList, countryList] = await Promise.all([
          getCities(),
          getCountries(),
        ]);
        setCities(cityList);
        setCountries(countryList);
        setRegistrationCountry(countryList[0] || "South Africa");
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const addMemberInvitation = () => {
    if (
      !newMemberEmail.trim() ||
      !newMemberFirstName.trim() ||
      !newMemberLastName.trim()
    ) {
      alert("Please fill in all member details");
      return;
    }

    if (memberInvitations.some((m) => m.email === newMemberEmail)) {
      alert("This email is already in the invitation list");
      return;
    }

    const totalFraction =
      memberInvitations.reduce((sum, m) => sum + m.fraction, 0) +
      newMemberFraction;
    if (totalFraction > 100) {
      alert("Total ownership cannot exceed 100%");
      return;
    }

    const newInvitation: MemberInvitation = {
      id: Date.now().toString(),
      email: newMemberEmail.trim(),
      firstName: newMemberFirstName.trim(),
      lastName: newMemberLastName.trim(),
      fraction: newMemberFraction,
    };

    setMemberInvitations([...memberInvitations, newInvitation]);
    setNewMemberEmail("");
    setNewMemberFirstName("");
    setNewMemberLastName("");
    setNewMemberFraction(10);
  };

  const removeMemberInvitation = (id: string) => {
    setMemberInvitations(memberInvitations.filter((m) => m.id !== id));
  };

  const handleCreateGroup = async () => {
    if (!groupName.trim()) {
      alert("Please enter a group name");
      return;
    }

    if (!description.trim()) {
      alert("Please enter a description");
      return;
    }

    if (!purpose.trim()) {
      alert("Please enter the group purpose");
      return;
    }

    if (!registrationNumber.trim()) {
      alert("Please enter a registration number");
      return;
    }

    const totalInvitedFraction = memberInvitations.reduce(
      (sum, m) => sum + m.fraction,
      0
    );
    const ownerFraction = 100 - totalInvitedFraction;

    if (ownerFraction <= 0) {
      alert("You must retain at least some ownership percentage");
      return;
    }

    try {
      setCreating(true);

      // Get current user's party ID (in real app, this would come from auth context)
      const currentUserPartyId = await getCurrentUserPartyId(1); // Placeholder user ID

      const result = await createGroup(
        groupName.trim(),
        description.trim(),
        purpose.trim(),
        registrationNumber.trim(),
        registrationCountry,
        currentUserPartyId,
        ownerFraction / 100, // Convert percentage to decimal
        selectedCityId
      );

      if (result.success && result.groupId) {
        // Send member invitations if any
        if (memberInvitations.length > 0) {
          const inviteResult = await inviteMembersToGroup(
            result.groupId,
            memberInvitations.map((invite) => ({
              email: invite.email,
              firstName: invite.firstName,
              lastName: invite.lastName,
              fraction: invite.fraction / 100, // Convert percentage to decimal
            }))
          );

          if (!inviteResult.success) {
            console.warn(
              "Group created but invitations failed:",
              inviteResult.message
            );
          }
        }

        alert("Group created successfully!");
        router.push(`/group-details/${result.groupId}`);
      } else {
        alert(result.message || "Failed to create group");
      }
    } catch (error) {
      console.error("Error creating group:", error);
      alert("Failed to create group. Please try again.");
    } finally {
      setCreating(false);
    }
  };

  const remainingOwnership =
    100 - memberInvitations.reduce((sum, m) => sum + m.fraction, 0);

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Create Group</h1>
      </div>

      {/* Form */}
      <div className="p-6 pb-24">
        {/* Basic Information */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
          <h2 className="text-lg font-semibold text-[#333333] mb-6">
            Group Information
          </h2>

          {/* Group Name */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Users size={16} className="mr-2 text-[#009639]" />
              Group Name
            </label>
            <input
              type="text"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              placeholder="Enter group name (e.g., City Commuters)"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              maxLength={100}
            />
          </div>

          {/* Description */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <FileText size={16} className="mr-2 text-[#009639]" />
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe your group's purpose (e.g., Daily commuters sharing electric vehicles)"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333] resize-none"
              rows={3}
              maxLength={500}
            />
            <p className="text-xs text-[#797879] mt-1">
              {description.length}/500 characters
            </p>
          </div>

          {/* Purpose */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Building size={16} className="mr-2 text-[#009639]" />
              Purpose
            </label>
            <input
              type="text"
              value={purpose}
              onChange={(e) => setPurpose(e.target.value)}
              placeholder="Enter the purpose (e.g., Vehicle sharing for commuting)"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              maxLength={200}
            />
          </div>

          {/* Location */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <MapPin size={16} className="mr-2 text-[#009639]" />
              Location (Optional)
            </label>
            {loading ? (
              <div className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg bg-gray-100 animate-pulse">
                Loading cities...
              </div>
            ) : (
              <select
                value={selectedCityId || ""}
                onChange={(e) =>
                  setSelectedCityId(
                    e.target.value ? parseInt(e.target.value) : undefined
                  )
                }
                className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              >
                <option value="">Select a city (optional)</option>
                {cities.map((city) => (
                  <option key={city.id} value={city.id}>
                    {city.name}, {city.province}, {city.country}
                  </option>
                ))}
              </select>
            )}
          </div>
        </div>

        {/* Registration Information */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
          <h2 className="text-lg font-semibold text-[#333333] mb-6">
            Registration Details
          </h2>

          {/* Registration Number */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Building size={16} className="mr-2 text-[#009639]" />
              Registration Number
            </label>
            <input
              type="text"
              value={registrationNumber}
              onChange={(e) => setRegistrationNumber(e.target.value)}
              placeholder="Enter registration number (e.g., 2023/123456/08)"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              maxLength={50}
            />
          </div>

          {/* Registration Country */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <MapPin size={16} className="mr-2 text-[#009639]" />
              Registration Country
            </label>
            <select
              value={registrationCountry}
              onChange={(e) => setRegistrationCountry(e.target.value)}
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
            >
              {countries.map((country) => (
                <option key={country} value={country}>
                  {country}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Member Invitations */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-[#333333]">
              Invite Members
            </h2>
            <span className="text-sm text-[#797879]">
              Your ownership: {remainingOwnership}%
            </span>
          </div>

          {/* Add Member Form */}
          <div className="rounded-lg mb-4">
            <div className="grid grid-cols-2 gap-3 mb-3">
              <input
                type="text"
                value={newMemberFirstName}
                onChange={(e) => setNewMemberFirstName(e.target.value)}
                placeholder="First name"
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
              <input
                type="text"
                value={newMemberLastName}
                onChange={(e) => setNewMemberLastName(e.target.value)}
                placeholder="Last name"
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
            </div>
            <div className="grid grid-cols-3 gap-3 mb-3">
              <input
                type="email"
                value={newMemberEmail}
                onChange={(e) => setNewMemberEmail(e.target.value)}
                placeholder="Email address"
                className="col-span-2 px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
              <div className="flex items-center">
                <input
                  type="number"
                  min="1"
                  max={remainingOwnership}
                  value={newMemberFraction}
                  onChange={(e) =>
                    setNewMemberFraction(parseInt(e.target.value) || 0)
                  }
                  className="w-full px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                />
                <Percent size={16} className="ml-2 text-[#797879]" />
              </div>
            </div>
            <button
              onClick={addMemberInvitation}
              disabled={
                !newMemberEmail.trim() ||
                !newMemberFirstName.trim() ||
                !newMemberLastName.trim() ||
                newMemberFraction <= 0 ||
                newMemberFraction > remainingOwnership
              }
              className="w-full bg-[#009639] text-white py-2 rounded-full font-medium disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
            >
              <Plus size={16} className="mr-2" />
              Add Member
            </button>
          </div>

          {/* Member List */}
          {memberInvitations.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-[#333333] mb-2">
                Members to Invite ({memberInvitations.length})
              </h4>
              {memberInvitations.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-3 bg-[#f8f9fa] rounded-lg"
                >
                  <div className="flex-1">
                    <p className="font-medium text-[#333333]">
                      {member.firstName} {member.lastName}
                    </p>
                    <p className="text-sm text-[#797879]">{member.email}</p>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-[#009639] mr-3">
                      {member.fraction}%
                    </span>
                    <button
                      onClick={() => removeMemberInvitation(member.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Create Button */}
        <div className=" bottom-0 left-0 right-0 p-4 bg-white z-50">
          <button
            onClick={handleCreateGroup}
            disabled={
              creating ||
              !groupName.trim() ||
              !description.trim() ||
              !purpose.trim() ||
              !registrationNumber.trim()
            }
            className="w-full bg-[#009639] text-white py-4 rounded-full font-semibold shadow-md disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {creating ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Creating Group...
              </>
            ) : (
              "Create Group"
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
