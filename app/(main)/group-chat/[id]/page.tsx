"use client";

import { useState, useRef, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  MoreVertical,
  Paperclip,
  Send,
  Image as ImageIcon,
  Camera,
  File,
  MapPin,
  Calendar,
  ThumbsUp,
  Smile,
  Mic,
} from "lucide-react";

export default function GroupChatScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const groupId = use(params).id;
  const [message, setMessage] = useState("");
  const [showAttachmentOptions, setShowAttachmentOptions] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock data - in a real app, this would come from an API
  const group = {
    id: groupId,
    name:
      groupId === "1"
        ? "Toyota Fortuner Group"
        : groupId === "2"
        ? "Volkswagen Polo Group"
        : "Ford Ranger Group",
    image: "/placeholder.svg?height=60&width=60",
    members: [
      {
        id: "user1",
        name: "<PERSON>",
        image: "/placeholder.svg?height=40&width=40",
      },
      {
        id: "user2",
        name: "<PERSON>",
        image: "/placeholder.svg?height=40&width=40",
      },
      {
        id: "user3",
        name: "<PERSON>",
        image: "/placeholder.svg?height=40&width=40",
      },
    ],
  };

  const [messages, setMessages] = useState([
    {
      id: 1,
      senderId: "user2",
      senderName: "Robert Fox",
      senderImage: "/placeholder.svg?height=40&width=40",
      text: "Hey everyone! I'm planning to use the car this weekend. Anyone else need it?",
      timestamp: "2023-05-15T10:30:00",
      isRead: true,
    },
    {
      id: 2,
      senderId: "user3",
      senderName: "Esther Howard",
      senderImage: "/placeholder.svg?height=40&width=40",
      text: "I was thinking of using it on Saturday for a grocery run. What time do you need it?",
      timestamp: "2023-05-15T10:32:00",
      isRead: true,
    },
    {
      id: 3,
      senderId: "user2",
      senderName: "Robert Fox",
      senderImage: "/placeholder.svg?height=40&width=40",
      text: "I was planning on all day Saturday for a trip to the beach. Maybe we can coordinate?",
      timestamp: "2023-05-15T10:35:00",
      isRead: true,
    },
    {
      id: 4,
      senderId: "user1",
      senderName: "Jane Cooper",
      senderImage: "/placeholder.svg?height=40&width=40",
      text: "I can help coordinate. Let me check the calendar.",
      timestamp: "2023-05-15T10:40:00",
      isRead: true,
    },
    {
      id: 5,
      senderId: "user1",
      senderName: "Jane Cooper",
      senderImage: "/placeholder.svg?height=40&width=40",
      text: "Looks like the car is free all weekend. Robert, you can have it Saturday, and Esther can use it Sunday morning. How does that sound?",
      timestamp: "2023-05-15T10:42:00",
      isRead: true,
    },
    {
      id: 6,
      senderId: "user3",
      senderName: "Esther Howard",
      senderImage: "/placeholder.svg?height=40&width=40",
      text: "That works for me! Thanks Jane.",
      timestamp: "2023-05-15T10:45:00",
      isRead: true,
    },
    {
      id: 7,
      senderId: "user2",
      senderName: "Robert Fox",
      senderImage: "/placeholder.svg?height=40&width=40",
      text: "Perfect! I'll make sure to fill up the tank before returning it.",
      timestamp: "2023-05-15T10:47:00",
      isRead: true,
    },
    {
      id: 8,
      senderId: "system",
      text: "Jane Cooper has updated the booking calendar",
      timestamp: "2023-05-15T10:50:00",
      isRead: true,
      isSystemMessage: true,
    },
  ]);

  const currentUser = {
    id: "user1",
    name: "Jane Cooper",
    image: "/placeholder.svg?height=40&width=40",
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = () => {
    if (message.trim() === "") return;

    const newMessage = {
      id: messages.length + 1,
      senderId: currentUser.id,
      senderName: currentUser.name,
      senderImage: currentUser.image,
      text: message,
      timestamp: new Date().toISOString(),
      isRead: false,
    };

    setMessages([...messages, newMessage]);
    setMessage("");
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString([], {
      weekday: "long",
      month: "long",
      day: "numeric",
    });
  };

  const shouldShowDate = (index: number) => {
    if (index === 0) return true;

    const currentDate = new Date(messages[index].timestamp).toDateString();
    const prevDate = new Date(messages[index - 1].timestamp).toDateString();

    return currentDate !== prevDate;
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#eef1f9"
              />
            </svg>
          </div>
        </div>
      </div> */}

      {/* Header */}
      <div className="bg-[#009639] px-4 py-3 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button className="mr-3" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <div
            className="flex items-center"
            onClick={() => router.push(`/group-details/${groupId}`)}
          >
            <div className="w-10 h-10 bg-[#e6ffe6] rounded-full overflow-hidden mr-3 shadow-sm border border-[#007A2F]">
              <Image
                src={group.image}
                alt={group.name}
                width={40}
                height={40}
                className="object-cover"
              />
            </div>
            <div>
              <h2 className="text-white font-medium">{group.name}</h2>
              <p className="text-xs text-[#e6ffe6]">
                {group.members.length} members
              </p>
            </div>
          </div>
        </div>
        <button className="text-white">
          <MoreVertical size={24} />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="space-y-4">
          {messages.map((msg, index) => (
            <div key={msg.id}>
              {shouldShowDate(index) && (
                <div className="flex justify-center my-4">
                  <div className="bg-[#e6ffe6] px-3 py-1 rounded-full shadow-sm">
                    <span className="text-xs text-[#007A2F] font-medium">
                      {formatDate(msg.timestamp)}
                    </span>
                  </div>
                </div>
              )}

              {msg.isSystemMessage ? (
                <div className="flex justify-center my-4">
                  <div className="bg-[#e6ffe6] px-3 py-1 rounded-full shadow-sm">
                    <span className="text-xs text-[#007A2F] font-medium">
                      {msg.text}
                    </span>
                  </div>
                </div>
              ) : (
                <div
                  className={`flex ${
                    msg.senderId === currentUser.id
                      ? "justify-end"
                      : "justify-start"
                  }`}
                >
                  <div
                    className={`max-w-[80%] ${
                      msg.senderId === currentUser.id ? "order-2" : "order-1"
                    }`}
                  >
                    {msg.senderId !== currentUser.id && (
                      <div className="flex items-center mb-1">
                        <div className="w-6 h-6 ride-avatar mr-1">
                          <Image
                            src={msg.senderImage || "/placeholder.svg"}
                            alt={msg.senderName || "User"}
                            width={24}
                            height={24}
                            className="object-cover"
                          />
                        </div>
                        <span className="text-xs text-[#797879]">
                          {msg.senderName}
                        </span>
                      </div>
                    )}
                    <div
                      className={`p-3 rounded-2xl shadow-sm ${
                        msg.senderId === currentUser.id
                          ? "bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-tr-none"
                          : "bg-white text-[#333333] rounded-tl-none border border-gray-100"
                      }`}
                    >
                      <p className="text-sm">{msg.text}</p>
                    </div>
                    <div
                      className={`mt-1 flex ${
                        msg.senderId === currentUser.id
                          ? "justify-end"
                          : "justify-start"
                      }`}
                    >
                      <span className="text-xs text-[#797879]">
                        {formatTime(msg.timestamp)}
                      </span>
                      {msg.senderId === currentUser.id && (
                        <span className="text-xs text-[#797879] ml-1">
                          {msg.isRead ? "Read" : "Sent"}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Attachment Options */}
      {showAttachmentOptions && (
        <div className="bg-white p-4 border-t border-[#f2f2f2] grid grid-cols-4 gap-4 shadow-lg">
          <button className="flex flex-col items-center">
            <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-1 shadow-sm">
              <ImageIcon size={24} className="text-[#009639]" />
            </div>
            <span className="text-xs text-[#333333]">Photo</span>
          </button>
          <button className="flex flex-col items-center">
            <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-1 shadow-sm">
              <Camera size={24} className="text-[#009639]" />
            </div>
            <span className="text-xs text-[#333333]">Camera</span>
          </button>
          <button className="flex flex-col items-center">
            <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-1 shadow-sm">
              <File size={24} className="text-[#009639]" />
            </div>
            <span className="text-xs text-[#333333]">Document</span>
          </button>
          <button className="flex flex-col items-center">
            <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-1 shadow-sm">
              <MapPin size={24} className="text-[#009639]" />
            </div>
            <span className="text-xs text-[#333333]">Location</span>
          </button>
          <button className="flex flex-col items-center">
            <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-1 shadow-sm">
              <Calendar size={24} className="text-[#009639]" />
            </div>
            <span className="text-xs text-[#333333]">Calendar</span>
          </button>
          <button className="flex flex-col items-center">
            <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-1 shadow-sm">
              <ThumbsUp size={24} className="text-[#009639]" />
            </div>
            <span className="text-xs text-[#333333]">Poll</span>
          </button>
        </div>
      )}

      {/* Message Input */}
      <div className="bg-white p-3 border-t border-[#f2f2f2] flex items-center shadow-lg">
        <button
          className="p-2 text-[#797879]"
          onClick={() => setShowAttachmentOptions(!showAttachmentOptions)}
        >
          <Paperclip size={24} />
        </button>
        <div className="flex-1 bg-[#f2f2f2] rounded-full px-4 py-2 mx-2 flex items-center shadow-sm">
          <input
            type="text"
            placeholder="Type a message..."
            className="flex-1 bg-transparent focus:outline-none text-[#333333] placeholder-[#797879]"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === "Enter") handleSendMessage();
            }}
          />
          <button className="text-[#797879] ml-2">
            <Smile size={20} />
          </button>
        </div>
        {message.trim() === "" ? (
          <button className="p-2 text-[#797879]">
            <Mic size={24} />
          </button>
        ) : (
          <button className="p-2 text-[#009639]" onClick={handleSendMessage}>
            <Send size={24} />
          </button>
        )}
      </div>
    </div>
  );
}
