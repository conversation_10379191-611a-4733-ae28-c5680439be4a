"use client";

import BottomNavigation from "../components/bottom-navigation";
import outputs from "@/amplify_outputs.json";
import { CookieStorage } from "aws-amplify/utils";
import { Amplify } from "aws-amplify";
import { cognitoUserPoolsTokenProvider } from "aws-amplify/auth/cognito";

Amplify.configure(outputs);

cognitoUserPoolsTokenProvider.setKeyValueStorage(new CookieStorage());

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen pb-16">
      {children}
      <BottomNavigation />
    </div>
  );
}
