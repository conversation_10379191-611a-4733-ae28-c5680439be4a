"use client";

import { useRouter } from "next/navigation";
import { use } from "react";
import Image from "next/image";
import {
  ArrowLeft,
  Users,
  Car,
  DollarSign,
  MessageCircle,
  Settings,
  ChevronRight,
  UserPlus,
  BarChart,
  Calendar as CalendarIcon,
} from "lucide-react";

export default function GroupDashboardScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const groupId = use(params).id;

  // Mock data - in a real app, this would come from an API
  const group = {
    id: groupId,
    name:
      groupId === "1"
        ? "Family SUV"
        : groupId === "2"
        ? "Weekend Getaway"
        : "Work Commute",
    photo: "/placeholder.svg?height=80&width=80",
    members: [
      { id: 1, name: "<PERSON>", ownership: 33, isAdmin: true },
      { id: 2, name: "<PERSON>", ownership: 33, isAdmin: false },
      { id: 3, name: "<PERSON>", ownership: 34, isAdmin: false },
    ],
    vehicles: [
      {
        id: 1,
        name:
          groupId === "1"
            ? "Toyota Fortuner"
            : groupId === "2"
            ? "Volkswagen Polo"
            : "Ford Ranger",
        image: "/placeholder.svg?height=120&width=200",
        status: "available",
      },
    ],
    balance: 1250,
    activities: [
      {
        id: 1,
        type: "booking",
        user: "John",
        description: "booked the vehicle",
        time: "2 hours ago",
      },
      {
        id: 2,
        type: "payment",
        user: "<PERSON>",
        description: "made a payment of $150",
        time: "Yesterday",
      },
      {
        id: 3,
        type: "maintenance",
        user: "System",
        description: "scheduled maintenance",
        time: "3 days ago",
      },
    ],
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">{group.name}</h1>
        <button
          className="ml-auto"
          onClick={() => router.push(`/group-settings/${groupId}`)}
        >
          <Settings size={24} className="text-white" />
        </button>
      </div>

      {/* Group Info */}
      <div className="bg-white p-4 flex items-center border-b border-[#f2f2f2] shadow-sm">
        <div className="w-16 h-16 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-4 shadow-sm">
          <Users size={32} className="text-[#009639]" />
        </div>
        <div>
          <h2 className="text-lg font-semibold text-[#333333]">{group.name}</h2>
          <p className="text-sm text-[#797879]">
            {group.members.length} members · {group.vehicles.length} vehicle
          </p>
        </div>
        <button
          className="ml-auto bg-[#009639] text-white p-2 rounded-full shadow-sm"
          onClick={() => router.push(`/group-chat/${groupId}`)}
        >
          <MessageCircle size={20} />
        </button>
      </div>

      {/* Quick Actions */}
      {/* <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-[#333333] font-semibold">Quick Actions</h3>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-4 mb-4">
          <div className="grid grid-cols-4 gap-2">
            <button
              className="flex flex-col items-center justify-center p-2"
              onClick={() => router.push(`/add-members/${groupId}`)}
            >
              <div className="w-12 h-12 bg-[#e6f3ff] rounded-full flex items-center justify-center mb-1">
                <UserPlus size={24} className="text-[#0286ff]" />
              </div>
              <span className="text-xs text-[#333333]">Add Members</span>
            </button>

            <button
              className="flex flex-col items-center justify-center p-2"
              onClick={() => router.push(`/vehicle-management/${groupId}`)}
            >
              <div className="w-12 h-12 bg-[#e6f3ff] rounded-full flex items-center justify-center mb-1">
                <Car size={24} className="text-[#0286ff]" />
              </div>
              <span className="text-xs text-[#333333]">Vehicle</span>
            </button>

            <button
              className="flex flex-col items-center justify-center p-2"
              onClick={() => router.push(`/group-finances/${groupId}`)}
            >
              <div className="w-12 h-12 bg-[#e6f3ff] rounded-full flex items-center justify-center mb-1">
                <BarChart size={24} className="text-[#0286ff]" />
              </div>
              <span className="text-xs text-[#333333]">Finances</span>
            </button>

            <button
              className="flex flex-col items-center justify-center p-2"
              onClick={() => router.push(`/group-settings/${groupId}`)}
            >
              <div className="w-12 h-12 bg-[#e6f3ff] rounded-full flex items-center justify-center mb-1">
                <Settings size={24} className="text-[#0286ff]" />
              </div>
              <span className="text-xs text-[#333333]">Settings</span>
            </button>
          </div>
        </div>
      </div> */}

      {/* Members */}
      <div className="p-4">
        <div className="flex justify-between items-center my-3">
          <h3 className="text-[#333333] font-semibold">Members</h3>
          <button
            className="text-[#009639] text-sm font-medium flex items-center"
            onClick={() => router.push(`/member-management/${groupId}`)}
          >
            View All <ChevronRight size={16} />
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          {group.members.map((member, index) => (
            <div
              key={member.id}
              className={`p-4 flex items-center justify-between ${
                index < group.members.length - 1
                  ? "border-b border-[#f2f2f2]"
                  : ""
              }`}
            >
              <div className="flex items-center">
                <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <span className="text-[#009639] font-medium">
                    {member.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <div className="flex items-center">
                    <p className="text-[#333333] font-medium">{member.name}</p>
                    {member.isAdmin && (
                      <span className="ml-2 text-xs bg-[#e6ffe6] text-[#009639] px-2 py-0.5 rounded-full">
                        Admin
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-[#797879]">
                    {member.ownership}% ownership
                  </p>
                </div>
              </div>
            </div>
          ))}

          <button
            className="p-4 flex items-center text-[#009639] w-full"
            onClick={() => router.push(`/add-members/${groupId}`)}
          >
            <UserPlus size={20} className="mr-2" />
            <span className="font-medium">Add Member</span>
          </button>
        </div>
      </div>

      {/* Vehicle */}
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-[#333333] font-semibold">Vehicle</h3>
          <button
            className="text-[#009639] text-sm font-medium flex items-center"
            onClick={() => router.push(`/vehicle-management/${groupId}`)}
          >
            Manage <ChevronRight size={16} />
          </button>
        </div>

        {group.vehicles.length > 0 ? (
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            {group.vehicles.map((vehicle) => (
              <div key={vehicle.id} className="flex">
                <div className="w-1/3 h-24 bg-[#f2f2f2] relative">
                  <Image
                    src={vehicle.image || "/placeholder.svg"}
                    alt={vehicle.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="flex-1 p-4">
                  <h4 className="text-[#333333] font-medium">{vehicle.name}</h4>
                  <div className="flex items-center mt-1">
                    <span className="text-xs bg-[#e6ffe6] text-[#007A2F] px-2 py-0.5 rounded-full font-medium">
                      Available
                    </span>
                  </div>
                  <div className="flex space-x-2 mt-2">
                    <button
                      className="text-xs bg-[#009639] text-white px-3 py-1.5 rounded-full shadow-sm"
                      onClick={() =>
                        router.push(`/booking-calendar/${vehicle.id}`)
                      }
                    >
                      Book
                    </button>
                    <button
                      className="text-xs border border-[#009639] text-[#009639] px-3 py-1.5 rounded-full shadow-sm"
                      onClick={() =>
                        router.push(`/vehicle-details/${vehicle.id}`)
                      }
                    >
                      Details
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md p-6 flex flex-col items-center border border-gray-100">
            <div className="w-16 h-16 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-sm">
              <Car size={32} className="text-[#009639]" />
            </div>
            <p className="text-[#333333] font-medium text-center mb-4">
              No vehicles added yet
            </p>
            <button
              className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white px-4 py-2 rounded-full text-sm font-medium shadow-md"
              onClick={() => router.push(`/vehicle-search`)}
            >
              Add Vehicle
            </button>
          </div>
        )}
      </div>

      {/* Group Funds */}
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-[#333333] font-semibold">Group Funds</h3>
          <button
            className="text-[#009639] text-sm font-medium flex items-center"
            onClick={() => router.push(`/group-finances/${groupId}`)}
          >
            View Details <ChevronRight size={16} />
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-[#797879] text-sm font-medium">
                Current Balance
              </p>
              <p className="text-2xl font-bold text-[#009639]">
                R{group.balance}
              </p>
            </div>
            <button
              className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white px-4 py-2 rounded-full text-sm font-medium shadow-sm"
              onClick={() => router.push(`/payment/${groupId}`)}
            >
              Make Payment
            </button>
          </div>

          <div className="h-2 bg-[#f2f2f2] rounded-full overflow-hidden">
            <div
              className="h-full bg-[#009639] rounded-full"
              style={{ width: "75%" }}
            ></div>
          </div>
          <div className="flex justify-between mt-1">
            <span className="text-xs text-[#797879]">Monthly Goal</span>
            <span className="text-xs text-[#797879]">R1,250 / R1,650</span>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="p-4 mb-8">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-[#333333] font-semibold">Recent Activity</h3>
        </div>

        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          {group.activities.map((activity, index) => (
            <div
              key={activity.id}
              className={`p-4 ${
                index < group.activities.length - 1
                  ? "border-b border-[#f2f2f2]"
                  : ""
              }`}
            >
              <div className="flex">
                <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  {activity.type === "booking" && (
                    <CalendarIcon size={20} className="text-[#009639]" />
                  )}
                  {activity.type === "payment" && (
                    <DollarSign size={20} className="text-[#009639]" />
                  )}
                  {activity.type === "maintenance" && (
                    <Car size={20} className="text-[#009639]" />
                  )}
                </div>
                <div>
                  <p className="text-[#333333]">
                    <span className="font-medium">{activity.user}</span>{" "}
                    {activity.description}
                  </p>
                  <p className="text-xs text-[#797879]">{activity.time}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
