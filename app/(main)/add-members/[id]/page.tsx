"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ArrowLeft,
  Search,
  Plus,
  Check,
  X,
  Mail,
  Clock,
  AlertCircle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  getGroupInvitations,
  cancelInvitation,
  inviteMembersToGroup,
} from "../../../../drizzle-actions/community";
import { createCompanyOwnershipInvite } from "@/actions/company-ownership-invites";
import { CompanyOwnershipInviteCreate } from "@/types/company-ownerships-invites";
interface GroupInvitation {
  id: number;
  email: string;
  name: string;
  firstName: string | null;
  lastName: string | null;
  fraction: number;
  status: string;
  sentAt: string | null;
}

// Zod schema for invitation form
const inviteSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  fraction: z
    .number()
    .min(1, "Ownership must be at least 1%")
    .max(100, "Ownership cannot exceed 100%"),
});

export default function AddMembersScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const groupId = parseInt(use(params).id);
  const [searchQuery, setSearchQuery] = useState("");
  const [invitations, setInvitations] = useState<GroupInvitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [showInviteForm, setShowInviteForm] = useState(false);

  // React Hook Form setup
  const form = useForm<z.infer<typeof inviteSchema>>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      fraction: 10,
    },
  });

  // Mock group data
  const group = {
    id: groupId,
    name: `Group ${groupId}`,
  };

  useEffect(() => {
    const fetchInvitations = async () => {
      try {
        setLoading(true);
        const invitationList = await getGroupInvitations(groupId);
        setInvitations(invitationList);
      } catch (error) {
        console.error("Failed to fetch invitations:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchInvitations();
  }, [groupId]);

  const filteredInvitations = invitations.filter(
    (invitation) =>
      invitation.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invitation.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCancelInvitation = async (invitationId: number) => {
    if (!confirm("Are you sure you want to cancel this invitation?")) {
      return;
    }

    try {
      const result = await cancelInvitation(invitationId);
      if (result.success) {
        setInvitations(invitations.filter((inv) => inv.id !== invitationId));
        alert("Invitation cancelled successfully");
      } else {
        alert(result.message || "Failed to cancel invitation");
      }
    } catch (error) {
      console.error("Error cancelling invitation:", error);
      alert("Failed to cancel invitation");
    }
  };

  const handleSendInvitation = async (values: z.infer<typeof inviteSchema>) => {
    if (invitations.some((inv) => inv.email === values.email)) {
      alert("This email has already been invited");
      return;
    }

    try {
      const result = await createCompanyOwnershipInvite({
        email: values.email.trim(),
        first_name: values.firstName.trim(),
        last_name: values.lastName.trim(),
        company_id: groupId,
        fraction: values.fraction,
      } as CompanyOwnershipInviteCreate);
      // const result = await inviteMembersToGroup(groupId, [
      //   {
      //     email: values.email.trim(),
      //     firstName: values.firstName.trim(),
      //     lastName: values.lastName.trim(),
      //     fraction: values.fraction / 100,
      //   },
      // ]);

      if (result) {
        const updatedInvitations = await getGroupInvitations(groupId);
        setInvitations(updatedInvitations);
        form.reset();
        setShowInviteForm(false);
        alert("Invitation sent successfully");
      } else {
        alert("Failed to send invitation");
      }
    } catch (error) {
      console.error("Error sending invitation:", error);
      alert("Failed to send invitation");
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "SENT":
        return <Clock size={16} className="text-yellow-600" />;
      case "ACCEPTED":
        return <Check size={16} className="text-green-600" />;
      case "DECLINED":
        return <X size={16} className="text-red-500" />;
      default:
        return <AlertCircle size={16} className="text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "SENT":
        return "bg-yellow-50 text-yellow-600";
      case "ACCEPTED":
        return "bg-green-50 text-green-600";
      case "DECLINED":
        return "bg-red-50 text-red-500";
      default:
        return "bg-gray-50 text-gray-500";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <div className="bg-green-600 px-6 py-4 flex items-center border-b border-green-700">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </Button>
          <h1 className="text-xl font-bold text-white">Manage Invitations</h1>
        </div>
        <div className="p-6">
          <Card>
            <CardContent className="p-4">
              <div className="h-4 bg-gray-200 rounded mb-4 animate-pulse"></div>
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="h-16 bg-gray-200 rounded animate-pulse"
                  ></div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-green-600 px-6 py-4 flex items-center justify-between border-b border-green-700">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </Button>
          <h1 className="text-xl font-bold text-white">Manage Invitations</h1>
        </div>
        <Button
          size="icon"
          className="bg-green-700 hover:bg-green-800"
          onClick={() => setShowInviteForm(!showInviteForm)}
        >
          <Plus size={24} className="text-white" />
        </Button>
      </div>

      {/* Invite Form */}
      {showInviteForm && (
        <Card className="m-4 border-gray-200">
          <CardHeader>
            <CardTitle>Send New Invitation</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSendInvitation)}
                className="space-y-4"
              >
                <div className="grid grid-cols-2 gap-3">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input placeholder="First name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Last name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Email address"
                          type="email"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="fraction"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Ownership %</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          max="100"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseInt(e.target.value) || 0)
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="flex space-x-3">
                  <Button
                    type="submit"
                    disabled={form.formState.isSubmitting}
                    className="flex-1 bg-[#009639] text-white py-2 rounded-lg font-medium disabled:bg-gray-300 disabled:cursor-not-allowed"
                  >
                    {form.formState.isSubmitting
                      ? "Sending..."
                      : "Send Invitation"}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowInviteForm(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}

      {/* Search */}
      <div className="p-4">
        <div className="relative">
          <Search
            size={18}
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
          />
          <Input
            type="text"
            placeholder="Search invitations by name or email"
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Invitations List */}
      <div className="px-4 pb-24 pt-2">
        <h3 className="text-gray-800 font-medium mb-4">
          Pending Invitations ({filteredInvitations.length})
        </h3>
        {filteredInvitations.length === 0 ? (
          <Card className="border-gray-200">
            <CardContent className="p-8 text-center">
              {searchQuery ? (
                <p className="text-gray-500">
                  No invitations match your search
                </p>
              ) : (
                <>
                  <Mail size={48} className="text-gray-200 mx-auto mb-4" />
                  <p className="text-gray-500 mb-4">No pending invitations</p>
                  <Button
                    onClick={() => setShowInviteForm(true)}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Send First Invitation
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card className="border-gray-200">
            <CardContent className="p-0">
              {filteredInvitations.map((invitation, index) => (
                <div
                  key={invitation.id}
                  className={`p-4 flex items-center ${
                    index < filteredInvitations.length - 1
                      ? "border-b border-gray-200"
                      : ""
                  }`}
                >
                  <div className="w-10 h-10 bg-[#333333] rounded-full flex items-center justify-center mr-3">
                    <Mail size={18} className="text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-[#333333] font-medium">
                      {invitation.name}
                    </h3>
                    <p className="text-xs text-[#333333]">{invitation.email}</p>
                    <div className="flex items-center mt-1 space-x-2">
                      <span
                        className={`text-xs px-2 py-0.5 rounded-full flex items-center ${getStatusColor(invitation.status)}`}
                      >
                        {getStatusIcon(invitation.status)}
                        <span className="ml-1">{invitation.status}</span>
                      </span>
                      <span className="text-xs text-[#333333]">
                        {invitation.fraction * 100}% ownership
                      </span>
                      {invitation.sentAt && (
                        <span className="text-xs text-[#333333]">
                          • {new Date(invitation.sentAt).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                  {invitation.status === "SENT" && (
                    <Button
                      className="[#333333]"
                      variant="destructive"
                      size="sm"
                      onClick={() => handleCancelInvitation(invitation.id)}
                    >
                      Cancel
                    </Button>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200 shadow-lg">
        <Button
          className="w-full bg-green-600 hover:bg-green-700"
          onClick={() => router.back()}
        >
          Done
        </Button>
      </div>
    </div>
  );
}
