"use server";
import { getIdentificationTypes } from "@/actions/identification-types";
import { getPartyByExternalID } from "@/actions/party";
import { createPartyIdentifications } from "@/actions/party-identifications";
import BackElement from "@/app/components/back-element";
import PostForm from "./post-form";

interface UploadDocumentsPageProps {
  params: {
    externalId: string;
  };
}

export default async function UploadDocumentsPage({
  params,
}: UploadDocumentsPageProps) {
  const { externalId } = await params;
  const party = await getPartyByExternalID(externalId);
  const identificationTypes = await getIdentificationTypes();
  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      <BackElement title="Upload Documents" />
      <PostForm
        partyId={party.id}
        identificationTypes={identificationTypes}
        action={createPartyIdentifications}
      />
    </div>
  );
}
