"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Bell,
  Calendar,
  Car,
  Wallet,
  MessageSquare,
} from "lucide-react";

export default function NotificationSettingsScreen() {
  const router = useRouter();

  // Mock data - in a real app, this would come from an API
  const [settings, setSettings] = useState({
    bookingReminders: true,
    paymentReminders: true,
    maintenanceAlerts: true,
    handoverReminders: true,
    groupMessages: true,
    systemUpdates: false,
    marketingEmails: false,
  });

  const handleToggle = (setting: keyof typeof settings) => {
    setSettings({
      ...settings,
      [setting]: !settings[setting],
    });
  };

  const handleSave = () => {
    console.log("Saving notification settings:", settings);
    // In a real app, you would make an API call here
    router.push("/profile");
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#eef1f9"
              />
            </svg>
          </div>
        </div>
      </div> */}

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Notification Settings</h1>
      </div>

      {/* Notification Settings */}
      <div className="p-4">
        <h3 className="text-[#333333] font-medium mb-3">App Notifications</h3>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2]">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <Calendar size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">
                  Booking Reminders
                </h3>
                <p className="text-xs text-[#797879]">
                  Notifications about upcoming bookings
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.bookingReminders}
                  onChange={() => handleToggle("bookingReminders")}
                />
                <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
              </label>
            </div>
          </div>

          <div className="p-4 border-b border-[#f2f2f2]">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <Wallet size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">
                  Payment Reminders
                </h3>
                <p className="text-xs text-[#797879]">
                  Notifications about upcoming payments
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.paymentReminders}
                  onChange={() => handleToggle("paymentReminders")}
                />
                <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
              </label>
            </div>
          </div>

          <div className="p-4 border-b border-[#f2f2f2]">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <Car size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">
                  Maintenance Alerts
                </h3>
                <p className="text-xs text-[#797879]">
                  Notifications about vehicle maintenance
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.maintenanceAlerts}
                  onChange={() => handleToggle("maintenanceAlerts")}
                />
                <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
              </label>
            </div>
          </div>

          <div className="p-4 border-b border-[#f2f2f2]">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <Car size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">
                  Handover Reminders
                </h3>
                <p className="text-xs text-[#797879]">
                  Notifications about vehicle handovers
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.handoverReminders}
                  onChange={() => handleToggle("handoverReminders")}
                />
                <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
              </label>
            </div>
          </div>

          <div className="p-4">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <MessageSquare size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">Group Messages</h3>
                <p className="text-xs text-[#797879]">
                  Notifications about new messages in groups
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.groupMessages}
                  onChange={() => handleToggle("groupMessages")}
                />
                <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Email Notifications */}
      <div className="p-4">
        <h3 className="text-[#333333] font-medium mb-3">Email Notifications</h3>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2]">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <Bell size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">System Updates</h3>
                <p className="text-xs text-[#797879]">
                  Emails about system updates and new features
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.systemUpdates}
                  onChange={() => handleToggle("systemUpdates")}
                />
                <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
              </label>
            </div>
          </div>

          <div className="p-4">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <Bell size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">Marketing Emails</h3>
                <p className="text-xs text-[#797879]">
                  Promotional emails and special offers
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={settings.marketingEmails}
                  onChange={() => handleToggle("marketingEmails")}
                />
                <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] shadow-md">
        <button
          className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white w-full py-4 rounded-full text-lg font-semibold shadow-md"
          onClick={handleSave}
        >
          Save Changes
        </button>
      </div>
    </div>
  );
}
