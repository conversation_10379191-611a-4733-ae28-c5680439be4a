"use client";

import {
  Bell,
  ChevronRight,
  CreditCard,
  FileText,
  HelpCircle,
  LogOut,
  Settings,
  Shield,
  Star,
  User,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

type ProfileTabsProps = {
  externalId: string;
};

export default function ProfileTabs({ externalId }: ProfileTabsProps) {
  const [activeTab, setActiveTab] = useState("account");
  const router = useRouter();
  const supportSections = [
    {
      id: "help",
      title: "Help Center",
      icon: <HelpCircle size={20} className="text-[#009639]" />,
      route: "/help",
    },
    {
      id: "privacy",
      title: "Privacy & Security",
      icon: <Shield size={20} className="text-[#009639]" />,
      route: "/privacy",
    },
    {
      id: "feedback",
      title: "Give Feedback",
      icon: <Star size={20} className="text-[#009639]" />,
      route: "/feedback",
    },
  ];
  const profileSections = [
    {
      id: "personal",
      title: "Personal Information",
      icon: <User size={20} className="text-[#009639]" />,
      route: "/profile/personal",
    },
    {
      id: "payment",
      title: "Payment Methods",
      icon: <CreditCard size={20} className="text-[#009639]" />,
      route: "/profile/payment",
    },
    {
      id: "documents",
      title: "Legal Documents",
      icon: <FileText size={20} className="text-[#009639]" />,
      route: `/profile/documents/${externalId}`,
    },
    {
      id: "notifications",
      title: "Notification Settings",
      icon: <Bell size={20} className="text-[#009639]" />,
      route: "/profile/notifications",
    },
    {
      id: "preferences",
      title: "App Preferences",
      icon: <Settings size={20} className="text-[#009639]" />,
      route: "/profile/preferences",
    },
  ];
  return (
    <>
      {/* Tabs */}
      <div className="px-4 mt-4">
        <div className="flex border-b border-[#f2f2f2] mb-4">
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "account"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("account")}
          >
            Account
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "support"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("support")}
          >
            Support
          </button>
        </div>
      </div>

      {/* Account Settings */}
      {activeTab === "account" && (
        <div className="px-4 pb-8">
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
            {profileSections.map((section, index) => (
              <div
                key={section.id}
                className={`p-4 flex items-center ${
                  index < profileSections.length - 1
                    ? "border-b border-[#f2f2f2]"
                    : ""
                }`}
                onClick={() => router.push(section.route)}
              >
                <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                  {section.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-[#333333] font-medium">
                    {section.title}
                  </h3>
                </div>
                <ChevronRight size={20} className="text-[#797879]" />
              </div>
            ))}
          </div>

          <button
            className="bg-white rounded-xl shadow-md w-full mt-4 p-4 flex items-center text-red-500"
            onClick={() => router.push("/logout")}
          >
            <div className="flex-1">
              <h3 className="text-red-500 font-medium">Log Out</h3>
            </div>
            <LogOut size={20} className="text-red-500 ml-2" />
          </button>
        </div>
      )}

      {/* Support Tab */}
      {activeTab === "support" && (
        <div className="px-4 pb-8">
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
            {supportSections.map((section, index) => (
              <div
                key={section.id}
                className={`p-4 flex items-center ${
                  index < supportSections.length - 1
                    ? "border-b border-[#f2f2f2]"
                    : ""
                }`}
                onClick={() => router.push(section.route)}
              >
                <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                  {section.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-[#333333] font-medium">
                    {section.title}
                  </h3>
                </div>
                <ChevronRight size={20} className="text-[#797879]" />
              </div>
            ))}
          </div>

          <div className="bg-white rounded-xl shadow-md p-4 mt-4 border border-gray-100">
            <div className="text-center">
              <p className="text-[#797879] text-sm mb-1">App Version</p>
              <p className="text-[#333333] font-medium">1.0.0 (Build 42)</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
