"use client";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
type BackElementProps = {
  title: string;
};
export default function BackElement({ title }: BackElementProps) {
  const router = useRouter();

  return (
    <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
      <button className="mr-4" onClick={() => router.back()}>
        <ArrowLeft size={24} className="text-white" />
      </button>
      <h1 className="text-xl font-bold text-white">{title}</h1>
    </div>
  );
}
//
