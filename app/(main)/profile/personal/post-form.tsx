"use client";
import { formatDateForInput } from "@/lib/utils";
import type { AddressTypeRead } from "@/types/address-type";
import type { ContactPointTypeRead } from "@/types/contact-point-types";
import type { ContactPointRead } from "@/types/contact-points";
import type { IndividualRead } from "@/types/individuals";
import { Calendar, Mail, MapPin, Phone } from "lucide-react";
import { startTransition, useActionState, useEffect, useState } from "react";
import FormSubmit from "./form-submit";

type PostFormProps = {
  action: (prevState: any, formData: FormData) => any;
  individual: IndividualRead;
  firstEmail: ContactPointRead | undefined;
  firstPhone: ContactPointRead | undefined;
  firstAddress: ContactPointRead | undefined;
  contactPointTypes: ContactPointTypeRead[];
  addressTypes: AddressTypeRead[];
};

export default function PostForm({
  individual,
  firstEmail,
  firstPhone,
  firstAddress,
  contactPointTypes,
  addressTypes,
  action,
}: PostFormProps) {
  const [state, formAction] = useActionState(action, {});
  const [email, setEmail] = useState<ContactPointRead | undefined>(undefined);
  const [phone, setPhone] = useState<ContactPointRead | undefined>(undefined);
  const [address, setAddress] = useState<ContactPointRead | undefined>(
    undefined
  );
  const [id, setId] = useState<number | undefined>(undefined);
  const [contactPointEmailId, setContactPointEmailId] = useState<
    number | undefined
  >(undefined);
  const [contactPointPhoneId, setContactPointPhoneId] = useState<
    number | undefined
  >(undefined);
  const [contactPointAddressId, setContactPointAddressId] = useState<
    number | undefined
  >(undefined);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    const deafaultContactPointEmailId = contactPointTypes?.find(
      (c) => c.name === "email"
    )?.id;
    const deafaultPhoneId = contactPointTypes?.find(
      (c) => c.name === "phone"
    )?.id;
    const deafaultAddressId = contactPointTypes?.find(
      (c) => c.name === "address"
    )?.id;

    const mainAddressId = addressTypes.find((c) => c.name === "Main")?.id;

    if (id) formData.set("id", String(id));
    if (email?.id) formData.set("emailId", String(email?.id));
    if (phone?.id) formData.set("phoneId", String(phone?.id));
    if (address?.id) formData.set("addressId", String(address?.id));
    formData.set(
      "contactPointEmailId",
      String(contactPointEmailId || deafaultContactPointEmailId)
    );
    formData.set(
      "contactPointPhoneId",
      String(contactPointPhoneId || deafaultPhoneId)
    );
    formData.set(
      "contactPointAddressId",
      String(contactPointAddressId || deafaultAddressId)
    );
    formData.set(
      "addressTypeId",
      String(address?.address_type_id || mainAddressId)
    );
    console.log("formDataformData", formData);
    startTransition(() => {
      formAction(formData);
    });
  };

  useEffect(() => {
    setEmail(firstEmail);
  }, [firstEmail]);

  useEffect(() => {
    setPhone(firstPhone);
  }, [firstPhone]);

  useEffect(() => {
    setAddress(firstAddress);
  }, [firstAddress]);

  useEffect(() => {
    if (individual) setId(individual?.id);
  }, [individual]);

  useEffect(() => {
    if (email) setContactPointEmailId(email?.contact_point_type_id);
  }, [email]);

  useEffect(() => {
    if (phone) setContactPointPhoneId(phone?.contact_point_type_id);
  }, [phone]);

  useEffect(() => {
    if (address) setContactPointAddressId(address?.contact_point_type_id);
  }, [address]);

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-w-md mx-auto">
      {state?.errors && (
        <div className="bg-red-100 text-red-700 p-4 rounded">
          <ul>
            {Object.entries(state.errors).map(([field, messages]) =>
              Array.isArray(messages)
                ? messages.map((msg, i) => <li key={`${field}-${i}`}>{msg}</li>)
                : null
            )}
          </ul>
        </div>
      )}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 space-y-4 border border-gray-100">
          <div>
            <label className="text-[#797879] text-xs mb-1 flex">
              First Name
            </label>
            <input
              type="text"
              name="firstName"
              defaultValue={individual.first_name}
              className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
            />
          </div>
          <div>
            <label className="text-[#797879] text-xs mb-1 flex">
              Last Name
            </label>
            <input
              type="text"
              name="lastName"
              defaultValue={individual.last_name}
              className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
            />
          </div>
          <div>
            <label className="text-[#797879] text-xs mb-1 flex items-center">
              <Mail size={14} className="mr-1 text-[#009639]" /> Email Address
            </label>
            <input
              type="email"
              name="email"
              defaultValue={firstEmail?.value}
              className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
            />
          </div>

          <div>
            <label className="text-[#797879] text-xs mb-1 flex items-center">
              <Phone size={14} className="mr-1 text-[#009639]" /> Phone Number
            </label>
            <input
              defaultValue={firstPhone?.value}
              type="tel"
              name="phone"
              className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
            />
          </div>

          <div>
            <label className="text-[#797879] text-xs mb-1 flex items-center">
              <MapPin size={14} className="mr-1 text-[#009639]" /> Address
            </label>
            <input
              defaultValue={firstAddress?.value}
              type="text"
              name="address"
              className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
            />
          </div>

          <div>
            <label className="text-[#797879] text-xs mb-1 flex items-center">
              <Calendar size={14} className="mr-1 text-[#009639]" /> Date of
              Birth
            </label>
            <input
              defaultValue={formatDateForInput(individual?.birth_date)}
              type="date"
              name="dateOfBirth"
              className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
            />
          </div>
        </div>
      </div>
      <FormSubmit />
    </form>
  );
}
