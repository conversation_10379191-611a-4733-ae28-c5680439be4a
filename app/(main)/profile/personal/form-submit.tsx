"use client";

import { useFormStatus } from "react-dom";

export default function FormSubmit() {
  const status = useFormStatus();
  if (status.pending) {
    return (
      <button
        className="bg-[#009639] text-white px-6 py-2 rounded-xl shadow hover:bg-[#007A2F]"
        type="submit"
        disabled
      >
        <div className="border-t-4 border-b-4 border-white w-6 h-6 border-solid rounded-full animate-spin"></div>
      </button>
    );
  }

  return (
    <button
      type="submit"
      className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white w-full py-4 rounded-full text-lg font-semibold shadow-md"
    >
      Save Changes
    </button>
  );
}
