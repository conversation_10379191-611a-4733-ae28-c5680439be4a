"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft, Globe, Sun, Clock } from "lucide-react";

export default function AppPreferencesScreen() {
  const router = useRouter();

  // Mock data - in a real app, this would come from an API
  const [preferences, setPreferences] = useState({
    language: "english",
    theme: "light",
    timeFormat: "24h",
    distanceUnit: "kilometers",
    currencyFormat: "zar",
  });

  const handleChange = (setting: keyof typeof preferences, value: string) => {
    setPreferences({
      ...preferences,
      [setting]: value,
    });
  };

  const handleSave = () => {
    console.log("Saving app preferences:", preferences);
    // In a real app, you would make an API call here
    router.push("/profile");
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#eef1f9"
              />
            </svg>
          </div>
        </div>
      </div> */}

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">App Preferences</h1>
      </div>

      {/* Language */}
      <div className="p-4">
        <div className="flex items-center mb-2">
          <Globe size={18} className="text-[#009639] mr-2" />
          <h3 className="text-[#333333] font-medium">Language</h3>
        </div>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div
            className={`p-4 border-b border-[#f2f2f2] flex items-center ${
              preferences.language === "english" ? "bg-[#f9fbff]" : ""
            }`}
            onClick={() => handleChange("language", "english")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333]">English</h3>
            </div>
            {preferences.language === "english" && (
              <div className="w-5 h-5 rounded-full bg-[#009639] flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
            )}
          </div>

          <div
            className={`p-4 border-b border-[#f2f2f2] flex items-center ${
              preferences.language === "afrikaans" ? "bg-[#f9fbff]" : ""
            }`}
            onClick={() => handleChange("language", "afrikaans")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333]">Afrikaans</h3>
            </div>
            {preferences.language === "afrikaans" && (
              <div className="w-5 h-5 rounded-full bg-[#009639] flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
            )}
          </div>

          <div
            className={`p-4 flex items-center ${
              preferences.language === "zulu" ? "bg-[#f9fbff]" : ""
            }`}
            onClick={() => handleChange("language", "zulu")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333]">Zulu</h3>
            </div>
            {preferences.language === "zulu" && (
              <div className="w-5 h-5 rounded-full bg-[#009639] flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Theme */}
      <div className="p-4">
        <div className="flex items-center mb-2">
          <Sun size={18} className="text-[#009639] mr-2" />
          <h3 className="text-[#333333] font-medium">Theme</h3>
        </div>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div
            className={`p-4 border-b border-[#f2f2f2] flex items-center ${
              preferences.theme === "light" ? "bg-[#f9fbff]" : ""
            }`}
            onClick={() => handleChange("theme", "light")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333]">Light</h3>
            </div>
            {preferences.theme === "light" && (
              <div className="w-5 h-5 rounded-full bg-[#009639] flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
            )}
          </div>

          <div
            className={`p-4 border-b border-[#f2f2f2] flex items-center ${
              preferences.theme === "dark" ? "bg-[#f9fbff]" : ""
            }`}
            onClick={() => handleChange("theme", "dark")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333]">Dark</h3>
            </div>
            {preferences.theme === "dark" && (
              <div className="w-5 h-5 rounded-full bg-[#009639] flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
            )}
          </div>

          <div
            className={`p-4 flex items-center ${
              preferences.theme === "system" ? "bg-[#f9fbff]" : ""
            }`}
            onClick={() => handleChange("theme", "system")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333]">System Default</h3>
            </div>
            {preferences.theme === "system" && (
              <div className="w-5 h-5 rounded-full bg-[#009639] flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Time Format */}
      <div className="p-4">
        <div className="flex items-center mb-2">
          <Clock size={18} className="text-[#009639] mr-2" />
          <h3 className="text-[#333333] font-medium">Time Format</h3>
        </div>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div
            className={`p-4 border-b border-[#f2f2f2] flex items-center ${
              preferences.timeFormat === "12h" ? "bg-[#f9fbff]" : ""
            }`}
            onClick={() => handleChange("timeFormat", "12h")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333]">12-hour (AM/PM)</h3>
            </div>
            {preferences.timeFormat === "12h" && (
              <div className="w-5 h-5 rounded-full bg-[#009639] flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
            )}
          </div>

          <div
            className={`p-4 flex items-center ${
              preferences.timeFormat === "24h" ? "bg-[#f9fbff]" : ""
            }`}
            onClick={() => handleChange("timeFormat", "24h")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333]">24-hour</h3>
            </div>
            {preferences.timeFormat === "24h" && (
              <div className="w-5 h-5 rounded-full bg-[#009639] flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Distance Unit */}
      <div className="p-4">
        <div className="flex items-center mb-2">
          <Globe size={18} className="text-[#009639] mr-2" />
          <h3 className="text-[#333333] font-medium">Distance Unit</h3>
        </div>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div
            className={`p-4 border-b border-[#f2f2f2] flex items-center ${
              preferences.distanceUnit === "miles" ? "bg-[#f9fbff]" : ""
            }`}
            onClick={() => handleChange("distanceUnit", "miles")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333]">Miles</h3>
            </div>
            {preferences.distanceUnit === "miles" && (
              <div className="w-5 h-5 rounded-full bg-[#009639] flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
            )}
          </div>

          <div
            className={`p-4 flex items-center ${
              preferences.distanceUnit === "kilometers" ? "bg-[#f9fbff]" : ""
            }`}
            onClick={() => handleChange("distanceUnit", "kilometers")}
          >
            <div className="flex-1">
              <h3 className="text-[#333333]">Kilometers</h3>
            </div>
            {preferences.distanceUnit === "kilometers" && (
              <div className="w-5 h-5 rounded-full bg-[#009639] flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-white"></div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] shadow-md">
        <button
          className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white w-full py-4 rounded-full text-lg font-semibold shadow-md"
          onClick={handleSave}
        >
          Save Changes
        </button>
      </div>
    </div>
  );
}
