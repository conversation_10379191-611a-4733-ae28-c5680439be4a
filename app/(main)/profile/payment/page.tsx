"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  CreditCard,
  Plus,
  ChevronRight,
  CheckCircle,
  Trash2,
} from "lucide-react";

export default function PaymentMethodsScreen() {
  const router = useRouter();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedCardId, setSelectedCardId] = useState<number | null>(null);

  // Mock data - in a real app, this would come from an API
  const [paymentMethods, setPaymentMethods] = useState([
    {
      id: 1,
      type: "credit",
      cardNumber: "**** **** **** 4242",
      expiryDate: "05/25",
      cardHolder: "Thandi Dlamini",
      isDefault: true,
      brand: "Visa",
    },
    {
      id: 2,
      type: "credit",
      cardNumber: "**** **** **** 5555",
      expiryDate: "08/24",
      cardHolder: "Thandi Dlamini",
      isDefault: false,
      brand: "Mastercard",
    },
  ]);

  const handleSetDefault = (id: number) => {
    setPaymentMethods(
      paymentMethods.map((method) => ({
        ...method,
        isDefault: method.id === id,
      }))
    );
  };

  const handleDeleteCard = (id: number) => {
    setSelectedCardId(id);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = () => {
    if (selectedCardId) {
      setPaymentMethods(
        paymentMethods.filter((method) => method.id !== selectedCardId)
      );
    }
    setShowDeleteConfirm(false);
    setSelectedCardId(null);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
    setSelectedCardId(null);
  };

  const getCardLogo = (brand: string) => {
    switch (brand.toLowerCase()) {
      case "visa":
        return (
          <div className="w-10 h-6 bg-[#1434CB] rounded-md flex items-center justify-center text-white font-bold text-xs shadow-sm">
            VISA
          </div>
        );
      case "mastercard":
        return (
          <div className="w-10 h-6 bg-[#EB001B] rounded-md flex items-center justify-center text-white font-bold text-xs shadow-sm">
            MC
          </div>
        );
      default:
        return (
          <div className="w-10 h-6 bg-[#333333] rounded-md flex items-center justify-center text-white font-bold text-xs shadow-sm">
            CARD
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#eef1f9"
              />
            </svg>
          </div>
        </div>
      </div> */}

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Payment Methods</h1>
      </div>

      {/* Payment Methods */}
      <div className="p-4">
        <h3 className="text-[#333333] font-medium mb-3">Your Cards</h3>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          {paymentMethods.map((method) => (
            <div
              key={method.id}
              className="p-4 border-b border-[#f2f2f2] last:border-b-0"
            >
              <div className="flex items-center">
                <div className="mr-3">{getCardLogo(method.brand)}</div>
                <div className="flex-1">
                  <div className="flex items-center">
                    <h3 className="text-[#333333] font-medium">
                      {method.cardNumber}
                    </h3>
                    {method.isDefault && (
                      <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-[#e6f3ff] text-[#0286ff]">
                        Default
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-[#797879]">
                    Expires {method.expiryDate}
                  </p>
                </div>
                <div className="flex space-x-2">
                  {!method.isDefault && (
                    <button
                      className="text-[#009639]"
                      onClick={() => handleSetDefault(method.id)}
                    >
                      <CheckCircle size={20} />
                    </button>
                  )}
                  <button
                    className="text-red-500 ml-2"
                    onClick={() => handleDeleteCard(method.id)}
                  >
                    <Trash2 size={20} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <button
          className="bg-white rounded-xl shadow-md w-full mt-4 p-4 flex items-center justify-center text-[#009639] border border-gray-100"
          onClick={() => router.push("/add-payment-method")}
        >
          <Plus size={20} className="mr-2" />
          <span className="font-medium">Add New Card</span>
        </button>
      </div>

      {/* Transaction History */}
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-[#333333] font-medium">Recent Transactions</h3>
          <button
            className="text-[#009639] text-sm font-medium flex items-center"
            onClick={() => router.push("/transaction-history")}
          >
            View All <ChevronRight size={16} className="text-[#009639]" />
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2]">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                <CreditCard size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">
                  Monthly Contribution
                </h3>
                <p className="text-xs text-[#797879]">May 15, 2023</p>
              </div>
              <div className="text-[#333333] font-bold">R1,200.00</div>
            </div>
          </div>

          <div className="p-4">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                <CreditCard size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">
                  Vehicle Maintenance
                </h3>
                <p className="text-xs text-[#797879]">May 3, 2023</p>
              </div>
              <div className="text-[#333333] font-bold">R450.00</div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 px-4">
          <div className="bg-white rounded-xl p-6 w-full max-w-sm shadow-lg border border-gray-100">
            <h3 className="text-[#333333] font-bold text-lg mb-2">
              Remove Card
            </h3>
            <p className="text-[#797879] mb-6">
              Are you sure you want to remove this card? This action cannot be
              undone.
            </p>
            <div className="flex space-x-3">
              <button
                className="flex-1 py-3 px-4 border border-[#d6d9dd] rounded-full text-[#333333] font-medium shadow-sm"
                onClick={cancelDelete}
              >
                Cancel
              </button>
              <button
                className="flex-1 py-3 px-4 bg-red-500 text-white rounded-full font-medium shadow-md"
                onClick={confirmDelete}
              >
                Remove
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
