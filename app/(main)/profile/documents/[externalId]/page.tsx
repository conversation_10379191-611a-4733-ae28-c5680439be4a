"use server";

import { getPartyByExternalID } from "@/actions/party";
import { getPartyIdentificationsByParty } from "@/actions/party-identifications";
import MissingParty from "@/components/missing-party";
import DocumentsDisplay from "./documents-display";
interface UploadDocumentsPageProps {
  params: {
    externalId: string;
  };
}
export default async function LegalDocumentsScreen({
  params,
}: UploadDocumentsPageProps) {
  const { externalId } = await params;
  if (!externalId) return <MissingParty />;
  const party = await getPartyByExternalID(externalId);
  if (!(party && party.id)) return <MissingParty />;
  const documents = await getPartyIdentificationsByParty(party.id);

  return <DocumentsDisplay documents={documents} externalId={externalId} />;
}
