"use client";

import {
  formatDateForInput,
  generateDocumentUrl,
  getFileExtension,
} from "@/lib/utils";
import type { PartyIdentificationRead } from "@/types/party-identifications";
import { ChevronRight, Download, Eye, FileText, Upload } from "lucide-react";
import { redirect } from "next/navigation";
import BackElement from "../../personal/back-element";
interface DocumentsDisplayProps {
  documents: PartyIdentificationRead[];
  externalId: string;
}
export default function DocumentsDisplay({
  documents,
  externalId,
}: DocumentsDisplayProps) {
  const getStatusBadge = (is_verified: boolean | null | undefined) => {
    switch (is_verified) {
      case true:
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#009639] font-medium">
            Verified
          </span>
        );
      case false:
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#fff9e6] text-[#7A5A00] font-medium">
            Pending
          </span>
        );
      default:
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6f7ff] text-[#0070a8] font-medium">
            Uploaded
          </span>
        );
    }
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      <BackElement title="Legal Documents" />
      {/* Documents */}
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-[#333333] font-medium">Your Documents</h3>
          <button
            className="text-[#009639] text-sm font-medium flex items-center"
            onClick={() => redirect(`/upload-document/${externalId}`)}
          >
            <Upload size={16} className="mr-1 text-[#009639]" /> Upload
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          {documents?.map((doc) => (
            <div
              key={doc.id}
              className="p-4 border-b border-[#f2f2f2] last:border-b-0"
            >
              <div className="flex flex-col">
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                    <FileText size={18} className="text-[#009639]" />
                  </div>
                  <div className="flex-1">
                    <div className="flex flex-col">
                      <div className="flex items-center mb-1">
                        <h3 className="text-[#333333] font-medium">
                          {doc.identification_type.name}
                        </h3>
                      </div>
                      <div className="flex items-center justify-between text-xs text-[#797879]">
                        <div className="flex items-center">
                          <span className="mr-2">
                            {getFileExtension(doc?.document_image_url)}
                          </span>
                          <span className="mr-2">•</span>
                          <span>{formatDateForInput(doc.created_at)}</span>
                        </div>
                        <div>{getStatusBadge(doc?.is_verified)}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex mt-3 ml-12 border-t border-gray-100 pt-2">
                  <button
                    type="button"
                    onClick={async () => {
                      try {
                        const url = await generateDocumentUrl(
                          doc.document_image_url
                        );
                        window.open(url, "_blank");
                      } catch (error) {
                        console.error("Failed to generate URL:", error);
                        alert("Could not open document.");
                      }
                    }}
                    className="flex items-center text-[#009639] mr-4 text-sm border border-[#009639] px-3 py-1 rounded-full"
                  >
                    <Eye size={16} className="mr-1" /> View
                  </button>
                  <button
                    type="button"
                    onClick={async () => {
                      try {
                        const url = await generateDocumentUrl(
                          doc.document_image_url
                        );
                        window.open(url, "_blank");
                      } catch (error) {
                        console.error("Failed to generate URL:", error);
                        alert("Could not open document.");
                      }
                    }}
                    className="flex items-center text-white text-sm bg-gradient-to-r from-[#009639] to-[#007A2F] px-3 py-1 rounded-full shadow-sm"
                  >
                    <Download size={16} className="mr-1" /> Download
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Required Documents */}
      {/* <div className="p-4">
        <h3 className="text-[#333333] font-medium mb-3">Required Documents</h3>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2]">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                <FileText size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">
                  Vehicle Registration (NATIS)
                </h3>
                <p className="text-xs text-[#797879]">
                  Upload your South African vehicle registration document
                </p>
              </div>
              <button
                className="text-[#009639]"
                onClick={() => redirect(`/upload-document/${externalId}`)}
              >
                <Plus size={20} />
              </button>
            </div>
          </div>

          <div className="p-4">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                <FileText size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">
                  Proof of Insurance (South African)
                </h3>
                <p className="text-xs text-[#797879]">
                  Upload your South African insurance policy document
                </p>
              </div>
              <button
                className="text-[#009639]"
                onClick={() => redirect(`/upload-document/${externalId}`)}
              >
                <Plus size={20} />
              </button>
            </div>
          </div>
        </div>
      </div> */}

      {/* Entity Documents */}
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-[#333333] font-medium">Entity Documents</h3>
          <button
            className="text-[#009639] text-sm font-medium flex items-center"
            onClick={() => redirect("/entity-documents")}
          >
            View All <ChevronRight size={16} className="text-[#009639]" />
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <p className="text-[#797879] text-sm mb-3">
            Access legal documents related to your South African vehicle sharing
            entities
          </p>
          <button
            className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white w-full py-3 rounded-full text-sm shadow-md"
            onClick={() => redirect("/entity-documents")}
          >
            View Entity Documents
          </button>
        </div>
      </div>
    </div>
  );
}
