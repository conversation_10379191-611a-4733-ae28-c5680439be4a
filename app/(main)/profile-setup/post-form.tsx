"use client";
import { DocumentUpload } from "@/lib/utils";
import type { AddressTypeRead } from "@/types/address-type";
import type { ContactPointTypeRead } from "@/types/contact-point-types";
import type { IdentificationTypeRead } from "@/types/identification-types";
import type { PartyStatusRead } from "@/types/party-statuses";
import type { PartyTypesRead } from "@/types/party-types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Calendar, CreditCard, Home, Upload } from "lucide-react";
import Image from "next/image";
import {
  useEffect,
  useRef,
  useState,
  useActionState,
  startTransition,
} from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import FormSubmit from "./form-submit";

const formSchema = z.object({
  dateOfBirth: z.string().min(1, "Date of Birth is required"),
  idNumber: z.string().min(1, "ID/Passport Number is required"),
  address: z.string().min(1, "Physical Address is required"),
  image: z.string().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  email: z.string().optional(),
  phone: z.string().optional(),
  sub: z.string().optional(),
  activePartyTypeId: z.string().optional(),
  emailId: z.string().optional(),
  phoneId: z.string().optional(),
  addressId: z.string().optional(),
  profilePicId: z.string().optional(),
  nationalId: z.string().optional(),
  mainAddressId: z.string().optional(),
  activePartyStatusId: z.string().optional(),
});

type PostFormProps = {
  action: (prevState: any, formData: FormData) => any;
  contactPointTypes: ContactPointTypeRead[];
  partyTypes: PartyTypesRead[];
  partyStatuses: PartyStatusRead[];
  identificationTypes: IdentificationTypeRead[];
  addressTypes: AddressTypeRead[];
  attributes: any;
};

export default function PostForm(props: PostFormProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [error, setError] = useState("");
  const [state, formAction] = useActionState(props.action, {});

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      dateOfBirth: "",
      idNumber: "",
      address: "",
      image: "",
      firstName: undefined,
      lastName: undefined,
      email: undefined,
      phone: undefined,
      sub: undefined,
      activePartyTypeId: undefined,
      emailId: undefined,
      phoneId: undefined,
      addressId: undefined,
      profilePicId: undefined,
      nationalId: undefined,
      mainAddressId: undefined,
      activePartyStatusId: undefined,
    },
  });

  useEffect(() => {
    const checkUser = async () => {
      try {
        if (props.attributes) {
          form.setValue("firstName", props.attributes?.given_name);
          form.setValue("lastName", props.attributes?.family_name);
          form.setValue("email", props.attributes?.email);
          form.setValue("phone", props.attributes?.phone_number);
          form.setValue("sub", props.attributes?.sub);
        }
      } catch (error: any) {
        setError(error?.message || "An error occurred. Please try again.");
      }
    };

    checkUser();
  }, [props.attributes, form]);

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = e.target.files?.[0];
      if (file) {
        const r = await DocumentUpload(file);
        if (r && r.path) {
          form.setValue("image", r.path);
        }
        const reader = new FileReader();
        reader.onload = (e) => {
          setProfileImage(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      }
    } catch (err: any) {
      setError(err.message || "An error occurred. Please try again.");
    }
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    const formData = new FormData();
    const activePartyTypeId = props.partyTypes.find(
      (p) => p.name === "Individual"
    )?.id;
    const emailId = props.contactPointTypes?.find(
      (c) => c.name === "email"
    )?.id;
    const phoneId = props.contactPointTypes?.find(
      (c) => c.name === "phone"
    )?.id;
    const addressId = props.contactPointTypes?.find(
      (c) => c.name === "address"
    )?.id;
    const profilePicId = props.identificationTypes?.find(
      (c) => c.name === "ProfilePicture"
    )?.id;
    const nationalId = props.identificationTypes?.find(
      (c) => c.name === "nationalId"
    )?.id;
    const mainAddressId = props.addressTypes?.find(
      (c) => c.name === "Main"
    )?.id;
    const activePartyStatusId = props.partyStatuses?.find(
      (c) => c.name === "Active"
    )?.id;

    Object.entries(values).forEach(([key, value]) => {
      if (value) formData.set(key, value);
    });

    if (activePartyTypeId)
      formData.set("activePartyTypeId", String(activePartyTypeId));
    if (emailId) formData.set("emailId", String(emailId));
    if (phoneId) formData.set("phoneId", String(phoneId));
    if (addressId) formData.set("addressId", String(addressId));
    if (profilePicId) formData.set("profilePicId", String(profilePicId));
    if (nationalId) formData.set("nationalId", String(nationalId));
    if (mainAddressId) formData.set("mainAddressId", String(mainAddressId));
    if (activePartyStatusId)
      formData.set("activePartyStatusId", String(activePartyStatusId));
    startTransition(() => {
      formAction(formData);
    });
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6 max-w-md mx-auto p-4"
      >
        {state?.errors && (
          <div className="bg-red-100 text-red-700 p-4 rounded">
            <ul>
              {Object.entries(state.errors).map(([field, messages]) =>
                Array.isArray(messages)
                  ? messages.map((msg, i) => (
                      <li key={`${field}-${i}`}>{msg}</li>
                    ))
                  : null
              )}
            </ul>
          </div>
        )}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 text-sm">
            {error}
          </div>
        )}
        <div className="flex flex-col items-center mb-8">
          <div
            className="relative w-32 h-32 rounded-full bg-white border-2 border-[#009639]y flex items-center justify-center overflow-hidden cursor-pointer mb-2 shadow-md"
            onClick={() => fileInputRef.current?.click()}
          >
            {profileImage ? (
              <Image
                src={profileImage || "/placeholder.svg"}
                alt="Profile"
                fill
                className="object-cover"
              />
            ) : (
              <Upload size={32} className="text-[#009639]" />
            )}
          </div>
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleImageUpload}
          />
          <p className="text-[#797879] text-sm">Upload profile picture</p>
        </div>

        <FormField
          control={form.control}
          name="dateOfBirth"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center text-foreground font-medium">
                <Calendar size={18} className="mr-2 text-[#009639]" />
                Date of Birth
              </FormLabel>
              <FormControl>
                <Input
                  type="date"
                  {...field}
                  className="w-full rounded-xl border shadow-sm"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="idNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center text-foreground font-medium">
                <CreditCard size={18} className="mr-2 text-[#009639]" />
                ID/Passport Number
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter your (Non) South African ID number"
                  {...field}
                  className="w-full rounded-xl border shadow-sm"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center text-foreground font-medium">
                <Home size={18} className="mr-2 text-[#009639]" />
                Physical Address
              </FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Enter your South African address"
                  {...field}
                  className="w-full rounded-xl border shadow-sm"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormSubmit />
      </form>
    </Form>
  );
}
