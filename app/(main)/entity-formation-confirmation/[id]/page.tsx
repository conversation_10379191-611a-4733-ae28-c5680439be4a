"use client";

import React, { use } from "react";
import { useRouter } from "next/navigation";
import {
  CheckCircle,
  Calendar,
  Building,
  FileText,
  Users,
  MapPin,
  ChevronRight,
} from "lucide-react";

export default function EntityFormationConfirmationScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}): React.ReactNode {
  const router = useRouter();
  const entityId = use(params).id;

  // Mock data - in a real app, this would come from an API
  const entity = {
    id: entityId,
    name:
      entityId === "1"
        ? "Family SUV (Pty) Ltd"
        : entityId === "2"
        ? "Weekend Getaway NPC"
        : "Work Commute Cooperative",
    type:
      entityId === "1"
        ? "Private Company"
        : entityId === "2"
        ? "Non-Profit Company"
        : "Cooperative",
    formationDate: new Date().toISOString(),
    registrationNumber: `REG-${Math.floor(100000 + Math.random() * 900000)}`,
    province:
      entityId === "1"
        ? "Western Cape"
        : entityId === "2"
        ? "Gauteng"
        : "KwaZulu-Natal",
    address:
      entityId === "1"
        ? "123 Beach Road, Cape Town"
        : entityId === "2"
        ? "456 Main Street, Johannesburg"
        : "789 Marine Drive, Durban",
    directors: [
      {
        name: "Jane Cooper",
        role: "Director",
        ownership: entityId === "3" ? 20 : 33.33,
      },
      {
        name: "Robert Fox",
        role: "Director",
        ownership: entityId === "3" ? 20 : 33.33,
      },
      {
        name: "Esther Howard",
        role: "Director",
        ownership: entityId === "3" ? 20 : 33.34,
      },
    ],
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-ZA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const handleBackHome = () => {
    router.push("/home");
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Success Message */}
      <div className="bg-[#009639] px-6 py-8 flex flex-col items-center border-b border-[#007A2F]">
        <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
          <CheckCircle size={40} className="text-[#009639]" />
        </div>
        <h1 className="text-2xl font-bold text-white mb-2">
          Entity Formation Complete!
        </h1>
        <p className="text-white text-center">
          Your legal entity has been successfully formed
        </p>
      </div>

      {/* Entity Details */}
      <div className="px-4 py-4">
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
          <div className="flex items-center p-4 border-b border-gray-100">
            <div className="w-14 h-14 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
              <Building size={28} className="text-[#009639]" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-[#333333]">
                {entity.name}
              </h2>
              <p className="text-[#797879]">{entity.type}</p>
            </div>
          </div>

          <div className="p-4">
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <FileText size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Registration Number</p>
                  <p className="text-[#333333]">{entity.registrationNumber}</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Calendar size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Formation Date</p>
                  <p className="text-[#333333]">
                    {formatDate(entity.formationDate)}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <MapPin size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Province</p>
                  <p className="text-[#333333]">{entity.province}</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <MapPin size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Business Address</p>
                  <p className="text-[#333333]">{entity.address}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Directors & Ownership */}
      <div className="px-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">
            Directors & Ownership
          </h3>

          <div className="space-y-3">
            {entity.directors.map((director, index) => (
              <div
                key={index}
                className="p-3 bg-[#f9f9f9] rounded-lg flex items-center justify-between"
              >
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                    <Users size={16} className="text-[#009639]" />
                  </div>
                  <div>
                    <p className="text-[#333333] font-medium">
                      {director.name}
                    </p>
                    <p className="text-xs text-[#797879]">{director.role}</p>
                  </div>
                </div>
                <p className="text-[#009639] font-medium">
                  {director.ownership}%
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <div className="px-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">What's Next?</h3>

          <div className="space-y-4">
            <div className="flex items-start">
              <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <span className="text-sm font-medium text-[#009639]">1</span>
              </div>
              <div className="flex-1">
                <p className="text-[#333333]">Set up a business bank account</p>
                <div className="h-6 w-0.5 bg-[#e6ffe6] ml-3 my-1"></div>
              </div>
            </div>

            <div className="flex items-start">
              <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <span className="text-sm font-medium text-[#009639]">2</span>
              </div>
              <div className="flex-1">
                <p className="text-[#333333]">Register for tax with SARS</p>
                <div className="h-6 w-0.5 bg-[#e6ffe6] ml-3 my-1"></div>
              </div>
            </div>

            <div className="flex items-start">
              <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <span className="text-sm font-medium text-[#009639]">3</span>
              </div>
              <div className="flex-1">
                <p className="text-[#333333]">
                  Complete compliance requirements
                </p>
                <div className="h-6 w-0.5 bg-[#e6ffe6] ml-3 my-1"></div>
              </div>
            </div>

            <div className="flex items-start">
              <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <span className="text-sm font-medium text-[#009639]">4</span>
              </div>
              <div className="flex-1">
                <p className="text-[#333333]">Start operating your business</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Information Note */}
      <div className="px-4">
        <div className="p-4 bg-[#e6ffe6] rounded-xl mb-4">
          <p className="text-[#007A2F] text-sm">
            Important documents have been sent to your email. You can also
            access them in the Documents section of your profile.
          </p>
        </div>
      </div>

      {/* View Entity Details Button */}
      <div className="px-4 mb-4">
        <button
          className="w-full py-4 border border-[#009639] text-[#009639] rounded-full text-lg font-semibold"
          onClick={() => router.push(`/compliance-dashboard`)}
        >
          View Compliance Dashboard
        </button>
      </div>

      {/* Return to Home Button */}
      <div className="px-4 pb-8">
        <button
          className="w-full py-4 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm"
          onClick={handleBackHome}
        >
          Return to Home
        </button>
      </div>
    </div>
  );
}
