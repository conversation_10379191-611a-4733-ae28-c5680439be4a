"use client";

import { useState, use, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Mail,
  Phone,
  MapPin,
  Calendar,
  User,
  MessageSquare,
} from "lucide-react";
import { getGroupMember } from "../../../../drizzle-actions/community";

interface MemberDetails {
  id: number;
  name: string;
  email: string;
  phone: string;
  avatar: string;
}

export default function MemberDetailsScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const memberId = parseInt(use(params).id);
  const [member, setMember] = useState<MemberDetails | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMemberData = async () => {
      try {
        setLoading(true);
        const memberDetails = await getGroupMember(memberId);
        if (memberDetails) {
          setMember(memberDetails);
        }
      } catch (error) {
        console.error("Failed to fetch member details:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchMemberData();
  }, [memberId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-[#f5f5f5]">
        {/* Header */}
        <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <div className="h-6 bg-white/20 rounded w-32 animate-pulse"></div>
        </div>
        
        {/* Loading Content */}
        <div className="p-6">
          <div className="bg-white rounded-xl p-6 text-center animate-pulse">
            <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-2 w-40 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-24 mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!member) {
    return (
      <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
        <div className="text-center">
          <p className="text-[#797879] mb-4">Member not found</p>
          <button
            className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
            onClick={() => router.back()}
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Member Details</h1>
      </div>

      {/* Member Profile */}
      <div className="p-6">
        <div className="bg-white rounded-xl p-6 text-center shadow-md border border-gray-100 mb-6">
          <div className="w-24 h-24 mx-auto mb-4 rounded-full overflow-hidden border-4 border-[#e6ffe6]">
            <Image
              src={member.avatar}
              alt={member.name}
              width={96}
              height={96}
              className="object-cover"
            />
          </div>
          <h2 className="text-2xl font-bold text-[#333333] mb-2">
            {member.name}
          </h2>
          <span className="inline-block bg-[#e6ffe6] text-[#009639] px-3 py-1 rounded-full text-sm font-medium">
            Group Member
          </span>
        </div>

        {/* Contact Information */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 mb-6">
          <div className="p-4 border-b border-[#f2f2f2]">
            <h3 className="text-lg font-semibold text-[#333333]">
              Contact Information
            </h3>
          </div>
          
          <div className="p-4 space-y-4">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <Mail size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-[#797879]">Email</p>
                <p className="text-[#333333] font-medium">{member.email}</p>
              </div>
            </div>

            <div className="flex items-center">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <Phone size={18} className="text-[#009639]" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-[#797879]">Phone</p>
                <p className="text-[#333333] font-medium">{member.phone}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2]">
            <h3 className="text-lg font-semibold text-[#333333]">
              Quick Actions
            </h3>
          </div>
          
          <div className="p-4 space-y-3">
            <button 
              className="w-full flex items-center p-3 bg-[#f8f9fa] rounded-lg hover:bg-[#e6ffe6] transition-colors"
              onClick={() => window.location.href = `mailto:${member.email}`}
            >
              <div className="w-10 h-10 bg-[#009639] rounded-full flex items-center justify-center mr-3">
                <Mail size={18} className="text-white" />
              </div>
              <div className="flex-1 text-left">
                <p className="font-medium text-[#333333]">Send Email</p>
                <p className="text-sm text-[#797879]">Contact via email</p>
              </div>
            </button>

            <button 
              className="w-full flex items-center p-3 bg-[#f8f9fa] rounded-lg hover:bg-[#e6ffe6] transition-colors"
              onClick={() => window.location.href = `tel:${member.phone}`}
            >
              <div className="w-10 h-10 bg-[#009639] rounded-full flex items-center justify-center mr-3">
                <Phone size={18} className="text-white" />
              </div>
              <div className="flex-1 text-left">
                <p className="font-medium text-[#333333]">Call Member</p>
                <p className="text-sm text-[#797879]">Make a phone call</p>
              </div>
            </button>

            <button 
              className="w-full flex items-center p-3 bg-[#f8f9fa] rounded-lg hover:bg-[#e6ffe6] transition-colors"
              onClick={() => router.push(`/chat/${member.id}`)}
            >
              <div className="w-10 h-10 bg-[#009639] rounded-full flex items-center justify-center mr-3">
                <MessageSquare size={18} className="text-white" />
              </div>
              <div className="flex-1 text-left">
                <p className="font-medium text-[#333333]">Send Message</p>
                <p className="text-sm text-[#797879]">Start a conversation</p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
