"use client";

import React, { use } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  CheckCircle,
  Calendar,
  Clock,
  ChevronRight,
  Building,
  FileText,
} from "lucide-react";

export default function ApplicationSubmittedScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}): React.ReactNode {
  const router = useRouter();
  const opportunityId = use(params).id;

  // Mock data - in a real app, this would come from an API
  const opportunity = {
    id: opportunityId,
    title:
      opportunityId === "1"
        ? "Corporate Fleet Partnership"
        : opportunityId === "2"
        ? "Weekend Tour Service"
        : "Delivery Service Partnership",
    company: {
      name:
        opportunityId === "1"
          ? "TechCorp Inc."
          : opportunityId === "2"
          ? "City Explorer Tours"
          : "Swift Delivery",
      logo: "/placeholder.svg?height=60&width=60",
      location: "Cape Town, Western Cape",
    },
    applicationDate: new Date().toISOString(),
    expectedResponseTime: "3-5 business days",
    applicationId: `APP-${Math.floor(100000 + Math.random() * 900000)}`,
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-ZA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const handleBackHome = () => {
    router.push("/home");
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Success Message */}
      <div className="bg-[#009639] px-6 py-8 flex flex-col items-center border-b border-[#007A2F]">
        <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
          <CheckCircle size={40} className="text-[#009639]" />
        </div>
        <h1 className="text-2xl font-bold text-white mb-2">
          Application Submitted!
        </h1>
        <p className="text-white text-center">
          Your application has been successfully submitted
        </p>
      </div>

      {/* Application Details */}
      <div className="px-4 py-4">
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
          <div className="flex items-center p-4 border-b border-gray-100">
            <div className="w-14 h-14 bg-white rounded-full border-2 border-[#e6ffe6] overflow-hidden flex items-center justify-center mr-3 shadow-sm">
              <Image
                src={opportunity.company.logo}
                alt={opportunity.company.name}
                width={56}
                height={56}
                className="object-cover"
              />
            </div>
            <div>
              <h2 className="text-lg font-bold text-[#333333]">
                {opportunity.title}
              </h2>
              <p className="text-[#797879]">{opportunity.company.name}</p>
            </div>
          </div>

          <div className="p-4">
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <FileText size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Application ID</p>
                  <p className="text-[#333333]">{opportunity.applicationId}</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Calendar size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Submission Date</p>
                  <p className="text-[#333333]">
                    {formatDate(opportunity.applicationDate)}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Clock size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">
                    Expected Response Time
                  </p>
                  <p className="text-[#333333]">
                    {opportunity.expectedResponseTime}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Building size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Location</p>
                  <p className="text-[#333333]">
                    {opportunity.company.location}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <div className="px-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">What's Next?</h3>

          <div className="space-y-4">
            <div className="flex items-start">
              <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <span className="text-sm font-medium text-[#009639]">1</span>
              </div>
              <div className="flex-1">
                <p className="text-[#333333]">
                  Application review by {opportunity.company.name}
                </p>
                <div className="h-6 w-0.5 bg-[#e6ffe6] ml-3 my-1"></div>
              </div>
            </div>

            <div className="flex items-start">
              <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <span className="text-sm font-medium text-[#009639]">2</span>
              </div>
              <div className="flex-1">
                <p className="text-[#333333]">
                  Vehicle inspection (if applicable)
                </p>
                <div className="h-6 w-0.5 bg-[#e6ffe6] ml-3 my-1"></div>
              </div>
            </div>

            <div className="flex items-start">
              <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <span className="text-sm font-medium text-[#009639]">3</span>
              </div>
              <div className="flex-1">
                <p className="text-[#333333]">Contract review and signing</p>
                <div className="h-6 w-0.5 bg-[#e6ffe6] ml-3 my-1"></div>
              </div>
            </div>

            <div className="flex items-start">
              <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                <span className="text-sm font-medium text-[#009639]">4</span>
              </div>
              <div className="flex-1">
                <p className="text-[#333333]">
                  Onboarding and start of partnership
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Information Note */}
      <div className="px-4">
        <div className="p-4 bg-[#e6ffe6] rounded-xl mb-4">
          <p className="text-[#007A2F] text-sm">
            You will receive notifications about your application status. You
            can also check the status in your profile.
          </p>
        </div>
      </div>

      {/* View Application Button */}
      <div className="px-4 mb-4">
        <button
          className="w-full py-4 border border-[#009639] text-[#009639] rounded-full text-lg font-semibold"
          onClick={() =>
            router.push(`/application-details/${opportunity.applicationId}`)
          }
        >
          View Application Details
        </button>
      </div>

      {/* Return to Home Button */}
      <div className="px-4 pb-8">
        <button
          className="w-full py-4 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm"
          onClick={handleBackHome}
        >
          Return to Home
        </button>
      </div>
    </div>
  );
}
