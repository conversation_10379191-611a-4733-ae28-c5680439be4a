"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft, Target, ChevronRight, MapPin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function RideSelectionPage() {
  const router = useRouter();
  const [selectedRider, setSelectedRider] = useState<number | null>(null);

  const riders = [
    {
      id: 1,
      name: "<PERSON>",
      rating: 4.9,
      price: 60,
      duration: 10,
      seats: 4,
      distance: "0.5 miles",
      carType: "Luxury Sedan",
      carImage: "/placeholder.svg?height=60&width=100",
      avatar: "/placeholder.svg?height=80&width=80",
    },
    {
      id: 2,
      name: "<PERSON>",
      rating: 4.9,
      price: 65,
      duration: 12,
      seats: 4,
      distance: "0.8 miles",
      carType: "SUV",
      carImage: "/placeholder.svg?height=60&width=100",
      avatar: "/placeholder.svg?height=80&width=80",
    },
    {
      id: 3,
      name: "<PERSON>",
      rating: 5,
      price: 70,
      duration: 10,
      seats: 4,
      distance: "1.2 miles",
      carType: "Premium Sedan",
      carImage: "/placeholder.svg?height=60&width=100",
      avatar: "/placeholder.svg?height=80&width=80",
    },
    {
      id: 4,
      name: "Robert Fox",
      rating: 4.9,
      price: 68,
      duration: 16,
      seats: 4,
      distance: "0.7 miles",
      carType: "Luxury SUV",
      carImage: "/placeholder.svg?height=60&width=100",
      avatar: "/placeholder.svg?height=80&width=80",
    },
  ];

  const [origin, setOrigin] = useState("Your current location");
  const [destination, setDestination] = useState(
    "1640 Riverside Drive, Hill Valley"
  );

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Status Bar */}
      <div className="bg-[#eef1f9] px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#eef1f9"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Location Selection */}
      <div className="bg-white px-4 py-3 border-b border-[#f2f2f2]">
        <div className="flex items-center mb-3">
          <div className="flex-shrink-0 mr-3">
            <div className="flex flex-col items-center">
              <div className="w-3 h-3 rounded-full bg-[#0286ff]"></div>
              <div className="w-0.5 h-6 bg-[#d6d9dd] my-1"></div>
              <div className="w-3 h-3 rounded-full bg-[#0cc25f]"></div>
            </div>
          </div>
          <div className="flex-1">
            <div className="mb-2">
              <div className="text-xs text-[#797879] mb-0.5">From</div>
              <div className="text-sm text-[#333333] font-medium">{origin}</div>
            </div>
            <div>
              <div className="text-xs text-[#797879] mb-0.5">To</div>
              <div className="text-sm text-[#333333] font-medium">
                {destination}
              </div>
            </div>
          </div>
          <button className="flex-shrink-0 ml-2 text-[#0286ff]">
            <ChevronRight size={20} />
          </button>
        </div>
      </div>

      {/* Map View */}
      <div className="relative flex-1 bg-[#eef1f9] overflow-hidden">
        {/* Map Background with Roads */}
        <div className="absolute inset-0 opacity-20">
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 400 400"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M0,200 L400,150" stroke="#333" strokeWidth="1" />
            <path d="M100,0 L150,400" stroke="#333" strokeWidth="1" />
            <path d="M250,0 L300,400" stroke="#333" strokeWidth="1" />
            <path d="M0,100 L400,50" stroke="#333" strokeWidth="1" />
            <path d="M0,300 L400,350" stroke="#333" strokeWidth="1" />
          </svg>
        </div>

        {/* Blue Circle Area */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 rounded-full border-2 border-[#67b6ff] bg-[#d6e6fa] bg-opacity-20"></div>

        {/* Back Button */}
        <button 
          className="absolute top-4 left-4 bg-white rounded-full p-3 shadow-md"
          onClick={() => router.push('/home')}
        >
          <ArrowLeft size={24} className="text-[#333333]" />
        </button>

        {/* Title */}
        <div className="absolute top-4 left-0 right-0 text-center">
          <h1 className="text-xl font-bold text-[#333333]">Choose a Rider</h1>
        </div>

        {/* Car Markers */}
        <div className="absolute top-1/4 left-1/4 ride-map-marker h-10 w-10">
          <div className="bg-white p-1 rounded-sm transform rotate-90">
            <svg width="16" height="8" viewBox="0 0 20 10" fill="#333333">
              <rect width="20" height="10" rx="2" />
            </svg>
          </div>
          <span className="absolute -right-1 -bottom-1 bg-[#67b6ff] text-white rounded-full h-5 w-5 flex items-center justify-center text-xs font-bold">
            4
          </span>
        </div>

        <div className="absolute top-1/2 left-1/2 ride-map-marker h-10 w-10">
          <div className="bg-white p-1 rounded-sm transform rotate-90">
            <svg width="16" height="8" viewBox="0 0 20 10" fill="#333333">
              <rect width="20" height="10" rx="2" />
            </svg>
          </div>
          <span className="absolute -right-1 -bottom-1 bg-[#67b6ff] text-white rounded-full h-5 w-5 flex items-center justify-center text-xs font-bold">
            1
          </span>
        </div>

        <div className="absolute bottom-1/4 left-1/4 ride-map-marker h-10 w-10">
          <div className="bg-white p-1 rounded-sm transform rotate-90">
            <svg width="16" height="8" viewBox="0 0 20 10" fill="#333333">
              <rect width="20" height="10" rx="2" />
            </svg>
          </div>
          <span className="absolute -right-1 -bottom-1 bg-[#67b6ff] text-white rounded-full h-5 w-5 flex items-center justify-center text-xs font-bold">
            5
          </span>
        </div>

        <div className="absolute top-1/4 right-1/4 ride-map-marker h-10 w-10">
          <div className="bg-white p-1 rounded-sm transform rotate-90">
            <svg width="16" height="8" viewBox="0 0 20 10" fill="#333333">
              <rect width="20" height="10" rx="2" />
            </svg>
          </div>
          <span className="absolute -right-1 -bottom-1 bg-[#67b6ff] text-white rounded-full h-5 w-5 flex items-center justify-center text-xs font-bold">
            2
          </span>
        </div>

        <div className="absolute bottom-1/3 right-1/4 ride-map-marker h-10 w-10">
          <div className="bg-white p-1 rounded-sm transform rotate-90">
            <svg width="16" height="8" viewBox="0 0 20 10" fill="#333333">
              <rect width="20" height="10" rx="2" />
            </svg>
          </div>
          <span className="absolute -right-1 -bottom-1 bg-[#67b6ff] text-white rounded-full h-5 w-5 flex items-center justify-center text-xs font-bold">
            7
          </span>
        </div>

        {/* Origin Marker */}
        <div className="absolute top-2/5 left-1/3">
          <div className="bg-[#0286ff] rounded-full p-2 shadow-md">
            <div className="w-2 h-2 bg-white rounded-full"></div>
          </div>
        </div>

        {/* Destination Marker */}
        <div className="absolute top-1/5 right-1/3">
          <div className="bg-[#0cc25f] rounded-full p-2 shadow-md">
            <MapPin size={16} className="text-white" />
          </div>
        </div>

        {/* Route Line */}
        <svg
          className="absolute inset-0"
          width="100%"
          height="100%"
          viewBox="0 0 400 400"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M150,200 C200,150 250,150 300,100"
            stroke="#0286ff"
            strokeWidth="4"
            fill="none"
            strokeLinecap="round"
            strokeDasharray="1, 0"
          />
        </svg>

        {/* Target Button */}
        <button className="absolute bottom-4 right-4 bg-[#0cc25f] rounded-full p-4 shadow-md">
          <Target size={24} className="text-white" />
        </button>
      </div>

      {/* Riders List */}
      <div className="bg-white rounded-t-3xl -mt-4 z-10">
        <div className="pt-4 px-4 pb-2">
          <h2 className="text-lg font-semibold text-[#333333]">
            Available Riders
          </h2>
        </div>
        {riders.map((rider, index) => (
          <div
            key={rider.id}
            className={`flex items-center p-4 ${
              index < riders.length - 1 ? "border-b border-[#ededed]" : ""
            } ${selectedRider === rider.id ? "bg-[#f9fbff]" : ""}`}
            onClick={() => setSelectedRider(rider.id)}
          >
            <div className="flex-shrink-0 mr-4">
              <div className="w-14 h-14 ride-avatar">
                <Image
                  src={rider.avatar || "/placeholder.svg"}
                  alt={rider.name}
                  width={56}
                  height={56}
                  className="object-cover"
                />
              </div>
            </div>
            <div className="flex-1">
              <div className="flex items-center">
                <h3 className="text-base font-semibold text-[#333333]">
                  {rider.name}
                </h3>
                <div className="flex items-center ml-2">
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="#ff5c00"
                  >
                    <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" />
                  </svg>
                  <span className="ml-1 text-sm text-[#333333]">
                    {rider.rating}
                  </span>
                </div>
              </div>
              <div className="flex items-center mt-1 text-xs text-[#797879]">
                <div className="flex items-center mr-3">
                  <span className="text-[#0286ff] font-bold mr-0.5">$</span>
                  <span className="text-[#333333] font-bold">
                    ${rider.price}
                  </span>
                </div>
                <div className="flex items-center mr-3">
                  <span>{rider.duration} min</span>
                </div>
                <div className="flex items-center">
                  <span>{rider.seats} seats</span>
                </div>
              </div>
              <div className="text-xs text-[#797879] mt-1">
                <span className="text-[#0cc25f]">{rider.carType}</span> ·{" "}
                {rider.distance}
              </div>
            </div>
            <div className="flex-shrink-0 ml-2">
              <Image
                src={rider.carImage || "/placeholder.svg?height=60&width=100"}
                alt="Car"
                width={80}
                height={48}
                className="object-contain"
              />
            </div>
          </div>
        ))}

        {/* Select Ride Button */}
        <div className="p-4">
          <button
            className="ride-primary-btn w-full py-4 text-lg font-semibold disabled:bg-[#d6d9dd] disabled:text-[#797879]"
            onClick={() =>
              selectedRider &&
              router.push(`/booking-confirmation/${selectedRider}`)
            }
            disabled={!selectedRider}
          >
            Select Ride
          </button>
        </div>
      </div>
    </div>
  );
}
