"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { CheckCircle, Calendar, Car, ChevronRight } from "lucide-react";
import type { VehiclePossessionWithContactRead } from "@/types/vehicle-possessions";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import { formatDateForInput } from "@/lib/utils";
import { getUrl } from "aws-amplify/storage";
import type { VehicleMediaRead } from "@/types/vehicles";


export default function HandoverConfirmationScreen({
  vehicle,
  possession,
}: {
  possession: VehiclePossessionWithContactRead;
  vehicle: VehicleReadWithModelAndParty;
}): React.ReactNode {
  const router = useRouter();
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [toPartyName, setToPartyName] = useState<string>("");
  const [fromPartyName, setFromPartyName] = useState<string>("");
  const [toLocation, setToLocation] = useState<string>("");

  useEffect(() => {
    async function loadImages() {
      if (!vehicle?.media?.length) return;

      const urls: string[] = await Promise.all(
        vehicle.media.map(async (item: VehicleMediaRead) => {
          const result = await getUrl({ path: item.media_path });
          return result.url.toString();
        })
      );

      setImageUrls(urls);
    }

    loadImages();
  }, [vehicle]);

  useEffect(() => {
    const fullName = (individual: any) => {
      return `${individual.first_name} ${individual.middle_name} ${individual.last_name}`;
    };
    if (possession) {
      const fromPartyName = fullName(possession.from_party.individual);
      setFromPartyName(fromPartyName);
      const toPartyName = fullName(possession.to_party.individual);
      setToPartyName(toPartyName);

      const addressContact = possession.to_party.contact_points.find(
        (cp) => cp.contact_point_type?.name === "address"
      );
      const toLocation = addressContact?.value || "No address provided";
      setToLocation(toLocation);
    }
  }, [possession]);

  return (
    <>
      <div className="min-h-screen bg-[#f5f5f5]">
        <div className="bg-[#009639] px-6 py-8 flex flex-col items-center border-b border-[#007A2F]">
          <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
            <CheckCircle size={40} className="text-[#009639]" />
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">
            Handover Complete!
          </h1>
          <p className="text-white text-center">
            {"You have successfully handed over the vehicle"}
          </p>
        </div>

        {/* Vehicle Details */}
        <div className="px-4 py-4">
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
            <div className="h-40 bg-[#f2f2f2] relative">
              <Image
                src={imageUrls?.[0] || "/placeholder.svg"}
                alt={vehicle?.model?.model}
                fill
                className="object-cover"
              />
            </div>

            <div className="p-4">
              <h2 className="text-lg font-bold text-[#333333] mb-1">
                {vehicle?.model?.make?.name} {vehicle?.model?.model} (
                {vehicle?.vin_number}){" "}
              </h2>
              <p className="text-[#797879] mb-3">Reference: {vehicle.id}</p>

              <div className="space-y-3">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                    <Calendar size={12} className="text-[#009639]" />
                  </div>
                  <div>
                    <p className="text-[#797879] text-xs">Date & Time</p>
                    <p className="text-[#333333]">
                      {formatDateForInput(possession?.created_at)}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                    <Car size={12} className="text-[#009639]" />
                  </div>
                  <div>
                    <p className="text-[#797879] text-xs">Odometer</p>
                    <p className="text-[#333333]">{0}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Handover Details */}
        <div className="px-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">
              Handover Details
            </h3>

            <div className="space-y-3">
              <div className="p-3 bg-[#f9f9f9] rounded-lg">
                <p className="text-[#797879] text-xs">Handed over from</p>
                <p className="text-[#333333] font-medium">{fromPartyName}</p>
              </div>

              <div className="p-3 bg-[#f9f9f9] rounded-lg">
                <p className="text-[#797879] text-xs">Handed over to</p>
                <p className="text-[#333333] font-medium">{toPartyName}</p>
              </div>

              <div className="p-3 bg-[#f9f9f9] rounded-lg">
                <p className="text-[#797879] text-xs">Location</p>
                <p className="text-[#333333]">{toLocation}</p>
              </div>
            </div>

            <div className="mt-4">
              <p className="text-sm text-[#797879]">
                A copy of this handover receipt has been sent to your email and
                is available in your account.
              </p>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="px-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">Next Steps</h3>

            <div className="space-y-3">
              <div className="p-3 bg-[#f9f9f9] rounded-lg flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                    <Car size={12} className="text-[#009639]" />
                  </div>
                  <span className="text-[#333333]">View Vehicle Details</span>
                </div>
                <button
                  onClick={() => router.push(`/vehicle-status/${vehicle.id}`)}
                >
                  <ChevronRight size={20} className="text-[#009639]" />
                </button>
              </div>

              <div className="p-3 bg-[#f9f9f9] rounded-lg flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                    <Calendar size={12} className="text-[#009639]" />
                  </div>
                  <span className="text-[#333333]">Schedule Usage</span>
                </div>
                <button
                  onClick={() => router.push(`/booking-calendar/${vehicle.id}`)}
                >
                  <ChevronRight size={20} className="text-[#009639]" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Return to Home Button */}
        <div className="px-4 py-4">
          <button
            className="w-full py-4 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm"
            onClick={() => router.push("/home")}
          >
            Return to Home
          </button>
        </div>
      </div>
    </>
  );
}
