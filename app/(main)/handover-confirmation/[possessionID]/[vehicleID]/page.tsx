"use server";
import { getVehicle } from "@/actions/vehicles";
import HandoverConfirmationScreen from "./handoover-confirmition";
import { getVehiclePossessionByVehicle } from "@/actions/vehicles-possessions";
export default async function HandoverConfirmation({
  params,
}: {
  params: Promise<{ possessionID: string; vehicleID: string }>;
}) {
  const { vehicleID, possessionID } = await params;

  const possession = await getVehiclePossessionByVehicle(
    +vehicleID,
    +possessionID
  );
  const vehicle = await getVehicle(+vehicleID);
  return (
    <HandoverConfirmationScreen possession={possession} vehicle={vehicle} />
  );
}
