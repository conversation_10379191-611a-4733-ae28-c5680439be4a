"use server"

import { db } from "@/db";
import { sql } from "drizzle-orm";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default async function DebugPage() {
  // Raw SQL query to join individuals with parties
  const result = await db.execute(sql`
    SELECT 
      i.id as individual_id,
      i.first_name,
      i.last_name,
      i.birth_date,
      i.created_at as individual_created_at,
      p.id as party_id,
      p.party_type_id,
      p.status_id,
      p.external_id,
      p.created_at as party_created_at
    FROM individual i
    JOIN party p ON i.party_id = p.id
    ORDER BY i.created_at DESC
  `);

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-5">Individuals and Parties Debug View</h1>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Individual ID</TableHead>
              <TableHead>First Name</TableHead>
              <TableHead>Last Name</TableHead>
              <TableHead>Birth Date</TableHead>
              <TableHead>Party ID</TableHead>
              <TableHead>Party Type</TableHead>
              <TableHead>Status ID</TableHead>
              <TableHead>External ID</TableHead>
              <TableHead>Created At</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {result.rows.map((row: any) => (
              <TableRow key={row.individual_id}>
                <TableCell>{row.individual_id}</TableCell>
                <TableCell>{row.first_name}</TableCell>
                <TableCell>{row.last_name}</TableCell>
                <TableCell>{row.birth_date}</TableCell>
                <TableCell>{row.party_id}</TableCell>
                <TableCell>{row.party_type_id}</TableCell>
                <TableCell>{row.status_id}</TableCell>
                <TableCell>{row.external_id}</TableCell>
                <TableCell>{new Date(row.individual_created_at).toLocaleString()}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}