"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  AlertTriangle,
  MessageSquare,
  Users,
  Car,
  Calendar,
  Plus,
  CheckCircle,
  Clock,
  XCircle,
  FileText,
  Paperclip,
  HelpCircle,
  Edit,
  ArrowRight,
  ChevronRight,
} from "lucide-react";
import { DisputeRead, DisputeStatus, DisputeType } from "@/types/disputes";
import { formatDateForInput } from "@/lib/utils";
export default function DisputeResolutionScreen({
  disputes,
}: {
  disputes: DisputeRead[];
}) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("active");
  const [selectedType, setSelectedType] = useState<string | null>(null);

  const disputeTypes = [
    {
      id: DisputeType.VEHICLE_DAMAGE,
      label: "Vehicle Damage",
      icon: <Car size={16} />,
    },
    { id: DisputeType.BOOKING, label: "Booking", icon: <Calendar size={16} /> },
    {
      id: DisputeType.MAINTENANCE_COST_DISPUTE,
      label: "Cost Dispute",
      icon: <FileText size={16} />,
    },
    {
      id: DisputeType.VEHICLE_MAINTENANCE,
      label: "Vehicle Maintenance",
      icon: <Users size={16} />,
    },
    { id: DisputeType.OTHER, label: "Other", icon: <Users size={16} /> },
  ];

  const filteredDisputes = disputes.filter((dispute) => {
    if (activeTab === "active" && dispute.dispute_status !== DisputeStatus.OPEN)
      return false;
    if (
      activeTab === "resolved" &&
      dispute.dispute_status !== DisputeStatus.RESOLVED
    )
      return false;
    if (selectedType && dispute.dispute_type !== selectedType) return false;
    return true;
  });

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#ffe6e6] text-[#7A0000] font-medium">
            High Priority
          </span>
        );
      case "medium":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#fff8e6] text-[#7A6500] font-medium">
            Medium Priority
          </span>
        );
      case "low":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#007A2F] font-medium">
            Low Priority
          </span>
        );
      default:
        return null;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <Clock size={18} className="text-[#009639]" />;
      case "resolved":
        return <CheckCircle size={18} className="text-[#009639]" />;
      case "rejected":
        return <XCircle size={18} className="text-red-500" />;
      default:
        return <AlertTriangle size={18} className="text-yellow-500" />;
    }
  };

  return (
    <div className="min-h-screen">
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Dispute Resolution</h1>
        </div>
        <button
          className="w-10 h-10 bg-[#007A2F] rounded-full flex items-center justify-center shadow-sm text-white"
          onClick={() => router.push("/dispute-resolution/add")}
        >
          <Plus size={20} className="text-white" />
        </button>
      </div>

      {/* Dispute Summary */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">Dispute Summary</h3>
          <div className="grid grid-cols-3 gap-3">
            <div className="p-3 bg-[#e6ffe6] rounded-lg text-center shadow-sm">
              <p className="text-2xl font-bold text-[#009639]">
                {
                  disputes?.filter(
                    (dispute) => dispute.dispute_status === DisputeStatus.OPEN
                  ).length
                }
              </p>
              <p className="text-xs text-[#797879]">Active</p>
            </div>
            <div className="p-3 bg-[#e6ffe6] rounded-lg text-center shadow-sm">
              <p className="text-2xl font-bold text-[#009639]">
                {
                  disputes?.filter(
                    (dispute) =>
                      dispute.dispute_status === DisputeStatus.RESOLVED
                  ).length
                }
              </p>
              <p className="text-xs text-[#797879]">Resolved</p>
            </div>
            <div className="p-3 bg-[#e6ffe6] rounded-lg text-center shadow-sm">
              <p className="text-2xl font-bold text-[#009639]">
                {disputes?.length}
              </p>
              <p className="text-xs text-[#797879]">Total</p>
            </div>
          </div>
        </div>
      </div>

      {/* Type Filter */}
      <div className="bg-white px-4 py-3 border-b border-[#f2f2f2] overflow-x-auto">
        <div className="flex space-x-2">
          <button
            className={`px-3 py-1 rounded-full text-sm whitespace-nowrap ${
              selectedType === null
                ? "bg-[#009639] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setSelectedType(null)}
          >
            All Types
          </button>
          {disputeTypes.map((type) => (
            <button
              key={type.id}
              className={`flex items-center px-3 py-1 rounded-full text-sm whitespace-nowrap ${
                selectedType === type.id
                  ? "bg-[#009639] text-white"
                  : "bg-[#f2f2f2] text-[#333333]"
              }`}
              onClick={() => setSelectedType(type.id)}
            >
              {type.icon}
              <span className="ml-1">{type.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow-sm mb-4">
        <div className="flex mx-4">
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "active" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("active")}
          >
            Active
            {activeTab === "active" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "resolved" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("resolved")}
          >
            Resolved
            {activeTab === "resolved" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
        </div>
      </div>

      {/* Disputes List */}
      <div className="p-4">
        {filteredDisputes.length > 0 ? (
          <div className="space-y-3">
            {filteredDisputes.map((dispute) => (
              <div
                key={dispute.id}
                className="bg-white rounded-xl shadow-md p-4 border border-gray-100"
              >
                <div className="flex items-start">
                  <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                    <AlertTriangle size={18} className="text-[#009639]" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="text-[#333333] font-medium">
                        {dispute.name}
                      </h3>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(dispute.dispute_status)}
                        <span className="text-xs text-[#797879] ml-1">
                          {dispute.dispute_status === DisputeStatus.OPEN
                            ? "Active"
                            : "Resolved"}
                        </span>
                        <button
                          type="button"
                          onClick={() =>
                            router.push(
                              `/dispute-resolution/edit/${dispute.id}`
                            )
                          }
                          className="ml-3 p-1 hover:bg-gray-100 text-[#333333] rounded"
                          title="Edit"
                        >
                          <Edit
                            size={16}
                            className="text-gray-600 text-[#333333]"
                          />
                        </button>
                        <button
                          type="button"
                          onClick={() =>
                            router.push(
                              `/dispute-resolution/details/${dispute.id}`
                            )
                          }
                          className="ml-3 p-1 hover:bg-gray-100 text-[#333333] rounded"
                          title="Edit"
                        >
                          <ChevronRight
                            size={16}
                            className="text-gray-600 text-[#333333]"
                          />
                        </button>
                      </div>
                    </div>
                    <p className="text-xs text-[#797879] mt-1">
                      {dispute.name}
                      {dispute.vehicle_id && ` • ${dispute.vehicle_id}`}
                    </p>
                    <p className="text-sm text-[#333333] mt-1 line-clamp-2">
                      {dispute.description}
                    </p>
                    <div className="flex items-center mt-2 space-x-3">
                      <div className="flex -space-x-2">
                        <div
                          className="w-6 h-6 rounded-full border-2 border-white overflow-hidden"
                          style={{ zIndex: 10 - dispute.party_logging }}
                        >
                          <Image
                            src={"/placeholder.svg"}
                            alt={dispute.party_logging?.toString()}
                            width={24}
                            height={24}
                            className="object-cover"
                          />
                        </div>
                        <div
                          className="w-6 h-6 rounded-full border-2 border-white overflow-hidden"
                          style={{ zIndex: 10 - dispute.party_offending }}
                        >
                          <Image
                            src={"/placeholder.svg"}
                            alt={dispute.party_offending?.toString()}
                            width={24}
                            height={24}
                            className="object-cover"
                          />
                        </div>
                      </div>
                      <div className="flex items-center text-xs text-[#797879]">
                        <MessageSquare
                          size={12}
                          className="text-[#797879] mr-1"
                        />
                        {dispute.comments?.length}
                      </div>
                      <div className="flex items-center text-xs text-[#797879]">
                        <Paperclip size={12} className="text-[#797879] mr-1" />
                        {dispute.media.length}
                      </div>
                    </div>
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center">
                        <Calendar size={14} className="text-[#797879] mr-1" />
                        <span className="text-xs text-[#797879]">
                          {dispute.dispute_status === DisputeStatus.OPEN
                            ? `Opened: ${formatDateForInput(dispute.created_date)}`
                            : `Resolved: ${formatDateForInput(
                                dispute.created_date || ""
                              )}`}
                        </span>
                      </div>
                      <div>{getPriorityBadge(dispute.priority)}</div>
                    </div>
                    {dispute.dispute_status === DisputeStatus.RESOLVED && (
                      <div className="mt-2 p-2 bg-[#e6ffe6] rounded-lg shadow-sm">
                        <p className="text-xs text-[#007A2F]">
                          <span className="font-medium">Resolved:</span>
                          {/* {dispute.resolution.description} */}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <div className="w-16 h-16 bg-[#e6ffe6] rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
              <AlertTriangle size={32} className="text-[#009639]" />
            </div>
            <p className="text-[#333333] font-medium">
              No {activeTab} disputes
            </p>
            <p className="text-[#797879] text-sm mt-1">
              {activeTab === "active"
                ? "You don't have any active disputes"
                : "Resolved disputes will appear here"}
            </p>
            {activeTab === "active" && (
              <div>
                <div className="flex justify-center mt-4">
                  <button
                    className="w-12 h-12 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full shadow-md flex items-center justify-center"
                    onClick={() => router.push("/dispute-resolution/add")}
                  >
                    <Plus size={24} />
                  </button>
                </div>
                <p className="text-[#009639] text-sm font-medium mt-2 text-center">
                  Report an Issue
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Help Section */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center mb-3">
            <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
              <HelpCircle size={20} className="text-[#009639]" />
            </div>
            <h3 className="text-[#333333] font-medium">Need Help?</h3>
          </div>
          <p className="text-sm text-[#797879] mb-4">
            Our support team can help mediate disputes and provide guidance on
            resolution options in South Africa.
          </p>
          <button
            className="w-full py-3 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full font-semibold shadow-md"
            onClick={() => router.push("/contact-support")}
          >
            Contact Support
          </button>
        </div>
      </div>
    </div>
  );
}
