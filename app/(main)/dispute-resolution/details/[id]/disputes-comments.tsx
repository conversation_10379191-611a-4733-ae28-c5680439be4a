"use client";

import { useState } from "react";
import {
  DisputeR<PERSON>,
  DisputeStatus,
  DisputeCommentCreate,
  DisputeCommentRead,
} from "@/types/disputes";
import type { CompanyMembershipRead } from "@/types/company-ownerships";
import { formatDateForInput } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { commentSchema } from "./dispute-details";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { createDisputeComment } from "@/actions/dispute-comments";

interface CommentItemProps {
  replyComments: DisputeCommentRead[];
  loggedInUserPartyId: number;
  ownershipData: CompanyMembershipRead;
  dispute: DisputeRead;
  level?: number;
  AllComments: DisputeCommentRead[];
}
// note, i have added replynoted and allnoted for nesting of replies

export default function CommentItem({
  replyComments,
  loggedInUserPartyId,
  ownershipData,
  dispute,
  AllComments,
  level = 0,
}: CommentItemProps) {
  let topLevelComments;
  if (level === 0) {
    topLevelComments = replyComments.filter(
      (comment) => comment.reply_to_comment_id == null
    );
  } else {
    topLevelComments = replyComments;
  }
  return (
    <>
      {topLevelComments.map((comment) => {
        const author = ownershipData.individuals.find(
          (ind) => ind.party_id === comment.user_id
        );
        const authorName = author
          ? `${author.first_name} ${author.last_name}`
          : "Unknown";

        const replies = AllComments.filter(
          (c) => c.reply_to_comment_id === comment.id
        );

        const [showReplyForm, setShowReplyForm] = useState(false);
        const [error, setError] = useState<string | null>(null);
        const [submitting, setSubmitting] = useState(false);

        const form = useForm<DisputeCommentCreate>({
          resolver: zodResolver(commentSchema),
          defaultValues: {
            comment: "",
            dispute_id: dispute.id,
            user_id: loggedInUserPartyId,
            reply_to_comment_id: comment.id,
          },
        });

        const onSubmit = async (data: DisputeCommentCreate) => {
          setSubmitting(true);
          setError(null);
          try {
            await createDisputeComment(data);
            form.reset();
            setShowReplyForm(false);
          } catch (err: any) {
            setError(
              err instanceof Error
                ? err.message
                : "Failed to submit reply. Please try again."
            );
          } finally {
            setSubmitting(false);
          }
        };

        return (
          <div
            key={comment.id}
            className={`border-l-2 border-gray-200 pl-4 py-2 ${level > 0 ? "ml-4" : ""}`}
          >
            <div className="flex items-start space-x-2">
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-semibold text-[#333333]">
                    {authorName}
                    {comment.user_id === loggedInUserPartyId && " (You)"}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatDateForInput(comment.created_at, true)}
                  </p>
                </div>
                <p className="text-sm text-[#333333] mt-1">{comment.comment}</p>
                <Button
                  variant="link"
                  className="text-sm text-[#009639] p-0 mt-1"
                  onClick={() => setShowReplyForm(!showReplyForm)}
                >
                  {showReplyForm ? "Cancel" : "Reply"}
                </Button>
              </div>
            </div>

            {/* Reply Form */}
            {showReplyForm &&
              dispute.dispute_status !== DisputeStatus.RESOLVED && (
                <div className="mt-2 ml-4">
                  {error && (
                    <div className="mb-2 p-2 bg-[#ffe6e6] rounded-lg shadow-sm">
                      <p className="text-xs text-[#7A0000]">{error}</p>
                    </div>
                  )}
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit)}
                      className="space-y-2"
                    >
                      <FormField
                        control={form.control}
                        name="comment"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm text-[#333333] font-medium">
                              Reply
                            </FormLabel>
                            <FormControl>
                              <Textarea
                                {...field}
                                placeholder="Enter your reply"
                                className="w-full mt-1 p-2 border border-gray-200 rounded-lg text-sm text-[#333333] focus:ring-[#009639] focus:border-[#009639]"
                                rows={3}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <Button
                        type="submit"
                        disabled={submitting}
                        className="py-2 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full font-semibold shadow-md"
                      >
                        {submitting ? "Submitting..." : "Post Reply"}
                      </Button>
                    </form>
                  </Form>
                </div>
              )}

            {replies.length > 0 && (
              <div className="mt-2">
                <CommentItem
                  replyComments={replies}
                  loggedInUserPartyId={loggedInUserPartyId}
                  ownershipData={ownershipData}
                  dispute={dispute}
                  level={level + 1}
                  AllComments={AllComments}
                />
              </div>
            )}
          </div>
        );
      })}
    </>
  );
}
