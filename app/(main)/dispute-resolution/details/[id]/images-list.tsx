"use client";

import React, { useEffect, useState } from "react";
import { Calendar, FileText, X, Loader } from "lucide-react";
import { formatDateForInput, DocumentDelete } from "@/lib/utils";
import Image from "next/image";
import { getUrl } from "aws-amplify/storage";
import type { DisputeRead, DisputeMediaRead } from "@/types/disputes";
import { deleteVehicleMedia } from "@/actions/vehicle-media";

interface VehicleMediaWithUrl extends DisputeMediaRead {
  signedUrl?: string;
}

export default function VehicleImageList({
  dispute,
}: {
  dispute: DisputeRead;
}) {
  const [vehicleMedia, setVehicleImages] = useState<VehicleMediaWithUrl[]>([]);
  const [deletingId, setDeletingId] = useState<number | null>(null);

  useEffect(() => {
    const fetchSignedUrls = async () => {
      const media = dispute.media;
      const withUrls: VehicleMediaWithUrl[] = await Promise.all(
        media.map(async (m) => {
          try {
            const urlResult = await getUrl({ path: m.media_path });
            return { ...m, signedUrl: urlResult.url.toString() };
          } catch (err) {
            console.error(`Failed to get signed URL for ${m.media_path}`, err);
            return { ...m, signedUrl: undefined };
          }
        })
      );
      setVehicleImages(withUrls);
    };

    fetchSignedUrls();
  }, [dispute]);

  const isImage = (path: string) =>
    /\.(jpg|jpeg|png|webp|gif)$/i.test(path ?? "");

  async function handleDeleteMedia(media: VehicleMediaWithUrl) {
    const confirmed = window.confirm(
      `Are you sure you want to delete "${media.media_path.split("/").pop()}"?`
    );
    if (!confirmed) return;

    setDeletingId(media.id);
    try {
      await DocumentDelete(media.media_path);
      await deleteVehicleMedia(media.id);
      setVehicleImages((prev) => prev.filter((m) => m.id !== media.id));
    } catch (error) {
      console.error("Error deleting file from S3 or database:", error);
    } finally {
      setDeletingId(null);
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {vehicleMedia.map((media) => (
          <div
            key={media.id}
            className="relative cursor-pointer border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition duration-150 bg-white"
          >
            <div className="absolute top-2 right-2 z-10">
              <button
                onClick={() => handleDeleteMedia(media)}
                className="bg-white rounded-full p-1 text-red-600 hover:bg-red-100"
                aria-label="Delete image"
                disabled={deletingId === media.id}
              >
                {deletingId === media.id ? (
                  <Loader size={16} className="animate-spin" />
                ) : (
                  <X size={16} />
                )}
              </button>
            </div>

            <div className="h-40 w-full bg-gray-100 flex items-center justify-center relative">
              {isImage(media.media_path) && media.signedUrl ? (
                <a
                  href={media.signedUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Image
                    src={media.signedUrl}
                    alt="Vehicle Media"
                    fill
                    className="object-cover"
                  />
                </a>
              ) : (
                <a
                  href={media.signedUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <FileText size={40} className="text-gray-400" />
                </a>
              )}
            </div>
            <div className="p-3">
              <p className="text-xs text-gray-500 truncate">
                {media.media_path.split("/").pop()}
              </p>
              <div className="flex items-center text-xs text-gray-400 mt-1">
                <Calendar size={12} className="mr-1" />
                <span>{formatDateForInput(media?.created_at)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
