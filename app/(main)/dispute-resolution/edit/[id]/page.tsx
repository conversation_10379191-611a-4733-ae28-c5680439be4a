"use server";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import { getCompanyOwnershipByParty } from "@/actions/company-ownership";
import { getCompanyOwnershipsByCompanies } from "@/actions/company-ownership";
import MissingParty from "@/components/missing-party";
import type { CompanyMembershipRead } from "@/types/company-ownerships";
import ReportIssueScreen from "./dispute-edit";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import { getVehiclesByParties } from "@/actions/vehicles";
import { getDispute } from "@/actions/dispute";
export default async function ReportIssue({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  const userOwnerships = await getCompanyOwnershipByParty(+dbId);

  const partyIds: number[] = userOwnerships
    .map((ownership) => ownership.company?.party_id)
    .filter((id): id is number => typeof id === "number");

  const companyIds: number[] = userOwnerships
    .map((ownership) => ownership.company_id)
    .filter((id): id is number => typeof id === "number");

  const vehiclesPromise =
    partyIds.length > 0
      ? getVehiclesByParties(partyIds)
      : Promise.resolve([] as VehicleReadWithModelAndParty[]);

  const ownershipsPromise =
    companyIds.length > 0
      ? getCompanyOwnershipsByCompanies(companyIds)
      : Promise.resolve({} as CompanyMembershipRead);

  const disputePromise = getDispute(+id);

  const [vehicles, ownerships, dispute] = await Promise.all([
    vehiclesPromise,
    ownershipsPromise,
    disputePromise,
  ]);

  return (
    <ReportIssueScreen
      ownershipData={ownerships}
      loggedInUserPartyId={+dbId}
      vehicles={vehicles}
      dispute={dispute}
    />
  );
}
