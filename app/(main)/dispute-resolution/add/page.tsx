"use server";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import { getCompanyOwnershipByParty } from "@/actions/company-ownership";
import { getCompanyOwnershipsByCompanies } from "@/actions/company-ownership";
import MissingParty from "@/components/missing-party";
import type { CompanyMembershipRead } from "@/types/company-ownerships";
import ReportIssueScreen from "./dispute-add";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import { getVehiclesByParties } from "@/actions/vehicles";

export default async function ReportIssue() {
  let ownerships, vehicles;
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;
  const userOwnerships = await getCompanyOwnershipByParty(+dbId);
  const partyIds: number[] = userOwnerships
    .map((ownership) => ownership.company?.party_id)
    .filter((id): id is number => typeof id === "number");
  if (partyIds.length === 0) {
    vehicles = [] as VehicleReadWithModelAndParty[];
  } else {
    vehicles = await getVehiclesByParties(partyIds);
  }

  const companyIds: number[] = userOwnerships
    .map((ownership) => ownership.company_id)
    .filter((id): id is number => typeof id === "number");

  if (companyIds.length === 0) {
    ownerships = {} as CompanyMembershipRead;
  } else {
    ownerships = await getCompanyOwnershipsByCompanies(companyIds);
  }
  return (
    <ReportIssueScreen
      ownershipData={ownerships}
      loggedInUserPartyId={+dbId}
      vehicles={vehicles}
    />
  );
}
