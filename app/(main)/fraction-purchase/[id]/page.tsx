"use client";

import type React from "react";

import { useState, useRef, use } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  User,
  Calendar,
  FileText,
  ChevronDown,
  ChevronUp,
  CreditCard,
  Check,
  AlertTriangle,
} from "lucide-react";

export default function FractionPurchaseScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const fractionId = use(params).id;
  const signatureCanvasRef = useRef<HTMLCanvasElement>(null);
  const [showLegalDetails, setShowLegalDetails] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    string | null
  >("card1");
  const [isDrawing, setIsDrawing] = useState(false);
  const [signature, setSignature] = useState<string | null>(null);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Mock data - in a real app, this would come from an API
  const fraction = {
    id: fractionId,
    vehicleName:
      fractionId === "1"
        ? "Toyota Hilux"
        : fractionId === "2"
        ? "Volkswagen Polo"
        : "Ford Ranger",
    year: 2021,
    image: "/placeholder.svg?height=120&width=200",
    price: fractionId === "1" ? 150000 : fractionId === "2" ? 120000 : 180000,
    ownershipPercentage: 25,
    ownerName: "Jane Cooper",
    usageDays: 7,
    location:
      fractionId === "1"
        ? "Cape Town, Western Cape"
        : fractionId === "2"
        ? "Johannesburg, Gauteng"
        : "Durban, KwaZulu-Natal",
    description:
      fractionId === "1"
        ? "Well-maintained Toyota Hilux in excellent condition. Low kilometers, clean interior, and regularly serviced."
        : fractionId === "2"
        ? "Economical Volkswagen Polo perfect for city driving. Fuel efficient with great handling."
        : "Powerful Ford Ranger with excellent towing capacity. Perfect for work and weekend adventures.",
  };

  const paymentMethods = [
    {
      id: "card1",
      type: "card",
      name: "Visa ending in 4242",
      icon: <CreditCard size={20} className="text-[#009639]" />,
      details: "Expires 12/25",
    },
    {
      id: "card2",
      type: "card",
      name: "Mastercard ending in 5678",
      icon: <CreditCard size={20} className="text-[#009639]" />,
      details: "Expires 10/24",
    },
  ];

  // Signature pad handlers
  const startDrawing = (
    e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>
  ) => {
    setIsDrawing(true);
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    ctx.beginPath();

    // Get position based on event type
    let clientX, clientY;
    if ("touches" in e) {
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }

    const rect = canvas.getBoundingClientRect();
    ctx.moveTo(clientX - rect.left, clientY - rect.top);
  };

  const draw = (
    e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>
  ) => {
    if (!isDrawing) return;

    const canvas = signatureCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Get position based on event type
    let clientX, clientY;
    if ("touches" in e) {
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      clientX = e.clientX;
      clientY = e.clientY;
    }

    const rect = canvas.getBoundingClientRect();
    ctx.lineTo(clientX - rect.left, clientY - rect.top);
    ctx.stroke();
  };

  const stopDrawing = () => {
    setIsDrawing(false);

    const canvas = signatureCanvasRef.current;
    if (!canvas) return;

    // Save the signature
    const dataUrl = canvas.toDataURL("image/png");
    setSignature(dataUrl);
  };

  const clearSignature = () => {
    const canvas = signatureCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    setSignature(null);
  };

  const handlePaymentMethodSelect = (id: string) => {
    setSelectedPaymentMethod(id);
  };

  const handleCompletePurchase = () => {
    if (!signature || !termsAccepted || !selectedPaymentMethod) return;

    setIsProcessing(true);

    // Simulate processing
    setTimeout(() => {
      console.log("Purchase completed:", {
        fractionId,
        paymentMethod: selectedPaymentMethod,
        signature,
        termsAccepted,
      });
      router.push(`/purchase-confirmation/${fractionId}`);
    }, 2000);
  };

  return (
    <>
      <div className="min-h-screen bg-[#f5f5f5]">
        {/* Header */}
        <div className="bg-[#009639] px-6 py-4 flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-[#ffffff]" />
          </button>
          <h1 className="text-xl font-bold text-[#ffffff]">
            Purchase Fraction
          </h1>
        </div>

        {/* Fraction Details */}
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
            <div className="h-40 bg-[#f2f2f2] relative">
              <Image
                src={fraction.image || "/placeholder.svg"}
                alt={fraction.vehicleName}
                fill
                className="object-cover"
              />
              <div className="absolute top-3 right-3 bg-[#e6ffe6] px-3 py-1 rounded-full text-xs font-medium text-[#009639]">
                {fraction.ownershipPercentage}% Ownership
              </div>
            </div>

            <div className="p-4">
              <h2 className="text-xl font-semibold text-[#333333] mb-1">
                {fraction.year} {fraction.vehicleName}
              </h2>
              <p className="text-[#797879] mb-3">{fraction.location}</p>

              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <User size={16} className="text-[#009639] mr-1" />
                  <span className="text-sm text-[#797879]">
                    Seller: {fraction.ownerName}
                  </span>
                </div>
                <div className="flex items-center">
                  <Calendar size={16} className="text-[#009639] mr-1" />
                  <span className="text-sm text-[#797879]">
                    {fraction.usageDays} days/month
                  </span>
                </div>
              </div>

              <div className="border-t border-[#f2f2f2] pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-[#797879]">Fraction Price</span>
                  <span className="text-xl font-bold text-[#009639]">
                    R{fraction.price.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Legal Implications */}
        <div className="px-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <button
              className="w-full flex items-center justify-between"
              onClick={() => setShowLegalDetails(!showLegalDetails)}
            >
              <div className="flex items-center">
                <FileText size={20} className="text-[#009639] mr-2" />
                <h3 className="text-[#333333] font-medium">
                  Legal Implications
                </h3>
              </div>
              {showLegalDetails ? (
                <ChevronUp size={20} />
              ) : (
                <ChevronDown size={20} />
              )}
            </button>

            {showLegalDetails && (
              <div className="mt-3 p-3 bg-[#f9f9f9] rounded-lg max-h-40 overflow-y-auto">
                <p className="text-sm text-[#797879]">
                  By purchasing this vehicle fraction, you understand and agree
                  to the following:
                  <br />
                  <br />
                  1. You are purchasing a {fraction.ownershipPercentage}%
                  ownership stake in the vehicle.
                  <br />
                  <br />
                  2. This purchase entitles you to use the vehicle for{" "}
                  {fraction.usageDays} days per month.
                  <br />
                  <br />
                  3. You will be responsible for a proportional share of
                  maintenance, insurance, and other costs.
                  <br />
                  <br />
                  4. All co-owners must adhere to the Pull Co-Ownership
                  Agreement.
                  <br />
                  <br />
                  5. Disputes between co-owners will be resolved according to
                  the terms in the agreement.
                  <br />
                  <br />
                  6. You have the right to sell your fraction at any time,
                  subject to the terms in the agreement.
                </p>
              </div>
            )}

            <div className="mt-3 flex items-start">
              <input
                type="checkbox"
                id="termsAccepted"
                checked={termsAccepted}
                onChange={(e) => setTermsAccepted(e.target.checked)}
                className="mt-1 h-4 w-4 rounded border-[#d6d9dd] text-[#009639] focus:ring-[#009639]"
              />
              <label
                htmlFor="termsAccepted"
                className="ml-2 text-sm text-[#797879]"
              >
                I have read and agree to the co-ownership terms and conditions
              </label>
            </div>
          </div>
        </div>

        {/* Payment Method */}
        <div className="px-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">Payment Method</h3>

            <div className="space-y-3">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className={`p-3 rounded-lg border ${
                    selectedPaymentMethod === method.id
                      ? "border-[#009639] bg-[#e6ffe6]"
                      : "border-[#d6d9dd]"
                  }`}
                  onClick={() => handlePaymentMethodSelect(method.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {method.icon}
                      <div className="ml-3">
                        <p className="text-[#333333] font-medium">
                          {method.name}
                        </p>
                        <p className="text-xs text-[#797879]">
                          {method.details}
                        </p>
                      </div>
                    </div>
                    {selectedPaymentMethod === method.id && (
                      <Check size={20} className="text-[#009639]" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Digital Signature */}
        <div className="px-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">
              Digital Signature
            </h3>

            {signature ? (
              <div className="relative border border-[#d6d9dd] rounded-lg p-2 h-40 mb-2">
                <Image
                  src={signature || "/placeholder.svg"}
                  alt="Signature"
                  fill
                  className="object-contain"
                />
                <button
                  className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm"
                  onClick={clearSignature}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="#ef4444"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18" />
                    <line x1="6" y1="6" x2="18" y2="18" />
                  </svg>
                </button>
              </div>
            ) : (
              <div className="border border-[#d6d9dd] rounded-lg bg-[#f9f9f9] h-40 mb-2 relative">
                <canvas
                  ref={signatureCanvasRef}
                  width={400}
                  height={160}
                  className="w-full h-full touch-none"
                  onMouseDown={startDrawing}
                  onMouseMove={draw}
                  onMouseUp={stopDrawing}
                  onMouseLeave={stopDrawing}
                  onTouchStart={startDrawing}
                  onTouchMove={draw}
                  onTouchEnd={stopDrawing}
                />
                <p className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-[#797879] pointer-events-none">
                  Sign here
                </p>
              </div>
            )}

            <p className="text-xs text-[#797879] text-center">
              By signing, you confirm your agreement to purchase this vehicle
              fraction
            </p>
          </div>
        </div>

        {/* Purchase Summary */}
        <div className="px-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">
              Purchase Summary
            </h3>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-[#797879]">Fraction Price</span>
                <span className="text-[#333333]">
                  R{fraction.price.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-[#797879]">Service Fee (5%)</span>
                <span className="text-[#333333]">
                  R{(fraction.price * 0.05).toLocaleString()}
                </span>
              </div>
              <div className="border-t border-[#f2f2f2] my-2 pt-2 flex justify-between">
                <span className="text-[#333333] font-medium">Total</span>
                <span className="text-[#333333] font-bold">
                  R{(fraction.price * 1.05).toLocaleString()}
                </span>
              </div>
            </div>

            <div className="mt-3 p-3 bg-yellow-50 rounded-lg flex items-start">
              <AlertTriangle
                size={18}
                className="text-yellow-500 mr-2 flex-shrink-0 mt-0.5"
              />
              <p className="text-sm text-yellow-700">
                This purchase is binding. You will be charged immediately upon
                confirmation.
              </p>
            </div>
          </div>
        </div>

        {/* Complete Purchase Button */}
        <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2]">
          <button
            className={`w-full py-4 rounded-full text-xl font-semibold ${
              signature &&
              termsAccepted &&
              selectedPaymentMethod &&
              !isProcessing
                ? "bg-[#009639] text-white"
                : "bg-[#f2f2f2] text-[#797879]"
            }`}
            onClick={handleCompletePurchase}
            disabled={
              !signature ||
              !termsAccepted ||
              !selectedPaymentMethod ||
              isProcessing
            }
          >
            {isProcessing ? "Processing..." : "Complete Purchase"}
          </button>
        </div>
      </div>
    </>
  );
}
