import { useFormStatus } from "react-dom";

export default function FormSubmit() {
  const status = useFormStatus();
  if (status.pending) {
    return (
      <div className="flex justify-end">
        <button
          className="bg-[#009639] text-white px-6 py-2 rounded-xl shadow hover:bg-[#007A2F]"
          type="submit"
          disabled
        >
          <div className="border-t-4 border-b-4 border-white w-6 h-6 border-solid rounded-full animate-spin"></div>
        </button>
      </div>
    );
  }

  return (
    <div className="flex justify-end">
      <button
        type="submit"
        className="bg-[#009639] text-white px-6 py-2 rounded-xl shadow hover:bg-[#007A2F]"
      >
        Submit
      </button>
    </div>
  );
}
