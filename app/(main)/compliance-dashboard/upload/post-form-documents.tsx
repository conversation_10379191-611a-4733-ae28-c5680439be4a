"use client";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { DocumentDelete, DocumentUpload } from "@/lib/utils";
import { FileText, Upload, X, ArrowLeft, Loader2 } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { format, parseISO } from "date-fns";
import type { ComplianceRequirementTypeRead } from "@/types/compliance";
import {
  ComplianceRequirementStatusEnum,
  ReferenceTypeEnum,
} from "@/types/compliance";
import { ComplianceSetRead, IssuingAuthorityRead } from "@/types/compliance";
import { IndividualRead } from "@/types/individuals";
import { PartyRead } from "@/types/party";
import { createComplianceRequirement } from "@/actions/compliance-requirement";

const formSchema = z.object({
  requirement_type_id: z.coerce.number({
    required_error: "Please select a compliance type",
    invalid_type_error: "Compliance type must be a number",
  }),
  party_id: z.coerce.number({
    required_error: "Please select a party",
    invalid_type_error: "Party ID must be a number",
  }),
  compliance_set_id: z.coerce.number({
    required_error: "Please select a compliance set",
    invalid_type_error: "Compliance set ID must be a number",
  }),
  verification_id: z.coerce.number().optional(),
  reviewed_by_id: z.coerce.number().optional(),
  status: z.nativeEnum(ComplianceRequirementStatusEnum).optional(),
  reference_type: z.nativeEnum(ReferenceTypeEnum).optional(),
  reference: z.string().optional(),
  submitted_at: z.string().optional(),
  reviewed_at: z.string().optional(),
  notes: z.string().optional(),
  issuing_authority_id: z.coerce.number().optional(),
  issue_date: z.string().optional(),
  expiry_date: z.string().optional(),
  uploaded_individual_id: z.coerce.number().optional(),
});

type PostFormProps = {
  compliance_types: ComplianceRequirementTypeRead[];
  party: PartyRead;
  compliance_sets: ComplianceSetRead[];
  individuals: IndividualRead[];
  issuing_authorities: IssuingAuthorityRead[];
};

export default function PostForm({
  compliance_types,
  party,
  compliance_sets,
  individuals,
  issuing_authorities,
}: PostFormProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [documents, setDocuments] = useState<File[]>([]);
  const [documentUrl, setDocumentUrl] = useState<string>("");
  const [error, setError] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const router = useRouter();

  // Initialize React Hook Form with Zod schema
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      requirement_type_id: undefined,
      party_id: party.id,
      compliance_set_id: undefined,
      verification_id: undefined,
      reviewed_by_id: undefined,
      status: undefined,
      reference_type: undefined,
      reference: "",
      submitted_at: "",
      reviewed_at: "",
      notes: "",
      issuing_authority_id: undefined,
      issue_date: "",
      expiry_date: "",
      uploaded_individual_id: undefined,
    },
  });

  // Watch reference_type to conditionally render fields
  const referenceType = form.watch("reference_type");

  const handleDocumentUpload = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];

    if (file) {
      try {
        setIsUploading(true);
        const upf = await DocumentUpload(file, "vehicle");

        if (upf && upf.path) {
          setDocumentUrl(upf.path);
          setDocuments([file]);
          form.setValue("reference", upf.path); // Set reference to document URL
        }
      } catch (err: any) {
        console.error(err);
        setError(err?.message || "An error occurred. Please try again.");
      } finally {
        setIsUploading(false);
      }
    }
  };

  async function handleRemoveDocument(index: number, path: string) {
    try {
      setIsUploading(true);
      await DocumentDelete(path);
      setDocumentUrl("");
      setDocuments((prev) => prev.filter((_, i) => i !== index));
      form.setValue("reference", ""); // Clear reference when document is removed
    } catch (error) {
      console.error("Error deleting file from S3:", error);
      setError("Failed to delete document. Please try again.");
    } finally {
      setIsUploading(false);
    }
  }

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      const payload = {
        ...values,
        reference:
          referenceType === ReferenceTypeEnum.URL
            ? documentUrl
            : values.reference,
      };
      await createComplianceRequirement(payload);
      router.push("/compliance-dashboard");
    } catch (error) {
      console.error("Submission error:", error);
      setError("Failed to submit form. Please try again.");
    }
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]-foreground">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.back()}
            className="mr-4"
          >
            <ArrowLeft size={24} />
          </Button>
          <h1 className="text-xl font-bold text-white">Compliance Upload</h1>
        </div>
        <Button
          variant="secondary"
          size="sm"
          className="bg-[#007A2F] text-white text-sm font-medium px-3 py-1.5 rounded-full shadow-sm"
          onClick={() => router.push("/legal-entity-formation")}
        >
          Add Entity
        </Button>
      </div>

      <div className="max-w-2xl mx-auto pt-6">
        <Card>
          <CardHeader>
            <CardTitle>Compliance Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                {/* Party ID */}
                <FormField
                  control={form.control}
                  name="party_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Party</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(Number(value))}
                        defaultValue={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Party" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem
                            key={party.id}
                            value={party.id.toString()}
                          >
                            {party.external_id}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Compliance Set ID */}
                <FormField
                  control={form.control}
                  name="compliance_set_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Compliance Set</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(Number(value))}
                        defaultValue={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Compliance Set" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {compliance_sets?.map((set) => (
                            <SelectItem key={set.id} value={set.id.toString()}>
                              {set.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Compliance Type ID */}
                <FormField
                  control={form.control}
                  name="requirement_type_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Compliance Type</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(Number(value))}
                        defaultValue={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Choose Type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {compliance_types?.map((compliance_type) => (
                            <SelectItem
                              key={compliance_type.id}
                              value={compliance_type.id.toString()}
                            >
                              {compliance_type.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Reviewed By ID */}
                <FormField
                  control={form.control}
                  name="reviewed_by_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reviewed By</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(Number(value))}
                        defaultValue={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Reviewer" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {individuals?.map((individual) => (
                            <SelectItem
                              key={individual.id}
                              value={individual.id.toString()}
                            >
                              {individual.first_name} {individual.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Status */}
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(ComplianceRequirementStatusEnum).map(
                            (status) => (
                              <SelectItem key={status} value={status}>
                                {status}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Reference Type */}
                <FormField
                  control={form.control}
                  name="reference_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reference Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Reference Type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.values(ReferenceTypeEnum).map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Conditional Reference or Document Upload */}
                {referenceType === ReferenceTypeEnum.URL ? (
                  <div>
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      onChange={handleDocumentUpload}
                      disabled={isUploading}
                    />
                    {isUploading ? (
                      <div className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center">
                        <Loader2
                          size={32}
                          className="text-[#009639] animate-spin mb-2"
                        />
                        <p className="text-[#797879] text-center">
                          Uploading...
                        </p>
                      </div>
                    ) : documents.length > 0 ? (
                      <ul className="space-y-4">
                        {documents.map((file, index) => (
                          <li
                            key={index}
                            className="bg-[#f9f9f9] p-4 rounded-xl border border-[#e5e7eb] shadow-sm"
                          >
                            <div className="flex items-start justify-between gap-4">
                              <div className="flex flex-1 items-center gap-2 min-w-0">
                                <FileText
                                  size={20}
                                  className="text-[#009639] shrink-0"
                                />
                                <span className="text-sm text-[#333333] font-medium break-all truncate">
                                  {file.name}
                                </span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="hover:bg-red-50 rounded-full p-1 transition shrink-0"
                                onClick={async () => {
                                  await handleRemoveDocument(
                                    index,
                                    documentUrl
                                  );
                                }}
                                disabled={isUploading}
                              >
                                <X size={14} className="text-red-500" />
                              </Button>
                            </div>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <div
                        className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <Upload size={32} className="text-[#009639] mb-2" />
                        <p className="text-[#797879] text-center">
                          Upload document
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <FormField
                    control={form.control}
                    name="reference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reference</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter reference" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Submitted At */}
                <FormField
                  control={form.control}
                  name="submitted_at"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Submitted At</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          {...field}
                          value={
                            field.value
                              ? format(
                                  parseISO(field.value),
                                  "yyyy-MM-dd'T'HH:mm"
                                )
                              : ""
                          }
                          onChange={(e) => field.onChange(e.target.value)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Reviewed At */}
                <FormField
                  control={form.control}
                  name="reviewed_at"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reviewed At</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          {...field}
                          value={
                            field.value
                              ? format(
                                  parseISO(field.value),
                                  "yyyy-MM-dd'T'HH:mm"
                                )
                              : ""
                          }
                          onChange={(e) => field.onChange(e.target.value)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Notes */}
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Enter notes" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Issuing Authority ID */}
                <FormField
                  control={form.control}
                  name="issuing_authority_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Issuing Authority</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(Number(value))}
                        defaultValue={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Issuing Authority" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {issuing_authorities?.map((authority) => (
                            <SelectItem
                              key={authority.id}
                              value={authority.id.toString()}
                            >
                              {authority.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Issue Date */}
                <FormField
                  control={form.control}
                  name="issue_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Issue Date</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          {...field}
                          value={
                            field.value
                              ? format(
                                  parseISO(field.value),
                                  "yyyy-MM-dd'T'HH:mm"
                                )
                              : ""
                          }
                          onChange={(e) => field.onChange(e.target.value)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Expiry Date */}
                <FormField
                  control={form.control}
                  name="expiry_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Expiry Date</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          {...field}
                          value={
                            field.value
                              ? format(
                                  parseISO(field.value),
                                  "yyyy-MM-dd'T'HH:mm"
                                )
                              : ""
                          }
                          onChange={(e) => field.onChange(e.target.value)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Uploaded Individual ID */}
                <FormField
                  control={form.control}
                  name="uploaded_individual_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Uploaded By</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(Number(value))}
                        defaultValue={field.value?.toString()}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Individual" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {individuals?.map((individual) => (
                            <SelectItem
                              key={individual.id}
                              value={individual.id.toString()}
                            >
                              {individual.first_name} {individual.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {error && <p className="text-destructive text-sm">{error}</p>}

                <Button
                  type="submit"
                  className="bg-[#009639] text-white px-6 py-2 rounded-xl shadow hover:bg-[#007A2F] w-full"
                  disabled={isUploading}
                >
                  Submit
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
