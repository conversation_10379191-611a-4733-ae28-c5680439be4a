"use server";
import PostForm from "./post-form-documents";
import { getComplianceRequirementTypes } from "@/actions/compliance-requirement-types";
import MissingParty from "@/components/missing-party";
import { getPartyByExternalID } from "@/actions/party";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import { getAllIssuingAuthorities } from "@/actions/issuing-authorities";
import { getIndividuals } from "@/actions/individuals";
import { getAllVerifications } from "@/actions/verifications";
import { getAllComplianceSets } from "@/actions/compliance-sets";

export default async function ComplianceDocumentUpload() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;
  const [
    compliance_types,
    party,
    compliance_sets,
    individuals,
    issuing_authorities,
  ] = await Promise.all([
    getComplianceRequirementTypes(),
    getPartyByExternalID(externalId),
    getAllComplianceSets(),
    getIndividuals(),
    getAllIssuingAuthorities(),
  ]);
  return (
    <PostForm
      compliance_types={compliance_types}
      party={party}
      compliance_sets={compliance_sets}
      individuals={individuals}
      issuing_authorities={issuing_authorities}
    />
  );
}
