"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Calendar,
  FileText,
  AlertCircle,
  CheckCircle,
  Clock,
  ChevronRight,
  Building,
  Plus,
} from "lucide-react";
import { Document, ComplianceItem, Entity } from "@/types/compliance";
import { formatDateForInput } from "@/lib/utils";

export default function ComplianceDashboardScreen({
  entities,
  complianceItems,
  documents,
}: {
  entities: Entity[];
  complianceItems: ComplianceItem[];
  documents: Document[];
}) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("upcoming");
  const [selectedEntity, setSelectedEntity] = useState<string | null>(null);

  const filteredComplianceItems = complianceItems.filter((item) => {
    if (activeTab === "upcoming" && item.status !== "upcoming") return false;
    if (activeTab === "completed" && item.status !== "completed") return false;
    if (selectedEntity && item.entityId !== selectedEntity) return false;
    return true;
  });

  const filteredDocuments = documents.filter((doc) => {
    if (selectedEntity && doc.entityId !== selectedEntity) return false;
    return true;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-ZA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getDaysUntilDue = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-red-100 text-red-800">
            High Priority
          </span>
        );
      case "medium":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-800">
            Medium Priority
          </span>
        );
      case "low":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-800">
            Low Priority
          </span>
        );
      default:
        return null;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "upcoming":
        return <Clock size={18} className="text-[#009639]" />;
      case "completed":
        return <CheckCircle size={18} className="text-green-500" />;
      default:
        return <AlertCircle size={18} className="text-yellow-500" />;
    }
  };

  const getDocumentStatusBadge = (status: string) => {
    switch (status) {
      case "valid":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-800">
            Valid
          </span>
        );
      case "expiring":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-800">
            Expiring Soon
          </span>
        );
      case "expired":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-red-100 text-red-800">
            Expired
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Compliance Dashboard</h1>
        </div>
        <button
          className="bg-[#007A2F] text-white text-sm font-medium px-3 py-1.5 rounded-full shadow-sm"
          onClick={() => router.push("/legal-entity-formation")}
        >
          Add Entity
        </button>
      </div>

      {/* Compliance Summary */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">
            Compliance Summary
          </h3>
          <div className="grid grid-cols-5 gap-2">
            {[
              { label: "Pending", status: "pending" },
              { label: "Rejected", status: "rejected" },
              { label: "Accepted", status: "accepted" },
              { label: "Incomplete", status: "incomplete" },
            ].map(({ label, status }) => (
              <div
                key={status}
                className="p-2 bg-[#e6ffe6] rounded-md text-center"
              >
                <p className="text-base font-semibold text-[#009639]">
                  {
                    complianceItems.filter((item) => item.status === status)
                      .length
                  }
                </p>
                <p className="text-[10px] text-[#007A2F]">{label}</p>
              </div>
            ))}
            <div className="p-2 bg-[#e6ffe6] rounded-md text-center">
              <p className="text-base font-semibold text-[#009639]">
                {entities.length}
              </p>
              <p className="text-[10px] text-[#007A2F]">Entities</p>
            </div>
          </div>
        </div>
      </div>

      {/* Entity Filter */}
      <div className="bg-white px-4 py-3 border-b border-[#f2f2f2] overflow-x-auto">
        <div className="flex space-x-2">
          <button
            className={`px-3 py-1.5 rounded-full text-sm whitespace-nowrap shadow-sm ${
              selectedEntity === null
                ? "bg-[#009639] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setSelectedEntity(null)}
          >
            All Entities
          </button>
          {entities.map((entity) => (
            <button
              key={entity.id}
              className={`flex items-center px-3 py-1.5 rounded-full text-sm whitespace-nowrap shadow-sm ${
                selectedEntity === entity.id
                  ? "bg-[#009639] text-white"
                  : "bg-[#f2f2f2] text-[#333333]"
              }`}
              onClick={() => setSelectedEntity(entity.id)}
            >
              <Building
                size={14}
                className={`mr-1 ${
                  selectedEntity === entity.id ? "text-white" : "text-[#009639]"
                }`}
              />
              {entity.name}
            </button>
          ))}
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow-sm mb-4">
        <div className="flex px-2">
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "upcoming" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("upcoming")}
          >
            Upcoming
            {activeTab === "upcoming" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "completed" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("completed")}
          >
            Completed
            {activeTab === "completed" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "documents" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("documents")}
          >
            Documents
            {activeTab === "documents" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
        </div>
      </div>

      {/* Compliance Items */}
      {(activeTab === "upcoming" || activeTab === "completed") && (
        <div className="p-4">
          {filteredComplianceItems.length > 0 ? (
            <div className="space-y-3">
              {filteredComplianceItems.map((item) => {
                const entity = entities.find((e) => e.id === item.entityId);
                const daysUntil = getDaysUntilDue(item.dueDate);
                return (
                  <div
                    key={item.id}
                    className="bg-white rounded-xl shadow-md p-4 border border-gray-100"
                    onClick={() =>
                      router.push(`/compliance-details/${item.id}`)
                    }
                  >
                    <div className="flex items-start">
                      <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                        <FileText size={18} className="text-[#009639]" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h3 className="text-[#333333] font-medium">
                            {item.type}
                          </h3>
                          <div className="flex items-center">
                            {getStatusIcon(item.status)}
                            <span className="text-xs text-[#797879] ml-1">
                              {item.status === "upcoming"
                                ? "Upcoming"
                                : "Completed"}
                            </span>
                          </div>
                        </div>
                        <p className="text-xs text-[#797879] mt-1">
                          {entity?.name}
                        </p>
                        <p className="text-sm text-[#333333] mt-1">
                          {item.description}
                        </p>
                        <div className="flex items-center justify-between mt-2">
                          <div className="flex items-center">
                            <Calendar
                              size={14}
                              className="text-[#797879] mr-1"
                            />
                            <span className="text-xs text-[#797879]">
                              {item.status === "upcoming"
                                ? `Due: ${formatDateForInput(item.dueDate)}`
                                : `Completed: ${formatDateForInput(
                                    item.dueDate
                                  )}`}
                            </span>
                          </div>
                          <div>{getPriorityBadge(item.priority)}</div>
                        </div>
                        {item.status === "upcoming" && daysUntil <= 7 && (
                          <div className="mt-2 p-2 bg-red-50 rounded-lg flex items-center">
                            <AlertCircle
                              size={14}
                              className="text-red-500 mr-2"
                            />
                            <span className="text-xs text-red-700">
                              {daysUntil <= 0
                                ? "Overdue!"
                                : `Due in ${daysUntil} days`}
                            </span>
                          </div>
                        )}
                        <div className="flex items-center justify-between mt-2">
                          <div className="text-sm font-medium text-[#333333]">
                            {item.fee > 0 ? `Fee: R${item.fee}` : "No fee"}
                          </div>
                          <ChevronRight size={18} className="text-[#797879]" />
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
              <FileText size={40} className="text-[#d6d9dd] mx-auto mb-3" />
              <p className="text-[#333333] font-medium">
                No {activeTab} compliance items
              </p>
              <p className="text-[#797879] text-sm mt-1">
                {activeTab === "upcoming"
                  ? "You're all caught up!"
                  : "Completed items will appear here"}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Documents Tab */}
      {activeTab === "documents" && (
        <div className="p-4">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-[#333333] font-medium">Entity Documents</h3>
            <button
              className="bg-[#009639] text-white text-sm font-medium flex items-center px-3 py-1.5 rounded-full shadow-sm"
              onClick={() => router.push("/compliance-dashboard/upload")}
            >
              <Plus size={14} className="mr-1" /> Add Document
            </button>
          </div>

          {filteredDocuments.length > 0 ? (
            <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
              {filteredDocuments.map((doc) => {
                const entity = entities.find((e) => e.id === doc.entityId);
                return (
                  <div
                    key={doc.id}
                    className="p-4 border-b border-[#f2f2f2] last:border-b-0"
                    onClick={() => router.push(`/document-details/${doc.id}`)}
                  >
                    <div className="flex items-start">
                      <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                        <FileText size={18} className="text-[#009639]" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h3 className="text-[#333333] font-medium">
                            {doc.name}
                          </h3>
                          {getDocumentStatusBadge(doc.status)}
                        </div>
                        <p className="text-xs text-[#797879] mt-1">
                          {entity?.name}
                        </p>
                        <p className="text-xs text-[#797879] mt-1">
                          {doc.type}
                        </p>
                        <div className="flex items-center justify-between mt-2">
                          <div className="flex items-center">
                            <Calendar
                              size={14}
                              className="text-[#797879] mr-1"
                            />
                            <span className="text-xs text-[#797879]">
                              {formatDate(doc.date)}
                            </span>
                          </div>
                          {doc.expirationDate && (
                            <span className="text-xs text-[#797879]">
                              Expires: {formatDate(doc.expirationDate)}
                            </span>
                          )}
                        </div>
                      </div>
                      <ChevronRight size={18} className="text-[#797879]" />
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
              <FileText size={40} className="text-[#d6d9dd] mx-auto mb-3" />
              <p className="text-[#333333] font-medium">No documents found</p>
              <p className="text-[#797879] text-sm mt-1">
                Upload documents to keep track of your entity compliance
              </p>
              <button
                className="w-full py-3 mt-4 bg-[#009639] text-white rounded-full font-semibold shadow-sm"
                onClick={() => router.push("/compliance-dashboard/upload")}
              >
                Upload Document
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
