"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  Filter,
  Car,
  Calendar,
  Clock,
  User,
  Plus,
} from "lucide-react";

export default function CalendarViewScreen() {
  const router = useRouter();
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedVehicle, setSelectedVehicle] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"month" | "list">("month");

  // Mock data
  const vehicles = [
    {
      id: "1",
      name: "Toyota Hilux",
      image: "/placeholder.svg?height=60&width=100",
    },
    {
      id: "2",
      name: "Volkswagen Polo",
      image: "/placeholder.svg?height=60&width=100",
    },
    {
      id: "3",
      name: "Ford Ranger",
      image: "/placeholder.svg?height=60&width=100",
    },
  ];

  const bookings = [
    {
      id: 1,
      vehicleId: "1",
      userId: "user1",
      userName: "<PERSON> Cooper",
      userImage: "/placeholder.svg?height=40&width=40",
      startDate: new Date(2023, new Date().getMonth(), 5),
      endDate: new Date(2023, new Date().getMonth(), 6),
      type: "booking", // booking, maintenance
    },
    {
      id: 2,
      vehicleId: "1",
      userId: "user2",
      userName: "<PERSON> Fox",
      userImage: "/placeholder.svg?height=40&width=40",
      startDate: new Date(2023, new Date().getMonth(), 12),
      endDate: new Date(2023, new Date().getMonth(), 14),
      type: "booking",
    },
    {
      id: 3,
      vehicleId: "2",
      userId: "user3",
      userName: "Esther Howard",
      userImage: "/placeholder.svg?height=40&width=40",
      startDate: new Date(2023, new Date().getMonth(), 8),
      endDate: new Date(2023, new Date().getMonth(), 10),
      type: "booking",
    },
    {
      id: 4,
      vehicleId: "1",
      startDate: new Date(2023, new Date().getMonth(), 20),
      endDate: new Date(2023, new Date().getMonth(), 21),
      type: "maintenance",
    },
    {
      id: 5,
      vehicleId: "3",
      userId: "user1",
      userName: "Jane Cooper",
      userImage: "/placeholder.svg?height=40&width=40",
      startDate: new Date(2023, new Date().getMonth(), 15),
      endDate: new Date(2023, new Date().getMonth(), 16),
      type: "booking",
    },
  ];

  // Calendar data
  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    return new Date(year, month, 1).getDay();
  };

  const daysInMonth = getDaysInMonth(currentMonth);
  const firstDayOfMonth = getFirstDayOfMonth(currentMonth);
  const monthName = currentMonth.toLocaleString("default", { month: "long" });
  const year = currentMonth.getFullYear();

  const handlePrevMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1)
    );
  };

  const getDayBookings = (day: number) => {
    const date = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      day
    );
    return bookings.filter((booking) => {
      const bookingStart = new Date(booking.startDate);
      const bookingEnd = new Date(booking.endDate);
      return (
        (!selectedVehicle || booking.vehicleId === selectedVehicle) &&
        date >= bookingStart &&
        date <= bookingEnd
      );
    });
  };

  const getDayClass = (day: number) => {
    const dayBookings = getDayBookings(day);
    const baseClass =
      "h-10 w-10 rounded-full flex flex-col items-center justify-center text-sm relative ";

    if (dayBookings.length > 0) {
      const hasMaintenance = dayBookings.some((b) => b.type === "maintenance");
      if (hasMaintenance) {
        return baseClass + "bg-[#ffe6e6] text-[#d32f2f]";
      }
      return baseClass + "bg-[#e6ffe6] text-[#009639] font-medium";
    }

    return baseClass + "text-[#333333] bg-white";
  };

  const getFilteredBookings = () => {
    const currentMonthStart = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      1
    );
    const currentMonthEnd = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth() + 1,
      0
    );

    return bookings
      .filter((booking) => {
        const bookingStart = new Date(booking.startDate);
        const bookingEnd = new Date(booking.endDate);
        return (
          (!selectedVehicle || booking.vehicleId === selectedVehicle) &&
          ((bookingStart >= currentMonthStart &&
            bookingStart <= currentMonthEnd) ||
            (bookingEnd >= currentMonthStart && bookingEnd <= currentMonthEnd))
        );
      })
      .sort((a, b) => a.startDate.getTime() - b.startDate.getTime());
  };

  const getVehicleName = (vehicleId: string) => {
    const vehicle = vehicles.find((v) => v.id === vehicleId);
    return vehicle ? vehicle.name : "Unknown Vehicle";
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Calendar</h1>
        </div>
        <div className="flex space-x-2">
          <button
            className={`px-3 py-1 rounded-full text-sm ${
              viewMode === "month"
                ? "bg-[#007A2F] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setViewMode("month")}
          >
            Month
          </button>
          <button
            className={`px-3 py-1 rounded-full text-sm ${
              viewMode === "list"
                ? "bg-[#007A2F] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setViewMode("list")}
          >
            List
          </button>
        </div>
      </div>

      {/* Vehicle Filter */}
      <div className="bg-white px-4 py-3 border-b border-[#f2f2f2] overflow-x-auto shadow-sm">
        <div className="flex space-x-2">
          <button
            className={`px-3 py-1 rounded-full text-sm whitespace-nowrap ${
              selectedVehicle === null
                ? "bg-[#009639] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setSelectedVehicle(null)}
          >
            All Vehicles
          </button>
          {vehicles.map((vehicle) => (
            <button
              key={vehicle.id}
              className={`flex items-center px-3 py-1 rounded-full text-sm whitespace-nowrap ${
                selectedVehicle === vehicle.id
                  ? "bg-[#009639] text-white"
                  : "bg-[#f2f2f2] text-[#333333]"
              }`}
              onClick={() => setSelectedVehicle(vehicle.id)}
            >
              <Car size={14} className="mr-1" />
              {vehicle.name}
            </button>
          ))}
        </div>
      </div>

      {/* Month Navigation */}
      <div className="p-4 flex items-center justify-between bg-white border-b border-[#f2f2f2] shadow-sm">
        <button
          className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center shadow-md"
          onClick={handlePrevMonth}
        >
          <ChevronLeft size={20} className="text-[#009639]" />
        </button>
        <h2 className="text-lg font-semibold text-[#333333]">
          {monthName} {year}
        </h2>
        <button
          className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center shadow-md"
          onClick={handleNextMonth}
        >
          <ChevronRight size={20} className="text-[#009639]" />
        </button>
      </div>

      {/* Month View */}
      {viewMode === "month" && (
        <div className="p-4 bg-white">
          {/* Weekdays */}
          <div className="grid grid-cols-7 mb-2">
            {["S", "M", "T", "W", "T", "F", "S"].map((day, index) => (
              <div
                key={index}
                className="text-center text-[#797879] text-sm font-medium"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Days */}
          <div className="grid grid-cols-7 gap-1">
            {/* Empty cells for days before the first day of month */}
            {Array.from({ length: firstDayOfMonth }).map((_, index) => (
              <div key={`empty-${index}`} className="h-10"></div>
            ))}

            {/* Actual days */}
            {Array.from({ length: daysInMonth }).map((_, index) => {
              const day = index + 1;
              const dayBookings = getDayBookings(day);

              return (
                <div key={day} className="flex justify-center items-center">
                  <div className={getDayClass(day)}>
                    {day}
                    {dayBookings.length > 0 && (
                      <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
                        <div className="flex space-x-0.5">
                          {dayBookings.length > 3 ? (
                            <div className="w-1 h-1 bg-[#009639] rounded-full"></div>
                          ) : (
                            dayBookings.map((booking, i) => (
                              <div
                                key={i}
                                className={`w-1 h-1 rounded-full ${
                                  booking.type === "maintenance"
                                    ? "bg-red-500"
                                    : "bg-[#009639]"
                                }`}
                              ></div>
                            ))
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* List View */}
      {viewMode === "list" && (
        <div className="p-4">
          <div className="ride-card overflow-hidden shadow-md rounded-xl border border-gray-100">
            {getFilteredBookings().length > 0 ? (
              getFilteredBookings().map((booking) => (
                <div
                  key={booking.id}
                  className="p-4 border-b border-[#f2f2f2] last:border-b-0 bg-white"
                >
                  <div className="flex items-start">
                    {booking.type === "booking" ? (
                      <div className="w-10 h-10 ride-avatar mr-3 flex-shrink-0">
                        <Image
                          src={booking.userImage || "/placeholder.svg"}
                          alt={booking.userName || ""}
                          width={40}
                          height={40}
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-10 h-10 bg-[#ffe6e6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                        <Car size={18} className="text-[#d32f2f]" />
                      </div>
                    )}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="text-[#333333] font-medium">
                          {booking.type === "booking"
                            ? booking.userName
                            : "Maintenance"}
                        </h3>
                        <span
                          className={`text-xs px-2 py-0.5 rounded-full ${
                            booking.type === "maintenance"
                              ? "bg-[#ffe6e6] text-[#d32f2f]"
                              : "bg-[#e6ffe6] text-[#009639]"
                          }`}
                        >
                          {booking.type === "maintenance"
                            ? "Maintenance"
                            : "Booking"}
                        </span>
                      </div>
                      <p className="text-xs text-[#797879]">
                        {getVehicleName(booking.vehicleId)}
                      </p>
                      <div className="flex items-center mt-1">
                        <Calendar size={12} className="text-[#797879] mr-1" />
                        <span className="text-xs text-[#797879]">
                          {formatDate(new Date(booking.startDate))} -{" "}
                          {formatDate(new Date(booking.endDate))}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-6 text-center">
                <p className="text-[#797879]">No bookings for this month</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="p-4 bg-white border-t border-[#f2f2f2]">
        <div className="flex flex-wrap gap-3">
          <div className="flex items-center">
            <div className="h-4 w-4 rounded-full bg-[#e6ffe6] mr-1"></div>
            <span className="text-xs text-[#333333]">Bookings</span>
          </div>
          <div className="flex items-center">
            <div className="h-4 w-4 rounded-full bg-[#ffe6e6] mr-1"></div>
            <span className="text-xs text-[#333333]">Maintenance</span>
          </div>
          <div className="flex items-center">
            <div className="h-4 w-4 rounded-full bg-white border border-[#d6d9dd] mr-1"></div>
            <span className="text-xs text-[#333333]">Available</span>
          </div>
        </div>
      </div>

      {/* Add Booking Button */}
      <div className="fixed bottom-20 right-4">
        <button
          className="w-14 h-14 bg-[#009639] rounded-full flex items-center justify-center shadow-lg"
          onClick={() => router.push("/booking-calendar/1")}
        >
          <Plus size={24} className="text-white" />
        </button>
      </div>
    </div>
  );
}
