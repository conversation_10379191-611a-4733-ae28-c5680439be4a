"use client";

import { useState, use } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Share,
  Heart,
  Car,
  Calendar,
  MapPin,
  DollarSign,
  Info,
  Clock,
  Check,
  ChevronRight,
  MessageSquare,
  Phone,
} from "lucide-react";

export default function VehicleDetailsScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const { id } = use(params);
  const vehicleId = id;
  const [activeTab, setActiveTab] = useState("details");
  const [isFavorite, setIsFavorite] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Mock data - in a real app, this would come from an API
  const vehicle = {
    id: vehicleId,
    make: "Tesla",
    model: "Model 3",
    year: 2022,
    price: 45000,
    location: "San Francisco, CA",
    distance: "5 miles away",
    type: "Electric",
    color: "Midnight Silver",
    mileage: 12500,
    fuelType: "Electric",
    transmission: "Automatic",
    drivetrain: "All-Wheel Drive",
    vin: "5YJ3E1EA1MF123456",
    description:
      "This Tesla Model 3 is in excellent condition with low mileage. Features include Autopilot, premium interior, and long-range battery. Perfect for city driving and weekend getaways.",
    features: [
      "Autopilot",
      "Premium Interior",
      "Long Range Battery",
      "Glass Roof",
      "Heated Seats",
      "Premium Sound System",
      '19" Sport Wheels',
      "White Interior",
    ],
    serviceHistory: [
      {
        date: "2023-01-15",
        mileage: 10000,
        service: "Regular Maintenance",
        description: "Software update, tire rotation, brake inspection",
      },
      {
        date: "2022-07-20",
        mileage: 5000,
        service: "Regular Maintenance",
        description: "Software update, cabin filter replacement",
      },
    ],
    images: [
      "/placeholder.svg?height=300&width=500&text=Tesla+Model+3+Front",
      "/placeholder.svg?height=300&width=500&text=Tesla+Model+3+Side",
      "/placeholder.svg?height=300&width=500&text=Tesla+Model+3+Interior",
      "/placeholder.svg?height=300&width=500&text=Tesla+Model+3+Rear",
    ],
    seller: {
      id: "seller1",
      name: "EV Motors",
      rating: 4.8,
      verified: true,
      responseRate: "95%",
      responseTime: "Within 1 hour",
      memberSince: "2019",
      phone: "+****************",
      email: "<EMAIL>",
      image: "/placeholder.svg?height=60&width=60",
    },
  };

  const handleNextImage = () => {
    setCurrentImageIndex((prev) =>
      prev === vehicle.images.length - 1 ? 0 : prev + 1
    );
  };

  const handlePrevImage = () => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? vehicle.images.length - 1 : prev - 1
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="min-h-screen bg-[#eef1f9]">
      {/* Status Bar */}
      <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#eef1f9"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white px-6 py-4 flex items-center justify-between border-b border-[#f2f2f2]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-[#333333]" />
        </button>
        <h1 className="text-xl font-bold text-[#333333] flex-1">
          Vehicle Details
        </h1>
        <div className="flex space-x-3">
          <button
            className="w-10 h-10 bg-[#f2f2f2] rounded-full flex items-center justify-center"
            onClick={() => setIsFavorite(!isFavorite)}
          >
            <Heart
              size={20}
              className={
                isFavorite ? "fill-red-500 text-red-500" : "text-[#333333]"
              }
            />
          </button>
          <button className="w-10 h-10 bg-[#f2f2f2] rounded-full flex items-center justify-center">
            <Share size={20} className="text-[#333333]" />
          </button>
        </div>
      </div>

      {/* Image Gallery */}
      <div className="relative bg-[#f2f2f2] h-64">
        <Image
          src={vehicle.images[currentImageIndex]}
          alt={`${vehicle.make} ${vehicle.model}`}
          fill
          className="object-cover"
        />
        <div className="absolute bottom-4 left-0 right-0 flex justify-center">
          <div className="flex space-x-1 px-2 py-1 bg-black bg-opacity-50 rounded-full">
            {vehicle.images.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentImageIndex
                    ? "bg-white"
                    : "bg-white bg-opacity-50"
                }`}
                onClick={() => setCurrentImageIndex(index)}
              ></div>
            ))}
          </div>
        </div>
        <button
          className="absolute left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white bg-opacity-70 rounded-full flex items-center justify-center"
          onClick={handlePrevImage}
        >
          <ChevronLeft size={20} className="text-[#333333]" />
        </button>
        <button
          className="absolute right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white bg-opacity-70 rounded-full flex items-center justify-center"
          onClick={handleNextImage}
        >
          <ChevronRight size={20} className="text-[#333333]" />
        </button>
      </div>

      {/* Vehicle Title and Price */}
      <div className="bg-white p-4 border-b border-[#f2f2f2]">
        <h2 className="text-xl font-bold text-[#333333]">
          {vehicle.year} {vehicle.make} {vehicle.model}
        </h2>
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center">
            <DollarSign size={18} className="text-[#0286ff] mr-1" />
            <span className="text-xl font-bold text-[#333333]">
              ${vehicle.price.toLocaleString()}
            </span>
          </div>
          <div className="flex items-center">
            <MapPin size={14} className="text-[#797879] mr-1" />
            <span className="text-sm text-[#797879]">{vehicle.distance}</span>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-[#f2f2f2]">
        <div className="flex">
          <button
            className={`flex-1 py-3 text-sm font-medium ${
              activeTab === "details"
                ? "text-[#0286ff] border-b-2 border-[#0286ff]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("details")}
          >
            Details
          </button>
          <button
            className={`flex-1 py-3 text-sm font-medium ${
              activeTab === "features"
                ? "text-[#0286ff] border-b-2 border-[#0286ff]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("features")}
          >
            Features
          </button>
          <button
            className={`flex-1 py-3 text-sm font-medium ${
              activeTab === "history"
                ? "text-[#0286ff] border-b-2 border-[#0286ff]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("history")}
          >
            History
          </button>
          <button
            className={`flex-1 py-3 text-sm font-medium ${
              activeTab === "seller"
                ? "text-[#0286ff] border-b-2 border-[#0286ff]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("seller")}
          >
            Seller
          </button>
        </div>
      </div>

      {/* Details Tab */}
      {activeTab === "details" && (
        <div className="p-4">
          <div className="ride-card p-4 mb-4">
            <h3 className="text-[#333333] font-medium mb-3">
              Vehicle Specifications
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-xs text-[#797879]">Make</p>
                <p className="text-sm text-[#333333] font-medium">
                  {vehicle.make}
                </p>
              </div>
              <div>
                <p className="text-xs text-[#797879]">Model</p>
                <p className="text-sm text-[#333333] font-medium">
                  {vehicle.model}
                </p>
              </div>
              <div>
                <p className="text-xs text-[#797879]">Year</p>
                <p className="text-sm text-[#333333] font-medium">
                  {vehicle.year}
                </p>
              </div>
              <div>
                <p className="text-xs text-[#797879]">Color</p>
                <p className="text-sm text-[#333333] font-medium">
                  {vehicle.color}
                </p>
              </div>
              <div>
                <p className="text-xs text-[#797879]">Mileage</p>
                <p className="text-sm text-[#333333] font-medium">
                  {vehicle.mileage.toLocaleString()} miles
                </p>
              </div>
              <div>
                <p className="text-xs text-[#797879]">Fuel Type</p>
                <p className="text-sm text-[#333333] font-medium">
                  {vehicle.fuelType}
                </p>
              </div>
              <div>
                <p className="text-xs text-[#797879]">Transmission</p>
                <p className="text-sm text-[#333333] font-medium">
                  {vehicle.transmission}
                </p>
              </div>
              <div>
                <p className="text-xs text-[#797879]">Drivetrain</p>
                <p className="text-sm text-[#333333] font-medium">
                  {vehicle.drivetrain}
                </p>
              </div>
              <div className="col-span-2">
                <p className="text-xs text-[#797879]">VIN</p>
                <p className="text-sm text-[#333333] font-medium">
                  {vehicle.vin}
                </p>
              </div>
            </div>
          </div>

          <div className="ride-card p-4">
            <h3 className="text-[#333333] font-medium mb-2">Description</h3>
            <p className="text-sm text-[#333333]">{vehicle.description}</p>
          </div>
        </div>
      )}

      {/* Features Tab */}
      {activeTab === "features" && (
        <div className="p-4">
          <div className="ride-card p-4">
            <h3 className="text-[#333333] font-medium mb-3">
              Vehicle Features
            </h3>
            <div className="grid grid-cols-1 gap-3">
              {vehicle.features.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <Check size={16} className="text-[#0286ff] mr-2" />
                  <span className="text-sm text-[#333333]">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* History Tab */}
      {activeTab === "history" && (
        <div className="p-4">
          <div className="ride-card p-4">
            <h3 className="text-[#333333] font-medium mb-3">Service History</h3>
            {vehicle.serviceHistory.length > 0 ? (
              <div className="space-y-4">
                {vehicle.serviceHistory.map((service, index) => (
                  <div
                    key={index}
                    className={`pb-4 ${
                      index < vehicle.serviceHistory.length - 1
                        ? "border-b border-[#f2f2f2]"
                        : ""
                    }`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="text-[#333333] font-medium">
                        {service.service}
                      </h4>
                      <span className="text-xs text-[#797879]">
                        {formatDate(service.date)}
                      </span>
                    </div>
                    <p className="text-xs text-[#797879] mb-1">
                      Mileage: {service.mileage.toLocaleString()} miles
                    </p>
                    <p className="text-sm text-[#333333]">
                      {service.description}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-[#797879]">
                No service history available
              </p>
            )}
          </div>
        </div>
      )}

      {/* Seller Tab */}
      {activeTab === "seller" && (
        <div className="p-4">
          <div className="ride-card p-4">
            <div className="flex items-center mb-4">
              <div className="w-14 h-14 bg-[#f2f2f2] rounded-full overflow-hidden mr-3">
                <Image
                  src={vehicle.seller.image}
                  alt={vehicle.seller.name}
                  width={56}
                  height={56}
                  className="object-cover"
                />
              </div>
              <div>
                <div className="flex items-center">
                  <h3 className="text-[#333333] font-medium mr-2">
                    {vehicle.seller.name}
                  </h3>
                  {vehicle.seller.verified && (
                    <span className="text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-800">
                      Verified
                    </span>
                  )}
                </div>
                <div className="flex items-center mt-1">
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="#ff5c00"
                  >
                    <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" />
                  </svg>
                  <span className="ml-1 text-sm text-[#333333]">
                    {vehicle.seller.rating}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center">
                <Clock size={16} className="text-[#0286ff] mr-2" />
                <div>
                  <p className="text-xs text-[#797879]">Response Time</p>
                  <p className="text-sm text-[#333333]">
                    {vehicle.seller.responseTime}
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <MessageSquare size={16} className="text-[#0286ff] mr-2" />
                <div>
                  <p className="text-xs text-[#797879]">Response Rate</p>
                  <p className="text-sm text-[#333333]">
                    {vehicle.seller.responseRate}
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <Calendar size={16} className="text-[#0286ff] mr-2" />
                <div>
                  <p className="text-xs text-[#797879]">Member Since</p>
                  <p className="text-sm text-[#333333]">
                    {vehicle.seller.memberSince}
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-[#f2f2f2] flex space-x-3">
              <button
                className="flex-1 ride-primary-btn py-3 flex items-center justify-center"
                onClick={() => router.push(`/chat/${vehicle.seller.id}`)}
              >
                <MessageSquare size={18} className="mr-2" /> Message
              </button>
              <button
                className="flex-1 ride-secondary-btn py-3 flex items-center justify-center"
                onClick={() => window.open(`tel:${vehicle.seller.phone}`)}
              >
                <Phone size={18} className="mr-2" /> Call
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] flex space-x-3">
        <button
          className="flex-1 ride-secondary-btn py-4 flex items-center justify-center"
          onClick={() => router.push(`/vehicle-purchase-planner/${vehicleId}`)}
        >
          <Car size={20} className="mr-2" /> Purchase Planner
        </button>
        <button
          className="flex-1 ride-primary-btn py-4 flex items-center justify-center"
          onClick={() => router.push(`/share-with-group/${vehicleId}`)}
        >
          <Share size={20} className="mr-2" /> Share with Group
        </button>
      </div>
    </div>
  );
}
