import { getUserAttributes } from "@/lib/amplifyServerUtils";
import MissingParty from "@/components/missing-party";
import ListVehicleForm from "./list-vehicle-form";


export default async function ListVehiclePage() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId } = attributes || {};
  
  if (!dbId) return <MissingParty />;

  return <ListVehicleForm partyId={+dbId} />;
}

