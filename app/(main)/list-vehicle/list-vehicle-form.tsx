"use client";

import type React from "react";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Upload,
  DollarSign,
  Percent,
  FileText,
  ChevronDown,
  ChevronUp,
  X,
  Loader,
} from "lucide-react";
import { createVehicleAndListingDrizzle } from "@/drizzle-actions/listings";
import { DocumentUpload, DocumentDelete } from "@/lib/utils";

interface ListVehicleFormProps {
  partyId: number;
}

interface UploadedImage {
  file: File;
  previewUrl: string;
  s3Path: string;
}

export default function ListVehicleForm({ partyId }: ListVehicleFormProps) {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [vehicleImages, setVehicleImages] = useState<UploadedImage[]>([]);
  const [showTerms, setShowTerms] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState("");
  const [formData, setFormData] = useState({
    make: "",
    model: "",
    year: "",
    color: "",
    mileage: "",
    condition: "used", // Default to 'used' since most cars will be used
    description: "",
    fractionOffer: "", // Changed from fractionSize and fractions to single number input
    pricePerFraction: "",
    termsAccepted: false,
  });

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;
    
    setFormData({
      ...formData,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    });
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setIsUploading(true);
      setError("");
      
      try {
        const uploadPromises = Array.from(files).map(async (file) => {
          // Upload to S3 using DocumentUpload
          const uploadResult = await DocumentUpload(file, "listingsMedia");
          
          if (uploadResult && uploadResult.path) {
            return {
              file,
              previewUrl: URL.createObjectURL(file),
              s3Path: uploadResult.path,
            };
          }
          throw new Error("Upload failed");
        });
        
        const uploadedImages = await Promise.all(uploadPromises);
        setVehicleImages(prev => [...prev, ...uploadedImages]);
      } catch (err: any) {
        console.error("Error uploading images:", err);
        setError(err?.message || "Failed to upload images. Please try again.");
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleRemoveImage = async (index: number) => {
    const imageToRemove = vehicleImages[index];
    if (!imageToRemove) return;
    
    setIsUploading(true);
    try {
      // Delete from S3
      await DocumentDelete(imageToRemove.s3Path);
      
      // Revoke preview URL
      URL.revokeObjectURL(imageToRemove.previewUrl);
      
      // Remove from state
      setVehicleImages(prev => prev.filter((_, i) => i !== index));
    } catch (error) {
      console.error("Error deleting image:", error);
      setError("Failed to delete image. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (vehicleImages.length < 3) {
      setError("Please upload at least 3 images of your vehicle.");
      return;
    }
    
    try {
      setIsUploading(true);
      setError("");
      
      // Prepare form data for the drizzle action
      const listingFormData = {
        make: formData.make,
        model: formData.model,
        year: formData.year,
        color: formData.color,
        mileage: formData.mileage,
        condition: formData.condition,
        location: "", // Removed location from form
        description: formData.description,
        vinNumber: `VIN${Date.now()}`, // Generate a VIN
        vehicleRegistration: undefined,
        fractionSize: "custom", // For compatibility with existing interface
        fractions: 1, // Default value for compatibility
        pricePerFraction: formData.pricePerFraction,
        listingType: "CO_OWNERSHIP_SALE",
        audience: "CONSUMER",
        effectiveFrom: new Date().toISOString().split('T')[0],
        effectiveTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        vehicleImages: vehicleImages.map(img => img.s3Path), // Use S3 paths instead of preview URLs
        fractionOffer: formData.fractionOffer, // Pass the fraction offer
      };
      
      // Create vehicle and listing using drizzle actions
      const result = await createVehicleAndListingDrizzle(listingFormData, partyId);
      
      console.log("Vehicle and listing created:", result);
      
      // Clean up preview URLs
      vehicleImages.forEach(img => URL.revokeObjectURL(img.previewUrl));
      
      // Redirect to opportunities or vehicle dashboard
      router.push("/opportunities");
    } catch (error) {
      console.error("Error creating vehicle listing:", error);
      setError("Failed to create listing. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">List Vehicle</h1>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="p-4 space-y-4">
        {/* Vehicle Images */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-[#333333] font-medium">Vehicle Photos</h3>
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
              disabled={isUploading}
            />
            <button
              type="button"
              className="bg-[#009639] text-white p-2 rounded-full shadow-sm disabled:opacity-50"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              {isUploading ? <Loader size={20} className="animate-spin" /> : <Upload size={20} />}
            </button>
          </div>

          {vehicleImages.length > 0 ? (
            <div className="grid grid-cols-3 gap-2">
              {vehicleImages.map((image, index) => (
                <div
                  key={index}
                  className="relative h-24 bg-[#f2f2f2] rounded-lg overflow-hidden"
                >
                  <Image
                    src={image.previewUrl}
                    alt={`Vehicle photo ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                  <button
                    type="button"
                    className="absolute top-1 right-1 bg-white rounded-full p-1 disabled:opacity-50"
                    onClick={() => handleRemoveImage(index)}
                    disabled={isUploading}
                  >
                    <X size={14} className="text-red-500" />
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <div
              className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload size={32} className="text-[#009639] mb-2" />
              <p className="text-[#797879] text-center">
                Upload photos of your vehicle (min. 3 photos)
              </p>
            </div>
          )}
        </div>

        {/* Vehicle Details */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-4">Vehicle Details</h3>

          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label
                  htmlFor="make"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Make
                </label>
                <input
                  type="text"
                  id="make"
                  name="make"
                  value={formData.make}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  placeholder="e.g. Toyota, Volkswagen"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="model"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Model
                </label>
                <input
                  type="text"
                  id="model"
                  name="model"
                  value={formData.model}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  placeholder="e.g. Fortuner, Polo"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label
                  htmlFor="year"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Year
                </label>
                <input
                  type="text"
                  id="year"
                  name="year"
                  value={formData.year}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  placeholder="e.g. 2020"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="color"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Color
                </label>
                <input
                  type="text"
                  id="color"
                  name="color"
                  value={formData.color}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  placeholder="e.g. Blue"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label
                  htmlFor="mileage"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Mileage
                </label>
                <input
                  type="text"
                  id="mileage"
                  name="mileage"
                  value={formData.mileage}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  placeholder="e.g. 25,000 km"
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="condition"
                  className="block text-sm text-[#797879] mb-1"
                >
                  Condition
                </label>
                <select
                  id="condition"
                  name="condition"
                  value={formData.condition}
                  onChange={handleChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm bg-white"
                  required
                >
                  <option value="new">New</option>
                  <option value="used">Used</option>
                </select>
              </div>
            </div>

            <div>
              <label
                htmlFor="description"
                className="block text-sm text-[#797879] mb-1"
              >
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm min-h-[100px]"
                placeholder="Describe your vehicle..."
                required
              />
            </div>
          </div>
        </div>

        {/* Fraction Offer */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center mb-4">
            <Percent size={20} className="text-[#009639] mr-2" />
            <h3 className="text-[#333333] font-medium">
              Fraction Offer
            </h3>
          </div>

          <div>
            <label
              htmlFor="fractionOffer"
              className="block text-sm text-[#797879] mb-1"
            >
              Fraction Size (as decimal)
            </label>
            <input
              type="number"
              id="fractionOffer"
              name="fractionOffer"
              value={formData.fractionOffer}
              onChange={handleChange}
              step="0.01"
              min="0.01"
              max="1.00"
              className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
              placeholder="e.g. 0.25 for 25%, 0.5 for 50%"
              required
            />
            <p className="text-xs text-[#797879] mt-2">
              Enter the fraction as a decimal (e.g., 0.25 = 25%, 0.5 = 50%)
            </p>
          </div>
        </div>

        {/* Pricing */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center mb-4">
            <DollarSign size={20} className="text-[#009639] mr-2" />
            <h3 className="text-[#333333] font-medium">Pricing per Fraction</h3>
          </div>

          <div>
            <div className="relative">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <span className="text-[#797879] font-medium">R</span>
              </div>
              <input
                type="text"
                id="pricePerFraction"
                name="pricePerFraction"
                value={formData.pricePerFraction}
                onChange={handleChange}
                className="w-full pl-10 pr-4 py-3 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                placeholder="Price per fraction in Rand"
                required
              />
            </div>
            <p className="text-xs text-[#797879] mt-2">
              This is the price for the {parseFloat(formData.fractionOffer || "0") * 100}% share of the vehicle.
            </p>
          </div>
        </div>

        {/* Terms and Conditions */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <button
            type="button"
            className="w-full flex items-center justify-between"
            onClick={() => setShowTerms(!showTerms)}
          >
            <div className="flex items-center">
              <FileText size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                Terms and Conditions
              </h3>
            </div>
            {showTerms ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          </button>

          {showTerms && (
            <div className="mt-3 p-3 bg-[#f9f9f9] rounded-lg max-h-40 overflow-y-auto">
              <p className="text-sm text-[#797879]">
                By listing your vehicle on Poolly, you agree to the following
                terms:
                <br />
                <br />
                1. You confirm that all information provided about the vehicle
                is accurate and complete.
                <br />
                <br />
                2. You have the legal right to sell or offer fractions of this
                vehicle in accordance with South African law.
                <br />
                <br />
                3. You agree to the standard Poolly co-ownership agreement that
                will govern the relationship between all co-owners in South
                Africa.
                <br />
                <br />
                4. Poolly will charge a 5% service fee on the total transaction
                value when fractions are sold in Rand (ZAR).
                <br />
                <br />
                5. You agree to maintain the vehicle in good condition according
                to South African roadworthiness standards until all fractions
                are sold.
              </p>
            </div>
          )}

          <div className="mt-3 flex items-start">
            <input
              type="checkbox"
              id="termsAccepted"
              name="termsAccepted"
              checked={formData.termsAccepted}
              onChange={(e) =>
                setFormData({ ...formData, termsAccepted: e.target.checked })
              }
              className="mt-1 h-4 w-4 rounded border-[#d6d9dd] text-[#009639] focus:ring-[#009639]"
              required
            />
            <label
              htmlFor="termsAccepted"
              className="ml-2 text-sm text-[#797879]"
            >
              I agree to the terms and conditions for listing my vehicle on
              Poolly
            </label>
          </div>
        </div>
      </form>

      {/* List Vehicle Button */}
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2]">
        <button
          type="submit"
          className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-4 rounded-full text-xl font-semibold shadow-md"
          onClick={handleSubmit}
          disabled={!formData.termsAccepted}
        >
          List Vehicle
        </button>
      </div>
    </div>
  );
} 