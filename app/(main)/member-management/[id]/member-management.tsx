"use client";

import { useState, use } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft, UserPlus, Trash2, Edit, Mail, Phone } from "lucide-react";
import type { CompanyMembershipRead } from "@/types/company-ownerships";
import type { ContactPointRead } from "@/types/contact-points";
import GetContactsByPartyId from "./cotactpoints";

export default function MemberManagementScreen({
  ownerships,
  contacts,
  groupId,
}: {
  ownerships: CompanyMembershipRead;
  contacts: ContactPointRead[];
  groupId: number;
}) {
  const router = useRouter();

  const [members, setMembers] = useState(ownerships.individuals);

  const [showOwnershipModal, setShowOwnershipModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<number | null>(null);
  const [newOwnership, setNewOwnership] = useState<number>(0);

  const handleEditOwnership = (memberId: number, currentOwnership: number) => {
    setSelectedMember(memberId);
    setNewOwnership(currentOwnership);
    setShowOwnershipModal(true);
  };

  const handleSaveOwnership = () => {};

  const handleRemoveMember = (memberId: number) => {};
  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Member Management</h1>
      </div>

      {/* Members List */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
          {members.map((member, index) => (
            <div
              key={member.individual.id}
              className={`p-4 ${
                index < members.length - 1 ? "border-b border-[#f2f2f2]" : ""
              }`}
            >
              <div className="flex items-center mb-3">
                <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <span className="text-[#009639] font-medium text-lg">
                    {member.individual.first_name.charAt(0)}
                  </span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center">
                    <h3 className="text-[#333333] font-semibold">
                      {member.individual.first_name}{" "}
                      {member.individual.last_name}
                    </h3>
                    {/* {member.isAdmin && (
                      <span className="ml-2 text-xs bg-[#e6ffe6] text-[#009639] px-2 py-0.5 rounded-full">
                        Admin
                      </span>
                    )} */}
                  </div>
                  <div className="flex items-center text-xs text-[#797879] mt-1">
                    <span className="bg-[#f2f2f2] px-2 py-0.5 rounded-full">
                      {member.fraction}% ownership
                    </span>
                  </div>
                </div>
                <button
                  className="p-2 text-[#009639]"
                  onClick={() =>
                    handleEditOwnership(member.individual.id, member.fraction)
                  }
                >
                  <Edit size={18} />
                </button>
              </div>
              <GetContactsByPartyId
                party_id={member.individual.party_id}
                contacts={contacts}
              />
              <div className="flex justify-between mt-3 pt-3 border-t border-[#f2f2f2]">
                <button
                  className={`text-sm px-3 py-1 rounded-full bg-[#f2f2f2] text-[#bcbcbc]`}
                  onClick={() => handleRemoveMember(member.individual.id)}
                  disabled={member.individual.id === 1}
                >
                  <Trash2 size={14} className="mr-1 inline-block" />
                  Remove
                </button>
              </div>
            </div>
          ))}
        </div>

        <button
          className="w-full bg-white rounded-xl shadow-md p-4 flex items-center justify-center text-[#009639] font-medium border border-gray-100"
          onClick={() => router.push(`/add-members/${groupId}`)}
        >
          <UserPlus size={20} className="mr-2" />
          Add New Member
        </button>
      </div>

      {/* Ownership Reallocation Tools */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-semibold mb-3">
            Ownership Distribution
          </h3>

          <div className="h-4 bg-[#f2f2f2] rounded-full overflow-hidden flex mb-3">
            {members.map((member) => (
              <div
                key={member.individual.id}
                className="h-full"
                style={{
                  width: `${member.fraction}%`,
                  backgroundColor: "#00d154",
                }}
              ></div>
            ))}
          </div>

          <div className="flex flex-wrap gap-2">
            {members.map((member) => (
              <div
                key={member.individual.id}
                className="flex items-center text-xs"
              >
                <div
                  className="w-3 h-3 rounded-full mr-1"
                  style={{
                    backgroundColor: "#00d154",
                  }}
                ></div>
                <span>
                  {member.individual.first_name.split(" ")[0]} (
                  {member.fraction}%)
                </span>
              </div>
            ))}
          </div>

          <button
            className="w-full mt-4 border border-[#009639] text-[#009639] py-2 rounded-full text-sm font-medium shadow-sm"
            onClick={() => router.push(`/ownership-reallocation/${groupId}`)}
          >
            Reallocate Ownership
          </button>
        </div>
      </div>

      {/* Ownership Modal */}
      {showOwnershipModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-4 w-5/6 max-w-md shadow-lg border border-gray-100">
            <h3 className="text-[#333333] font-semibold mb-4">
              Edit Ownership Percentage
            </h3>

            <div className="mb-4">
              <label className="block text-[#797879] text-sm mb-1">
                Ownership Percentage
              </label>
              <input
                type="number"
                min="1"
                max="100"
                value={newOwnership}
                onChange={(e) =>
                  setNewOwnership(Number.parseInt(e.target.value) || 0)
                }
                className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
              />
            </div>

            <div className="flex space-x-2">
              <button
                className="flex-1 border border-[#d6d9dd] text-[#797879] py-2 rounded-full shadow-sm"
                onClick={() => setShowOwnershipModal(false)}
              >
                Cancel
              </button>
              <button
                className="flex-1 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-2 rounded-full shadow-md"
                onClick={handleSaveOwnership}
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
