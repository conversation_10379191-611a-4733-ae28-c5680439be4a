"use client";
import type { ContactPointRead } from "@/types/contact-points";
import { Mail, Phone } from "lucide-react";

interface ContactDisplayProps {
  party_id: number;
  contacts: ContactPointRead[];
}

const GetContactsByPartyId = ({ contacts, party_id }: ContactDisplayProps) => {
  const email = contacts.find(
    (contact) =>
      contact.party_id === party_id &&
      contact.contact_point_type.name === "email"
  );

  const phone = contacts.find(
    (contact) =>
      contact.party_id === party_id &&
      contact.contact_point_type.name === "phone"
  );
  console.log(party_id, contacts, email);
  return (
    <div className="space-y-2 pl-4 ml-4">
      {email && (
        <div className="flex items-center text-sm text-[#797879]">
          <Mail size={14} className="mr-2" />
          {email.value}
        </div>
      )}
      {phone && (
        <div className="flex items-center text-sm text-[#797879]">
          <Phone size={14} className="mr-2" />
          {phone.value}
        </div>
      )}
    </div>
  );
};

export default GetContactsByPartyId;
