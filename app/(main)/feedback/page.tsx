"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft, Star, Send, Camera, Paperclip, X } from "lucide-react";

export default function FeedbackScreen() {
  const router = useRouter();
  const [rating, setRating] = useState<number | null>(null);
  const [feedback, setFeedback] = useState("");
  const [category, setCategory] = useState("general");
  const [attachments, setAttachments] = useState<string[]>([]);

  const handleSubmit = () => {
    console.log("Submitting feedback:", {
      rating,
      feedback,
      category,
      attachments,
    });
    // In a real app, you would make an API call here
    router.push("/profile");
  };

  const handleAddAttachment = () => {
    // In a real app, this would open a file picker
    const mockAttachment = `/placeholder.svg?height=100&width=100&text=Attachment${
      attachments.length + 1
    }`;
    setAttachments([...attachments, mockAttachment]);
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#eef1f9"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Give Feedback</h1>
      </div>

      {/* Feedback Form */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 space-y-6 border border-gray-100">
          {/* Rating */}
          <div>
            <h3 className="text-[#333333] font-medium mb-3">
              How would you rate your experience?
            </h3>
            <div className="flex justify-center space-x-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  className="p-2"
                  onClick={() => setRating(star)}
                >
                  <Star
                    size={32}
                    className={
                      rating && star <= rating
                        ? "fill-[#FFD700] text-[#FFD700]"
                        : "text-[#d6d9dd]"
                    }
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Category */}
          <div>
            <label className="text-[#333333] font-medium mb-2 block">
              Feedback Category
            </label>
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              className="w-full p-3 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
            >
              <option value="general">General Feedback</option>
              <option value="app">App Experience</option>
              <option value="vehicle">
                Vehicle Experience in South Africa
              </option>
              <option value="group">Group Management</option>
              <option value="payment">Payment & Billing (ZAR)</option>
              <option value="bug">Bug Report</option>
            </select>
          </div>

          {/* Feedback Text */}
          <div>
            <label className="text-[#333333] font-medium mb-2 block">
              Your Feedback
            </label>
            <textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Tell us what you think..."
              className="w-full p-3 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm min-h-[150px]"
            />
          </div>

          {/* Attachments */}
          <div>
            <label className="text-[#333333] font-medium mb-2 block">
              Attachments (Optional)
            </label>
            <div className="flex flex-wrap gap-2 mb-3">
              {attachments.map((attachment, index) => (
                <div
                  key={index}
                  className="w-20 h-20 bg-[#f2f2f2] rounded-lg overflow-hidden relative shadow-sm"
                >
                  <img
                    src={attachment}
                    alt={`Attachment ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  <button
                    className="absolute top-1 right-1 bg-white rounded-full p-1"
                    onClick={() =>
                      setAttachments(attachments.filter((_, i) => i !== index))
                    }
                  >
                    <X size={12} className="text-red-500" />
                  </button>
                </div>
              ))}
              <button
                className="w-20 h-20 border-2 border-dashed border-[#d6d9dd] rounded-lg flex flex-col items-center justify-center shadow-sm"
                onClick={handleAddAttachment}
              >
                <Paperclip size={24} className="text-[#009639] mb-1" />
                <span className="text-xs text-[#797879]">Add</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] shadow-lg">
        <button
          className={`w-full py-4 text-lg font-semibold flex items-center justify-center rounded-full ${
            !rating || !feedback
              ? "bg-[#f2f2f2] text-[#797879]"
              : "bg-gradient-to-r from-[#009639] to-[#007A2F] text-white shadow-md"
          }`}
          onClick={handleSubmit}
          disabled={!rating || !feedback}
        >
          <Send size={20} className="mr-2" />
          Submit Feedback
        </button>
      </div>
    </div>
  );
}
