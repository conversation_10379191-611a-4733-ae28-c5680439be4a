"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Calendar,
  Car,
  ChevronRight,
  Plus,
  AlertCircle,
  CheckCircle,
  Clock as ClockIcon,
  Wrench,
} from "lucide-react";
import { formatDateForInput } from "@/lib/utils";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import { useVehicleMaintenance } from "@/hooks/use-vehicle-maintenance";
import {
  VehicleServiceStatus,
  VehicleMaintenanceRead,
} from "@/types/maintanance";

export default function MaintenanceScheduleScreen({
  vehicles,
}: {
  vehicles: VehicleReadWithModelAndParty[];
}) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("upcoming");
  const [selectedVehicle, setSelectedVehicle] = useState<number | null>(null);
  const maintenanceItems = useVehicleMaintenance(vehicles);

  const filteredMaintenanceItems = maintenanceItems.filter(
    (item: VehicleMaintenanceRead) => {
      if (
        activeTab === "upcoming" &&
        item.status !== VehicleServiceStatus.SCHEDULED &&
        item.status !== VehicleServiceStatus.PENDING
      ) {
        return false;
      }
      if (
        activeTab === "completed" &&
        item.status !== VehicleServiceStatus.COMPLETED
      ) {
        return false;
      }
      if (selectedVehicle && item.vehicle_id !== +selectedVehicle) {
        return false;
      }
      return true;
    }
  );

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "high":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#FFD700] text-[#333333] font-medium">
            High Priority
          </span>
        );
      case "medium":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#333333] font-medium">
            Medium Priority
          </span>
        );
      case "low":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#007A2F] font-medium">
            Low Priority
          </span>
        );
      default:
        return null;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "upcoming":
        return <ClockIcon size={18} className="text-[#009639]" />;
      case "completed":
        return <CheckCircle size={18} className="text-[#009639]" />;
      default:
        return <AlertCircle size={18} className="text-[#FFD700]" />;
    }
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Maintenance Schedule</h1>
        </div>
      </div>

      {/* Maintenance Fund */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-[#333333] font-medium">Maintenance Fund</h3>
            <button
              className="text-[#009639] text-sm font-medium flex items-center"
              onClick={() => router.push("/maintenance-fund")}
            >
              View Details <ChevronRight size={16} className="ml-1" />
            </button>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-[#797879] text-xs">Current Balance</p>
              <p className="text-xl font-bold text-[#333333]">R 0</p>
            </div>
            <button
              className="bg-[#009639] text-white py-2 px-4 rounded-full text-sm flex items-center justify-center shadow-sm"
              onClick={() => router.push("/add-funds")}
            >
              <Plus size={16} className="mr-1" /> Add Funds
            </button>
          </div>
        </div>
      </div>

      {/* Vehicle Filter */}
      <div className="flex space-x-2 items-center p-4">
        <button
          className={`px-3 py-1 rounded-full text-sm whitespace-nowrap shadow-sm ${
            selectedVehicle === null
              ? "bg-[#009639] text-white"
              : "bg-[#f2f2f2] text-[#333333]"
          }`}
          onClick={() => setSelectedVehicle(null)}
        >
          All Vehicles
        </button>
        {vehicles.map((vehicle) => (
          <button
            key={vehicle.id}
            className={`flex items-center px-3 py-1 rounded-full text-sm whitespace-nowrap shadow-sm ${
              selectedVehicle === vehicle.id
                ? "bg-[#009639] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setSelectedVehicle(vehicle.id)}
          >
            <Car size={14} className="mr-1" />
            {vehicle.model?.model} {vehicle.model?.make?.name}
          </button>
        ))}

        {selectedVehicle && (
          <>
            <div className="flex-1" />

            <button
              className="bg-[#009639] text-white py-2 px-4 rounded-full text-sm flex items-center justify-center shadow-sm"
              onClick={() =>
                router.push(`/schedule-maintenance/${selectedVehicle}`)
              }
            >
              Schedule
            </button>
          </>
        )}
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-[#f2f2f2] mx-4">
        <div className="flex">
          <button
            className={`flex-1 py-3 text-sm font-medium ${
              activeTab === "upcoming"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("upcoming")}
          >
            Upcoming
          </button>
          <button
            className={`flex-1 py-3 text-sm font-medium ${
              activeTab === "completed"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("completed")}
          >
            History
          </button>
        </div>
      </div>

      {/* Maintenance List */}
      <div className="p-4">
        {filteredMaintenanceItems.length > 0 ? (
          <div className="space-y-3">
            {filteredMaintenanceItems.map((item) => {
              const vehicle = vehicles.find((v) => v.id === item.vehicle_id);
              return (
                <div
                  key={item.id}
                  className="bg-white rounded-xl shadow-md p-4 border border-gray-100"
                  onClick={() => router.push(`/maintenance-details/${item.id}`)}
                >
                  <div className="flex items-start">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                      <Wrench size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="text-[#333333] font-medium">
                          {item.is_scheduled ? "shedule" : "regular"}
                        </h3>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center">
                            {getStatusIcon(item.status)}
                            <span className="text-xs text-[#797879] ml-1">
                              {item.status === VehicleServiceStatus.SCHEDULED
                                ? "Upcoming"
                                : "Completed"}
                            </span>
                          </div>
                          <a
                            href={`/edit-maintenance/${item.id}`}
                            className="text-[#009639] hover:text-[#00732e] transition-colors"
                          >
                            <button className="p-1 rounded-md hover:bg-[#e6ffe6] transition-colors">
                              <svg
                                className="w-5 h-5"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                />
                              </svg>
                            </button>
                          </a>
                          <ChevronRight size={18} className="text-[#797879]" />
                        </div>
                      </div>
                      <p className="text-xs text-[#797879] mt-1">
                        {vehicle?.model?.model} {vehicle?.model?.make?.name}
                      </p>
                      <p className="text-sm text-[#333333] mt-1">
                        {item.description}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center">
                          <Calendar size={14} className="text-[#797879] mr-1" />
                          <span className="text-xs text-[#797879]">
                            {formatDateForInput(item.due_date)}
                          </span>
                        </div>
                        <div>{getPriorityBadge("high")}</div>
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <div className="text-sm font-medium text-[#333333]">
                          R{item.expected_cost.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <Wrench
              size={40}
              className="text-[#009639] opacity-30 mx-auto mb-3"
            />
            <p className="text-[#333333] font-medium">
              No maintenance
              {activeTab === "upcoming" ? "scheduled" : "history"}
            </p>
            <p className="text-[#797879] text-sm mt-1">
              {activeTab === "upcoming"
                ? "Schedule maintenance to keep your vehicles in top condition"
                : "Completed maintenance will appear here"}
            </p>
            {activeTab === "upcoming" && selectedVehicle && (
              <button
                className="bg-[#009639] text-white w-full py-3 rounded-full text-base font-medium shadow-sm mt-4"
                onClick={() =>
                  router.push(`/schedule-maintenance/${selectedVehicle}`)
                }
              >
                Schedule Maintenance
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
