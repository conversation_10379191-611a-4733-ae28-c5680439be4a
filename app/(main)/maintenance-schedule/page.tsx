"use server";
import MaintenanceScheduleScreen from "./maintenance-schedule";
import MissingParty from "@/components/missing-party";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import { getCompanyOwnershipByParty } from "@/actions/company-ownership";
import { getVehiclesByParties } from "@/actions/vehicles";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";

export default async function MaintenanceSchedule() {
  let vehicles;
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;
  const ownerships = await getCompanyOwnershipByParty(+dbId);
  const partyIds: number[] = ownerships
    .map((ownership) => ownership.company?.party_id)
    .filter((id): id is number => typeof id === "number");
  if (partyIds.length === 0) {
    vehicles = [] as VehicleReadWithModelAndParty[];
  } else {
    vehicles = await getVehiclesByParties(partyIds);
  }
  return <MaintenanceScheduleScreen vehicles={vehicles} />;
}
