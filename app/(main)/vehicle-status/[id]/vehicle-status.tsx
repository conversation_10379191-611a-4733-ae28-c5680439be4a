"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getUrl } from "aws-amplify/storage";
import type { VehicleMediaRead } from "@/types/vehicles";
import type { VehiclePossessionWithContactRead } from "@/types/vehicle-possessions";
import {
  ArrowLeft,
  Phone,
  User,
  Calendar,
  ChevronRight,
  Car,
  Wrench,
  Clock,
  Mail,
  ChevronLeft,
  ChevronRight as ChevronRightPagination,
} from "lucide-react";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import VehicleImageCarousel from "./vehicleImage-carousel";
import {
  getStatusColor,
  getStatusLabel,
  formatDateForInput,
  calculateTimeRemaining,
} from "@/lib/utils";

export default function VehicleStatusScreen({
  vehicle,
  possessions,
}: {
  vehicle: VehicleReadWithModelAndParty;
  possessions: VehiclePossessionWithContactRead[];
}) {
  const router = useRouter();
  const [timeRemaining, setTimeRemaining] = useState({ hours: 5, minutes: 45 });
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [currentPossession, setCurrentPossession] =
    useState<VehiclePossessionWithContactRead | null>(null);
  const [activeTab, setActiveTab] = useState<"maintenance" | "bookings">(
    "maintenance"
  );
  const [maintenancePage, setMaintenancePage] = useState(1);
  const [bookingsPage, setBookingsPage] = useState(1);
  const itemsPerPage = 10;

  useEffect(() => {
    async function loadImages() {
      if (!vehicle?.media?.length) return;

      const urls: string[] = await Promise.all(
        vehicle.media.map(async (item: VehicleMediaRead) => {
          const result = await getUrl({ path: item.media_path });
          return result.url.toString();
        })
      );

      setImageUrls(urls);
    }

    loadImages();
  }, [vehicle]);

  useEffect(() => {
    if (!currentPossession || !currentPossession.handover_expected_datetime)
      return;

    const updateTime = () => {
      const remaining = calculateTimeRemaining(
        currentPossession.handover_expected_datetime
      );
      setTimeRemaining(remaining);
    };

    updateTime();
    const intervalId = setInterval(updateTime, 60 * 1000);

    return () => clearInterval(intervalId);
  }, [currentPossession?.handover_expected_datetime]);

  useEffect(() => {
    async function fetchPossession() {
      if (!vehicle?.id) return;
      const latest = possessions
        .filter((p) => p.status === "pending")
        .sort(
          (a, b) =>
            new Date(b.handover_expected_datetime).getTime() -
            new Date(a.handover_expected_datetime).getTime()
        )[0];
      setCurrentPossession(latest || null);
    }

    fetchPossession();
  }, [vehicle?.id]);

  // Pagination logic for Maintenance
  const maintenanceItems = vehicle?.maintenance_items || [];
  const totalMaintenancePages = Math.ceil(
    maintenanceItems.length / itemsPerPage
  );
  const paginatedMaintenanceItems = maintenanceItems.slice(
    (maintenancePage - 1) * itemsPerPage,
    maintenancePage * itemsPerPage
  );

  // Pagination logic for Bookings
  const bookings = vehicle?.bookings || [];
  const totalBookingsPages = Math.ceil(bookings.length / itemsPerPage);
  const paginatedBookings = bookings.slice(
    (bookingsPage - 1) * itemsPerPage,
    bookingsPage * itemsPerPage
  );

  return (
    <div className="min-h-screen bg-white">
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">
          {vehicle?.model?.model} Status
        </h1>
      </div>

      {/* Vehicle Image */}
      <div className="p-4">
        <VehicleImageCarousel images={imageUrls} />
      </div>

      {/* Current User */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <User size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                {currentPossession ? "Current User" : ""}
              </h3>
            </div>
            <span className="text-xs bg-[#FFD700] text-[#333333] px-2 py-1 rounded-full shadow-sm">
              {currentPossession ? "In Use" : "Not in use"}
            </span>
          </div>
          {currentPossession && (
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#333333] font-medium">
                  {currentPossession?.to_party?.individual
                    ? `${currentPossession.to_party.individual.first_name} ${
                        currentPossession.to_party.individual?.middle_name ?? ""
                      } ${currentPossession.to_party.individual.last_name}`
                    : "Unknown user"}
                </p>
                <div className="flex items-center mt-1">
                  <Calendar size={14} className="text-[#797879] mr-1" />
                  <p className="text-xs text-[#797879]">
                    Return:{" "}
                    {new Date(
                      currentPossession?.handover_expected_datetime || ""
                    ).toLocaleString()}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  className="bg-[#009639] text-white p-2 rounded-full shadow-sm"
                  onClick={() => {
                    const phoneNumber =
                      currentPossession?.to_party?.contact_points?.find(
                        (cp) => cp.contact_point_type?.name === "phone"
                      )?.value || "0000";
                    window.open(`tel:${phoneNumber}`);
                  }}
                >
                  <Phone size={20} />
                </button>
                <button
                  className="bg-[#009639] text-white p-2 rounded-full shadow-sm"
                  onClick={() => {
                    const emailAddress =
                      currentPossession?.to_party?.contact_points?.find(
                        (cp) => cp.contact_point_type?.name === "email"
                      )?.value || "<EMAIL>";
                    window.open(`mailto:${emailAddress}`);
                  }}
                >
                  <Mail size={20} />
                </button>
              </div>
            </div>
          )}
          {currentPossession && (
            <div className="mt-4">
              <div className="flex justify-between text-xs text-[#797879] mb-1">
                <span>Time Remaining</span>
                <span>
                  {timeRemaining.hours}h {timeRemaining.minutes}m
                </span>
              </div>
              <div className="h-2 bg-[#f2f2f2] rounded-full overflow-hidden">
                <div
                  className="h-full bg-[#009639] rounded-full"
                  style={{
                    width: `${
                      ((timeRemaining.hours * 60 + timeRemaining.minutes) /
                        (6 * 60)) *
                      100
                    }%`,
                  }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4">
        <div className="flex border-b border-gray-200 mb-4">
          <button
            className={`flex-1 py-2 text-center text-sm font-medium ${
              activeTab === "maintenance"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => {
              setActiveTab("maintenance");
              setMaintenancePage(1); // Reset to first page when switching tabs
            }}
          >
            Maintenance
          </button>
          <button
            className={`flex-1 py-2 text-center text-sm font-medium ${
              activeTab === "bookings"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => {
              setActiveTab("bookings");
              setBookingsPage(1); // Reset to first page when switching tabs
            }}
          >
            Bookings
          </button>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          {activeTab === "maintenance" && (
            <>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Car size={20} className="text-[#009639] mr-2" />
                  <h3 className="text-[#333333] font-medium">Maintenance</h3>
                </div>
              </div>
              <div className="space-y-3">
                {paginatedMaintenanceItems.length > 0 ? (
                  paginatedMaintenanceItems.map((item) => (
                    <div
                      key={item.id}
                      className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
                    >
                      <div className="flex items-start">
                        <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                          <Wrench size={18} className="text-[#009639]" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3>{vehicle?.model?.model}</h3>
                              <h5>{item.name}</h5>
                              <p className="text-xs text-[#797879]">
                                {item.description}
                              </p>
                            </div>
                            <div className="flex items-center gap-2 ml-auto">
                              <span
                                className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(
                                  item.status
                                )}`}
                              >
                                {getStatusLabel(item.status)}
                              </span>
                              <button
                                className="text-[#009639] text-sm flex items-center"
                                onClick={() =>
                                  router.push(`/maintenance-details/${item.id}`)
                                }
                              >
                                <ChevronRight
                                  size={20}
                                  className="text-[#797879]"
                                />
                              </button>
                            </div>
                          </div>
                          <div className="flex items-center mt-2">
                            <Clock size={14} className="text-[#797879] mr-1" />
                            <span className="text-xs text-[#797879]">
                              {formatDateForInput(item.due_date)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-[#797879] text-sm">
                    No maintenance items available.
                  </p>
                )}
              </div>
              {/* Maintenance Pagination */}
              {totalMaintenancePages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setMaintenancePage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={maintenancePage === 1}
                  >
                    <ChevronLeft size={20} />
                  </button>
                  <span className="text-sm text-[#333333]">
                    Page {maintenancePage} of {totalMaintenancePages}
                  </span>
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setMaintenancePage((prev) =>
                        Math.min(prev + 1, totalMaintenancePages)
                      )
                    }
                    disabled={maintenancePage === totalMaintenancePages}
                  >
                    <ChevronRightPagination size={20} />
                  </button>
                </div>
              )}
            </>
          )}

          {activeTab === "bookings" && (
            <>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Car size={20} className="text-[#009639] mr-2" />
                  <h3 className="text-[#333333] font-medium">Bookings</h3>
                </div>
              </div>
              <div className="space-y-3">
                {paginatedBookings.length > 0 ? (
                  paginatedBookings.map((item) => (
                    <div
                      key={item.id}
                      className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
                    >
                      <div className="flex items-start">
                        <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                          <Wrench size={18} className="text-[#009639]" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3>{vehicle?.model?.model}</h3>
                              <h5>{item?.reference}</h5>
                              <p className="text-xs text-[#797879]">
                                {item?.notes}
                              </p>
                            </div>
                            <span
                              className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(
                                item.status
                              )}`}
                            >
                              {getStatusLabel(item.status)}
                            </span>
                          </div>
                          <div className="flex items-center mt-2">
                            <Clock size={14} className="text-[#797879] mr-1" />
                            <span className="text-xs text-[#797879]">
                              {formatDateForInput(item?.start_datetime)} -{" "}
                              {formatDateForInput(item?.end_datetime)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-[#797879] text-sm">
                    No bookings available.
                  </p>
                )}
              </div>
              {/* Bookings Pagination */}
              {totalBookingsPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setBookingsPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={bookingsPage === 1}
                  >
                    <ChevronLeft size={20} />
                  </button>
                  <span className="text-sm text-[#333333]">
                    Page {bookingsPage} of {totalBookingsPages}
                  </span>
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setBookingsPage((prev) =>
                        Math.min(prev + 1, totalBookingsPages)
                      )
                    }
                    disabled={bookingsPage === totalBookingsPages}
                  >
                    <ChevronRightPagination size={20} />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] space-y-3">
        {/* Book Vehicle Button - only show if vehicle is available */}
        {!currentPossession && vehicle?.bookings?.every(booking => 
          booking.status === "Cancelled" || booking.status === "Completed" || 
          new Date(booking.end_datetime) < new Date()
        ) && (
          <button
            className="w-full py-3 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm mb-2"
            onClick={() => router.push(`/booking-calendar/${vehicle.id}`)}
          >
            Book This Vehicle
          </button>
        )}
        
        {/* Handover Button */}
        <button
          className="w-full py-4 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm"
          onClick={() => router.push(`/vehicle-handover/${vehicle.id}`)}
        >
          Initiate Vehicle Handover
        </button>
      </div>
    </div>
  );
}
