"use server";
import { getVehicle } from "@/actions/vehicles";
import { getVehiclePossessionsByVehicle } from "@/actions/vehicles-possessions";
import VehicleStatusScreen from "./vehicle-status";
export default async function VehicleStatus({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const vehicle = await getVehicle(+id);
  if (!vehicle) {
    return <p>Vehicle not found.</p>;
  }
  const possessions = await getVehiclePossessionsByVehicle(+id);

  return <VehicleStatusScreen vehicle={vehicle} possessions={possessions} />;
}