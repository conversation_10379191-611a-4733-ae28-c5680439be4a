"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Search,
  Car,
  Wallet,
  Users,
  Settings,
  ChevronRight,
  MessageSquare,
} from "lucide-react";

export default function HelpCenterScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");

  // Mock data - in a real app, this would come from an API
  const helpCategories = [
    {
      id: 1,
      title: "Getting Started",
      icon: <Car size={24} className="text-[#009639]" />,
      articles: [
        "How to create a vehicle group in South Africa",
        "Joining an existing group in your area",
        "Setting up your profile with South African details",
      ],
    },
    {
      id: 2,
      title: "Payments & Billing",
      icon: <Wallet size={24} className="text-[#009639]" />,
      articles: [
        "Understanding payment schedules in Rand (ZAR)",
        "Adding South African payment methods",
        "Disputing a charge with local banks",
      ],
    },
    {
      id: 3,
      title: "Group Management",
      icon: <Users size={24} className="text-[#009639]" />,
      articles: [
        "Adding members to a group",
        "Setting group rules",
        "Resolving disputes",
      ],
    },
    {
      id: 4,
      title: "Account Settings",
      icon: <Settings size={24} className="text-[#009639]" />,
      articles: [
        "Changing notification preferences",
        "Updating personal information",
        "Managing privacy settings",
      ],
    },
  ];

  const popularArticles = [
    {
      id: 1,
      title: "How to book a vehicle in South Africa",
      category: "Getting Started",
      views: 1245,
    },
    {
      id: 2,
      title: "Understanding ownership percentages",
      category: "Group Management",
      views: 987,
    },
    {
      id: 3,
      title: "Vehicle maintenance in South Africa",
      category: "Vehicle Management",
      views: 856,
    },
  ];

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Help Center</h1>
      </div>

      {/* Search */}
      <div className="p-4 bg-white shadow-sm">
        <div className="bg-[#f2f2f2] rounded-full px-4 py-3 flex items-center shadow-sm">
          <Search size={18} className="text-[#009639] mr-2" />
          <input
            type="text"
            placeholder="Search for help articles"
            className="flex-1 bg-transparent focus:outline-none text-[#333333] text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Help Categories */}
      <div className="p-4">
        <h3 className="text-[#333333] font-medium mb-3">Help Categories</h3>
        <div className="space-y-3">
          {helpCategories.map((category) => (
            <div
              key={category.id}
              className="bg-white rounded-xl shadow-md p-4 border border-gray-100"
            >
              <div className="flex items-start">
                <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                  {category.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-[#333333] font-medium">
                    {category.title}
                  </h3>
                  <p className="text-xs text-[#797879] mt-1">
                    {category.articles.length} articles
                  </p>
                </div>
                <button
                  className="text-[#009639]"
                  onClick={() => router.push(`/help/category/${category.id}`)}
                >
                  <ChevronRight size={20} />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Popular Articles */}
      <div className="p-4">
        <h3 className="text-[#333333] font-medium mb-3">Popular Articles</h3>
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          {popularArticles.map((article, index) => (
            <div
              key={article.id}
              className={`p-4 ${
                index < popularArticles.length - 1
                  ? "border-b border-[#f2f2f2]"
                  : ""
              }`}
              onClick={() => router.push(`/help/article/${article.id}`)}
            >
              <h3 className="text-[#333333] font-medium">{article.title}</h3>
              <div className="flex items-center text-xs text-[#797879] mt-1">
                <span>{article.category}</span>
                <span className="mx-2">•</span>
                <span>{article.views} views</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Contact Support */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-2">
            Need more assistance?
          </h3>
          <p className="text-[#797879] text-sm mb-4">
            Our South African support team is ready to help you with any
            questions or issues you may have about car sharing in your area.
          </p>
          <button
            className="w-full py-3 flex items-center justify-center bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full font-semibold shadow-md"
            onClick={() => router.push("/contact-support")}
          >
            <MessageSquare size={18} className="mr-2" />
            Contact Support
          </button>
        </div>
      </div>
    </div>
  );
}
