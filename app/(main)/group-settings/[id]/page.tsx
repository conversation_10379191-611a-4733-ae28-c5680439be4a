"use client";

import { useState, use, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Lock,
  Globe,
  AlertTriangle,
  ChevronRight,
  ToggleLeft,
  ToggleRight,
} from "lucide-react";

export default function GroupSettingsScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const groupId = use(params).id;

  const [settings, setSettings] = useState({
    privacy: "private",
    notifications: {
      bookings: true,
      payments: true,
      maintenance: true,
      memberActivity: false,
    },
    decisionMaking: {
      votingThreshold: 75, // percentage
    },
    disputeResolution: "majority",
  });

  const handleToggle = (category: string, setting: string) => {
    setSettings({
      ...settings,
      [category]: {
        ...(settings[category as keyof typeof settings] as Record<
          string,
          boolean
        >),
        [setting]: !(
          settings[category as keyof typeof settings] as Record<string, boolean>
        )[setting],
      },
    });
  };

  const handlePrivacyChange = (privacy: string) => {
    setSettings({
      ...settings,
      privacy,
    });
  };

  const handleVotingThresholdChange = (threshold: number) => {
    setSettings({
      ...settings,
      decisionMaking: {
        ...(settings.decisionMaking as Record<string, number>),
        votingThreshold: threshold,
      },
    });
  };

  const handleDisputeResolutionChange = (method: string) => {
    setSettings({
      ...settings,
      disputeResolution: method,
    });
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Group Settings</h1>
      </div>

      {/* Settings Sections */}
      <div className="p-4 space-y-4">
        {/* Privacy Settings */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2]">
            <h3 className="text-[#333333] font-semibold mb-1">
              Privacy Settings
            </h3>
            <p className="text-sm text-[#797879]">
              Control who can see and join your group
            </p>
          </div>

          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Lock size={20} className="text-[#009639] mr-2" />
                <div>
                  <p className="text-[#333333] font-medium">Private Group</p>
                  <p className="text-xs text-[#797879]">
                    Only invited members can join
                  </p>
                </div>
              </div>
              <button
                className={`w-6 h-6 rounded-full ${
                  settings.privacy === "private"
                    ? "bg-[#009639]"
                    : "bg-[#f2f2f2]"
                } flex items-center justify-center shadow-sm`}
                onClick={() => handlePrivacyChange("private")}
              >
                {settings.privacy === "private" && (
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                )}
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Globe size={20} className="text-[#009639] mr-2" />
                <div>
                  <p className="text-[#333333] font-medium">Public Group</p>
                  <p className="text-xs text-[#797879]">
                    Anyone can request to join
                  </p>
                </div>
              </div>
              <button
                className={`w-6 h-6 rounded-full ${
                  settings.privacy === "public"
                    ? "bg-[#009639]"
                    : "bg-[#f2f2f2]"
                } flex items-center justify-center shadow-sm`}
                onClick={() => handlePrivacyChange("public")}
              >
                {settings.privacy === "public" && (
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Notification Preferences */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2]">
            <h3 className="text-[#333333] font-semibold mb-1">
              Notification Preferences
            </h3>
            <p className="text-sm text-[#797879]">
              Control what notifications you receive
            </p>
          </div>

          <div className="divide-y divide-[#f2f2f2]">
            <div className="p-4 flex items-center justify-between">
              <p className="text-[#333333]">Booking Notifications</p>
              <button onClick={() => handleToggle("notifications", "bookings")}>
                {settings.notifications.bookings ? (
                  <ToggleRight size={24} className="text-[#009639]" />
                ) : (
                  <ToggleLeft size={24} className="text-[#797879]" />
                )}
              </button>
            </div>

            <div className="p-4 flex items-center justify-between">
              <p className="text-[#333333]">Payment Notifications</p>
              <button onClick={() => handleToggle("notifications", "payments")}>
                {settings.notifications.payments ? (
                  <ToggleRight size={24} className="text-[#009639]" />
                ) : (
                  <ToggleLeft size={24} className="text-[#797879]" />
                )}
              </button>
            </div>

            <div className="p-4 flex items-center justify-between">
              <p className="text-[#333333]">Maintenance Alerts</p>
              <button
                onClick={() => handleToggle("notifications", "maintenance")}
              >
                {settings.notifications.maintenance ? (
                  <ToggleRight size={24} className="text-[#009639]" />
                ) : (
                  <ToggleLeft size={24} className="text-[#797879]" />
                )}
              </button>
            </div>

            <div className="p-4 flex items-center justify-between">
              <p className="text-[#333333]">Member Activity</p>
              <button
                onClick={() => handleToggle("notifications", "memberActivity")}
              >
                {settings.notifications.memberActivity ? (
                  <ToggleRight size={24} className="text-[#009639]" />
                ) : (
                  <ToggleLeft size={24} className="text-[#797879]" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Decision-making Rules */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2]">
            <h3 className="text-[#333333] font-semibold mb-1">
              Decision-making Rules
            </h3>
            <p className="text-sm text-[#797879]">
              Set voting thresholds for group decisions
            </p>
          </div>

          <div className="p-4">
            <p className="text-[#333333] mb-2">Voting Threshold</p>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-[#797879]">
                Simple Majority (50%)
              </span>
              <span className="text-sm text-[#797879]">Unanimous (100%)</span>
            </div>
            <input
              type="range"
              min="50"
              max="100"
              step="5"
              value={settings.decisionMaking.votingThreshold}
              onChange={(e) =>
                handleVotingThresholdChange(Number.parseInt(e.target.value))
              }
              className="w-full h-2 bg-[#f2f2f2] rounded-full appearance-none [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4 [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-[#009639]"
            />
            <p className="text-center mt-2 text-sm font-medium text-[#009639]">
              {settings.decisionMaking.votingThreshold}% agreement required
            </p>
          </div>
        </div>

        {/* Dispute Resolution */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2]">
            <h3 className="text-[#333333] font-semibold mb-1">
              Dispute Resolution
            </h3>
            <p className="text-sm text-[#797879]">
              How conflicts will be resolved
            </p>
          </div>

          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#333333] font-medium">Majority Vote</p>
                <p className="text-xs text-[#797879]">
                  Simple majority decides
                </p>
              </div>
              <button
                className={`w-6 h-6 rounded-full ${
                  settings.disputeResolution === "majority"
                    ? "bg-[#009639]"
                    : "bg-[#f2f2f2]"
                } flex items-center justify-center shadow-sm`}
                onClick={() => handleDisputeResolutionChange("majority")}
              >
                {settings.disputeResolution === "majority" && (
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                )}
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#333333] font-medium">Admin Decision</p>
                <p className="text-xs text-[#797879]">
                  Group admin has final say
                </p>
              </div>
              <button
                className={`w-6 h-6 rounded-full ${
                  settings.disputeResolution === "admin"
                    ? "bg-[#009639]"
                    : "bg-[#f2f2f2]"
                } flex items-center justify-center shadow-sm`}
                onClick={() => handleDisputeResolutionChange("admin")}
              >
                {settings.disputeResolution === "admin" && (
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                )}
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#333333] font-medium">Mediation</p>
                <p className="text-xs text-[#797879]">Third-party mediation</p>
              </div>
              <button
                className={`w-6 h-6 rounded-full ${
                  settings.disputeResolution === "mediation"
                    ? "bg-[#009639]"
                    : "bg-[#f2f2f2]"
                } flex items-center justify-center shadow-sm`}
                onClick={() => handleDisputeResolutionChange("mediation")}
              >
                {settings.disputeResolution === "mediation" && (
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Group Dissolution */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
          <div className="p-4 border-b border-[#f2f2f2]">
            <h3 className="text-[#333333] font-semibold mb-1">
              Group Dissolution
            </h3>
            <p className="text-sm text-[#797879]">
              Options for ending the group
            </p>
          </div>

          <button
            className="p-4 w-full flex items-center justify-between text-red-600"
            onClick={() => router.push(`/group-dissolution/${groupId}`)}
          >
            <div className="flex items-center">
              <AlertTriangle size={20} className="mr-2" />
              <span>Dissolve Group</span>
            </div>
            <ChevronRight size={20} />
          </button>
        </div>
      </div>

      {/* Save Button */}
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] shadow-lg">
        <button
          className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-4 rounded-full text-xl font-semibold shadow-md"
          onClick={() => {
            console.log("Settings saved:", settings);
            router.back();
          }}
        >
          Save Changes
        </button>
      </div>
    </div>
  );
}
