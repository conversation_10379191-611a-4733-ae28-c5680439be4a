"use client";

import type React from "react";

import { useState, use } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  CreditCard,
  Building,
  Check,
  ChevronDown,
  ChevronUp,
  Plus,
  DollarSign,
  FileText,
} from "lucide-react";

export default function PaymentScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const groupId = use(params).id;
  const [amount, setAmount] = useState("150");
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    string | null
  >("card1");
  const [showAddCard, setShowAddCard] = useState(false);
  const [receiptEmail, setReceiptEmail] = useState("<EMAIL>");
  const [isProcessing, setIsProcessing] = useState(false);

  // Mock data - in a real app, this would come from an API
  const paymentMethods = [
    {
      id: "card1",
      type: "card",
      name: "Visa ending in 4242",
      icon: <CreditCard size={20} className="text-[#009639]" />,
      details: "Expires 12/25",
    },
    {
      id: "bank1",
      type: "bank",
      name: "FNB Bank Account",
      icon: <Building size={20} className="text-[#009639]" />,
      details: "****6789",
    },
  ];

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow numbers and decimal point
    const value = e.target.value.replace(/[^0-9.]/g, "");
    setAmount(value);
  };

  const handlePaymentMethodSelect = (id: string) => {
    setSelectedPaymentMethod(id);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsProcessing(true);

    // Simulate payment processing
    setTimeout(() => {
      console.log("Payment processed:", {
        amount,
        paymentMethod: selectedPaymentMethod,
        receiptEmail,
      });
      router.push(`/payment-confirmation/${groupId}`);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Make Payment</h1>
      </div>

      <form onSubmit={handleSubmit} className="p-4 space-y-4">
        {/* Amount Due */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <label
            htmlFor="amount"
            className="block text-[#333333] font-medium mb-2"
          >
            Payment Amount
          </label>
          <div className="relative">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
              <span className="text-[#797879] font-medium">R</span>
            </div>
            <input
              type="text"
              id="amount"
              value={amount}
              onChange={handleAmountChange}
              className="w-full pl-10 pr-4 py-3 text-xl font-bold rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
            />
          </div>
          <p className="text-xs text-[#797879] mt-2">
            Your monthly contribution is R150. Additional payments will go to
            the group fund.
          </p>
        </div>

        {/* Payment Method */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">Payment Method</h3>

          <div className="space-y-3">
            {paymentMethods.map((method) => (
              <div
                key={method.id}
                className={`p-3 rounded-lg border ${
                  selectedPaymentMethod === method.id
                    ? "border-[#009639] bg-[#e6ffe6]"
                    : "border-[#d6d9dd]"
                }`}
                onClick={() => handlePaymentMethodSelect(method.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {method.icon}
                    <div className="ml-3">
                      <p className="text-[#333333] font-medium">
                        {method.name}
                      </p>
                      <p className="text-xs text-[#797879]">{method.details}</p>
                    </div>
                  </div>
                  {selectedPaymentMethod === method.id && (
                    <Check size={20} className="text-[#009639]" />
                  )}
                </div>
              </div>
            ))}

            <button
              type="button"
              className="w-full flex items-center justify-between p-3 rounded-lg border border-dashed border-[#d6d9dd]"
              onClick={() => setShowAddCard(!showAddCard)}
            >
              <div className="flex items-center">
                <Plus size={20} className="text-[#009639]" />
                <span className="ml-3 text-[#009639] font-medium">
                  Add Payment Method
                </span>
              </div>
              {showAddCard ? (
                <ChevronUp size={20} />
              ) : (
                <ChevronDown size={20} />
              )}
            </button>

            {showAddCard && (
              <div className="p-3 rounded-lg border border-[#d6d9dd] space-y-3">
                <div>
                  <label
                    htmlFor="cardNumber"
                    className="block text-sm text-[#797879] mb-1"
                  >
                    Card Number
                  </label>
                  <input
                    type="text"
                    id="cardNumber"
                    placeholder="1234 5678 9012 3456"
                    className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                  />
                </div>

                <div className="flex space-x-3">
                  <div className="flex-1">
                    <label
                      htmlFor="expiry"
                      className="block text-sm text-[#797879] mb-1"
                    >
                      Expiry Date
                    </label>
                    <input
                      type="text"
                      id="expiry"
                      placeholder="MM/YY"
                      className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                    />
                  </div>
                  <div className="flex-1">
                    <label
                      htmlFor="cvc"
                      className="block text-sm text-[#797879] mb-1"
                    >
                      CVC
                    </label>
                    <input
                      type="text"
                      id="cvc"
                      placeholder="123"
                      className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                    />
                  </div>
                </div>

                <button
                  type="button"
                  className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-2 rounded-lg text-sm font-medium shadow-sm"
                >
                  Add Card
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Receipt */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-[#333333] font-medium">Receipt</h3>
            <div className="flex items-center">
              <FileText size={16} className="text-[#009639] mr-1" />
              <span className="text-sm text-[#009639]">Generate Receipt</span>
            </div>
          </div>

          <div>
            <label
              htmlFor="receiptEmail"
              className="block text-sm text-[#797879] mb-1"
            >
              Email Receipt To
            </label>
            <input
              type="email"
              id="receiptEmail"
              value={receiptEmail}
              onChange={(e) => setReceiptEmail(e.target.value)}
              className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
            />
          </div>
        </div>

        {/* Payment Summary */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">Payment Summary</h3>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-[#797879]">Payment Amount</span>
              <span className="text-[#333333]">R{amount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879]">Processing Fee</span>
              <span className="text-[#333333]">R0.00</span>
            </div>
            <div className="border-t border-[#f2f2f2] my-2 pt-2 flex justify-between">
              <span className="text-[#333333] font-medium">Total</span>
              <span className="text-[#333333] font-bold">R{amount}</span>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className={`w-full py-4 rounded-full text-xl font-semibold shadow-md ${
            isProcessing
              ? "bg-[#f2f2f2] text-[#797879]"
              : "bg-gradient-to-r from-[#009639] to-[#007A2F] text-white"
          }`}
          disabled={isProcessing || !selectedPaymentMethod}
        >
          {isProcessing ? "Processing..." : "Confirm Payment"}
        </button>
      </form>
    </div>
  );
}
