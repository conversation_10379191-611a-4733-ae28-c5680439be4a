"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  Search,
  Filter,
  ChevronRight,
  Users,
  MessageCircle,
  HelpCircle,
  Car,
  Plus,
} from "lucide-react";
import { getPublicGroups } from "../../../drizzle-actions/community";

interface Group {
  id: number;
  name: string;
  description: string;
  members: number;
  vehicles: number;
  location: string;
  image: string;
}

export default function CommunityScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("groups");
  const [publicGroups, setPublicGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchGroups = async () => {
      try {
        setLoading(true);
        const groups = await getPublicGroups();
        setPublicGroups(groups);
      } catch (error) {
        console.error("Failed to fetch groups:", error);
      } finally {
        setLoading(false);
      }
    };

    if (activeTab === "groups") {
      fetchGroups();
    }
  }, [activeTab]);

  const filteredGroups = publicGroups.filter(
    (group) =>
      group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      group.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      group.location.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const discussions = [
    {
      id: 1,
      title: "Best practices for vehicle handovers",
      author: "Jane Cooper",
      replies: 24,
      lastActive: "2 hours ago",
      authorImage: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 2,
      title: "Insurance options for shared vehicles",
      author: "Robert Fox",
      replies: 18,
      lastActive: "Yesterday",
      authorImage: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 3,
      title: "How to handle maintenance costs",
      author: "Leslie Alexander",
      replies: 32,
      lastActive: "3 days ago",
      authorImage: "/placeholder.svg?height=40&width=40",
    },
  ];

  const supportTopics = [
    {
      id: 1,
      title: "Getting Started",
      description: "Learn the basics of vehicle sharing",
      icon: <Car size={24} className="text-[#009639]" />,
    },
    {
      id: 2,
      title: "Payments & Billing",
      description: "Understand payment processes and billing cycles",
      icon: <HelpCircle size={24} className="text-[#009639]" />,
    },
    {
      id: 3,
      title: "Maintenance & Repairs",
      description: "Guidelines for vehicle maintenance",
      icon: <HelpCircle size={24} className="text-[#009639]" />,
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 border-b border-[#007A2F]">
        <h1 className="text-xl font-bold text-white">Community</h1>
      </div>

      {/* Search and Filter */}
      <div className="p-4">
        <div className="flex space-x-2">
          <div className="flex-1 bg-white rounded-full px-4 py-2 flex items-center shadow-md border border-gray-100">
            <Search size={18} className="text-[#797879] mr-2" />
            <input
              type="text"
              placeholder="Search community"
              className="flex-1 bg-transparent focus:outline-none text-[#333333] text-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {/* <button className="bg-white p-2 rounded-full shadow-sm">
            <Filter size={18} className="text-[#333333]" />
          </button> */}
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4">
        <div className="flex border-b border-[#f2f2f2] mb-4">
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "groups"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("groups")}
          >
            Groups
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "discussions"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("discussions")}
          >
            Discussions
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "support"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("support")}
          >
            Support
          </button>
        </div>
      </div>

      {/* Public Groups Tab */}
      {activeTab === "groups" && (
        <div className="px-4 pb-8">
          {loading ? (
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div
                  key={i}
                  className="bg-white rounded-xl shadow-md p-4 border border-gray-100"
                >
                  <div className="flex items-start animate-pulse">
                    <div className="w-14 h-14 bg-gray-200 rounded-full mr-3"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredGroups.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-[#797879] mb-4">No groups found</p>
              <button
                className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
                onClick={() => router.push("/create-group")}
              >
                Create First Group
              </button>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredGroups.map((group) => (
                <div
                  key={group.id}
                  className="bg-white rounded-xl shadow-md p-4 border border-gray-100"
                >
                  <div className="flex items-start">
                    <div className="w-14 h-14 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-100">
                      <Image
                        src={group.image}
                        alt={group.name}
                        width={56}
                        height={56}
                        className="object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-semibold">
                        {group.name}
                      </h3>
                      <p className="text-sm text-[#797879] mb-2">
                        {group.description}
                      </p>

                      <div className="flex items-center text-xs text-[#797879]">
                        <div className="flex items-center mr-3">
                          <Users size={12} className="mr-1" />
                          <span>{group.members} members</span>
                        </div>
                        <div className="flex items-center mr-3">
                          <Car size={12} className="mr-1" />
                          <span>{group.vehicles} vehicles</span>
                        </div>
                        <div>📍 {group.location}</div>
                      </div>
                    </div>
                    <button
                      className="text-[#009639] flex-shrink-0"
                      onClick={() => router.push(`/group-details/${group.id}`)}
                    >
                      <ChevronRight size={20} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Discussions Tab */}
      {activeTab === "discussions" && (
        <div className="px-4 pb-8">
          <div className="space-y-3">
            {discussions.map((discussion) => (
              <div
                key={discussion.id}
                className="bg-white rounded-xl shadow-md p-4 border border-gray-100"
              >
                <div className="flex items-start">
                  <div className="w-10 h-10 rounded-full overflow-hidden mr-3 flex-shrink-0 border border-gray-100">
                    <Image
                      src={discussion.authorImage}
                      alt={discussion.author}
                      width={40}
                      height={40}
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-[#333333] font-medium">
                      {discussion.title}
                    </h3>
                    <div className="flex items-center text-xs text-[#797879] mt-1">
                      <span className="mr-2">By {discussion.author}</span>
                      <span className="mr-2">•</span>
                      <MessageCircle size={12} className="mr-1" />
                      <span>{discussion.replies} replies</span>
                      <span className="mr-2 ml-2">•</span>
                      <span>{discussion.lastActive}</span>
                    </div>
                  </div>
                  <button
                    className="text-[#009639] flex-shrink-0"
                    onClick={() => router.push(`/discussion/${discussion.id}`)}
                  >
                    <ChevronRight size={20} />
                  </button>
                </div>
              </div>
            ))}

            <button
              className="w-full py-3 mt-4 bg-[#009639] text-white rounded-full font-semibold shadow-sm"
              onClick={() => router.push("/new-discussion")}
            >
              Start New Discussion
            </button>
          </div>
        </div>
      )}

      {/* Support Tab */}
      {activeTab === "support" && (
        <div className="px-4 pb-8">
          <div className="space-y-3">
            {supportTopics.map((topic) => (
              <div
                key={topic.id}
                className="bg-white rounded-xl shadow-md p-4 border border-gray-100"
              >
                <div className="flex items-start">
                  <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                    {topic.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-[#333333] font-medium">
                      {topic.title}
                    </h3>
                    <p className="text-sm text-[#797879]">
                      {topic.description}
                    </p>
                  </div>
                  <button
                    className="text-[#009639] flex-shrink-0"
                    onClick={() => router.push(`/support/${topic.id}`)}
                  >
                    <ChevronRight size={20} className="text-[#009639]" />
                  </button>
                </div>
              </div>
            ))}

            <div className="bg-white rounded-xl shadow-md p-4 mt-4 border border-gray-100">
              <h3 className="text-[#333333] font-medium mb-2">
                Need more help?
              </h3>
              <p className="text-sm text-[#797879] mb-3">
                Contact our support team for personalized assistance
              </p>
              <button
                className="w-full py-2 bg-[#009639] text-white rounded-full font-semibold shadow-sm"
                onClick={() => router.push("/contact-support")}
              >
                Contact Support
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Add Group FAB */}
      {activeTab === "groups" && (
        <div className="fixed bottom-20 right-4">
          <button
            className="w-14 h-14 bg-[#009639] rounded-full flex items-center justify-center shadow-lg"
            onClick={() => router.push("/create-group")}
          >
            <Plus size={24} className="text-white" />
          </button>
        </div>
      )}
    </div>
  );
}
