"use server";
import { getVehicleBookingsWithDetails, addBookingDrizzle } from "@/drizzle-actions/bookings";
import BookingCalendarScreen from "./booking-calender";
import { getVehicle } from "@/actions/vehicles";
import MissingParty from "@/components/missing-party";
import { getUserAttributes } from "@/lib/amplifyServerUtils";

export default async function BookingCalendar({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const attributes = await getUserAttributes();

  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  // Use drizzle for all booking operations
  const bookings = await getVehicleBookingsWithDetails(+id);
  const vehicle = await getVehicle(+id); // Keep using API for vehicle data until vehicles are migrated
  
  if (!vehicle) {
    return <p>Vehicle not found.</p>;
  }

  return (
    <BookingCalendarScreen
      bookingsx={bookings}
      vehicle={vehicle}
      action={addBookingDrizzle} // Now using drizzle action
      party_id={+dbId}
    />
  );
}
