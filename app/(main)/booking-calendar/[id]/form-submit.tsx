import { useFormStatus } from "react-dom";

export default function FormSubmit({
  selectedDates,
}: {
  selectedDates: number[];
}) {
  const status = useFormStatus();
  if (status.pending) {
    return (
      <div className=" bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2]">
        <button
          className={`ride-primary-btn w-full py-4 text-lg font-semibold ${"bg-[#f2f2f2] text-[#797879]"}`}
          type="submit"
          disabled={true}
        >
          {" "}
          <div className="border-t-4 border-b-4 border-white w-6 h-6 border-solid rounded-full animate-spin"></div>
        </button>
      </div>
    );
  }

  return (
    <div className=" bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2]">
      <button
        className={`ride-primary-btn w-full py-4 text-lg font-semibold ${
          selectedDates.length === 0 ? "bg-[#f2f2f2] text-[#797879]" : ""
        }`}
        type="submit"
        disabled={selectedDates.length === 0}
      >
        Confirm Booking
      </button>
    </div>
  );
}
