"use client";

import React, {
  useState,
  useActionState,
  startTransition,
  useEffect,
} from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
} from "lucide-react";
import { BookingRead } from "@/types/bookings";
import { VehicleRead } from "@/types/vehicles";
import { v4 as uuidv4 } from "uuid";
import { BookingStatus } from "@/types/bookings";
import FormSubmit from "./form-submit";

export default function BookingCalendarScreen({
  bookingsx,
  vehicle,
  party_id,
  action,
}: {
  bookingsx: BookingRead[];
  vehicle: VehicleRead;
  party_id: number;
  action: (prevState: any, formData: FormData) => any;
}) {
  const router = useRouter();

  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDates, setSelectedDates] = useState<number[]>([]);
  const [bookingDuration, setBookingDuration] = useState(1);
  const [showConflictWarning, setShowConflictWarning] = useState(false);
  const [state, formAction] = useActionState(action, {});
  const [selectionMode, setSelectionMode] = useState<"individual" | "range">("range");

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    return new Date(year, month, 1).getDay();
  };

  const daysInMonth = getDaysInMonth(currentMonth);
  const firstDayOfMonth = getFirstDayOfMonth(currentMonth);
  const monthName = currentMonth.toLocaleString("default", { month: "long" });
  const year = currentMonth.getFullYear();

  const bookings = React.useMemo(() => {
    const bookingsMap: Record<number, { status: string; memberId?: string }> =
      {};

    bookingsx.forEach((booking) => {
      const startDate = new Date(booking.start_datetime);
      const endDate = new Date(booking.end_datetime);

      const isSameMonth =
        startDate.getFullYear() === currentMonth.getFullYear() &&
        startDate.getMonth() === currentMonth.getMonth();

      if (isSameMonth) {
        let date = new Date(startDate);
        while (date <= endDate) {
          const day = date.getDate();
          bookingsMap[day] = {
            status: booking.status,
            memberId: booking.party_id.toString(),
          };
          date.setDate(date.getDate() + 1);
        }
      }
    });

    return bookingsMap;
  }, [bookingsx, currentMonth]);
  
  const getDayStatus = (day: number) => {
    return bookings[day as keyof typeof bookings] || { status: "available" };
  };

  // Enhanced day class with range selection visual feedback
  const getDayClass = (day: number) => {
    const status = getDayStatus(day).status;
    const isSelected = selectedDates.includes(day);
    const sortedDates = [...selectedDates].sort((a, b) => a - b);
    const isInRange = selectionMode === "range" && sortedDates.length >= 2 && 
      day >= sortedDates[0] && day <= sortedDates[sortedDates.length - 1];
    const isRangeStart = isSelected && day === sortedDates[0];
    const isRangeEnd = isSelected && day === sortedDates[sortedDates.length - 1];

    const baseClass =
      "h-10 w-10 rounded-full flex items-center justify-center text-sm cursor-pointer transition-colors ";

    if (status !== "available") {
      // Unavailable dates - using correct enum values
      switch (status) {
        case "CONFIRMED":
          return baseClass + "bg-[#e6ffe6] text-[#009639] font-medium cursor-not-allowed";
        case "PENDING":
          return baseClass + "bg-[#ffe6e6] text-[#d32f2f] font-medium cursor-not-allowed";
        case "COMPLETED":
          return baseClass + "bg-[#FFD700] text-[#333333] font-medium cursor-not-allowed";
        case "CANCELLED":
        default:
          return baseClass + "text-[#333333] bg-white hover:bg-[#e6ffe6]";
      }
    }

    // Available dates
    if (isRangeStart || isRangeEnd) {
      return baseClass + "bg-[#009639] text-white font-semibold";
    } else if (isSelected || isInRange) {
      return baseClass + "bg-[#009639] text-white";
    } else {
      return baseClass + "text-[#333333] bg-white hover:bg-[#e6ffe6]";
    }
  };

  const handlePrevMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)
    );
    setSelectedDates([]);
    setShowConflictWarning(false);
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1)
    );
    setSelectedDates([]);
    setShowConflictWarning(false);
  };

  const handleDayClick = (day: number) => {
    const status = getDayStatus(day).status;

    if (status !== "available") {
      setShowConflictWarning(true);
      return;
    }

    setShowConflictWarning(false);

    if (selectionMode === "individual") {
      // Individual date selection mode
      if (selectedDates.includes(day)) {
        setSelectedDates(selectedDates.filter((d) => d !== day));
      } else {
        setSelectedDates([...selectedDates, day].sort((a, b) => a - b));
      }
    } else {
      // Range selection mode
      if (selectedDates.length === 0) {
        // First click - select start date
        setSelectedDates([day]);
      } else if (selectedDates.length === 1) {
        // Second click - create range
        const startDay = selectedDates[0];
        if (day === startDay) {
          // Clicking same date - deselect
          setSelectedDates([]);
        } else {
          // Create range from start to end
          const start = Math.min(startDay, day);
          const end = Math.max(startDay, day);
          const range = [];
          
          // Check if any dates in range are unavailable
          let hasConflict = false;
          for (let d = start; d <= end; d++) {
            if (getDayStatus(d).status !== "available") {
              hasConflict = true;
              break;
            }
            range.push(d);
          }
          
          if (hasConflict) {
            setShowConflictWarning(true);
            setSelectedDates([startDay]); // Keep only the start date
          } else {
            setSelectedDates(range);
          }
        }
      } else {
        // Already have a range - start new selection
        setSelectedDates([day]);
      }
    }
  };

  const clearSelection = () => {
    setSelectedDates([]);
    setShowConflictWarning(false);
  };

  const handleBookingDurationChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setBookingDuration(Number(e.target.value));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    if (selectedDates.length === 0) return;

    const sortedDates = [...selectedDates].sort((a, b) => a - b);

    const startDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      sortedDates[0]
    );
    const endDate = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      sortedDates[sortedDates.length - 1]
    );

    endDate.setHours(23, 59, 59, 999);
    formData.set("vehicle_id", vehicle.id.toString());
    formData.set("start_datetime", startDate.toISOString());
    formData.set("end_datetime", endDate.toISOString());
    formData.set("party_id", party_id.toString());
    formData.set("status", BookingStatus.CONFIRMED);
    formData.set("reference", uuidv4());

    startTransition(() => {
      formAction(formData);
    });
  };
  
  useEffect(() => {
    if (state?.success && state?.bookingID) {
      router.push(`/booking-confirmation/${state.bookingID}`);
    }
  }, [state]);

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">
          Book {vehicle.model?.model}
        </h1>
      </div>

      {/* Selection Mode Toggle */}
      <div className="bg-white px-6 py-3 border-b border-[#f2f2f2]">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-[#333333]">Selection Mode:</span>
          <div className="flex bg-[#f2f2f2] rounded-full p-1">
            <button
              type="button"
              className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
                selectionMode === "range"
                  ? "bg-[#009639] text-white"
                  : "text-[#797879] hover:text-[#333333]"
              }`}
              onClick={() => {
                setSelectionMode("range");
                clearSelection();
              }}
            >
              Date Range
            </button>
            <button
              type="button"
              className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
                selectionMode === "individual"
                  ? "bg-[#009639] text-white"
                  : "text-[#797879] hover:text-[#333333]"
              }`}
              onClick={() => {
                setSelectionMode("individual");
                clearSelection();
              }}
            >
              Individual Dates
            </button>
          </div>
        </div>
        {selectionMode === "range" && (
          <p className="text-xs text-[#797879] mt-1">
            Click start date, then end date to select a range
          </p>
        )}
        {selectionMode === "individual" && (
          <p className="text-xs text-[#797879] mt-1">
            Click multiple dates to select individual days
          </p>
        )}
      </div>

      {/* Month Navigation */}
      <div className="p-4 flex items-center justify-between bg-white border-b border-[#f2f2f2]">
        <button
          className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center shadow-sm"
          onClick={handlePrevMonth}
        >
          <ChevronLeft size={20} className="text-[#009639]" />
        </button>
        <h2 className="text-lg font-semibold text-[#333333]">
          {monthName} {year}
        </h2>
        <button
          className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center shadow-sm"
          onClick={handleNextMonth}
        >
          <ChevronRight size={20} className="text-[#009639]" />
        </button>
      </div>

      {/* Calendar */}
      <div className="p-4 bg-white ">
        {/* Weekdays */}
        <div className="grid grid-cols-7 mb-2">
          {["S", "M", "T", "W", "T", "F", "S"].map((day, index) => (
            <div
              key={index}
              className="text-center text-[#797879] text-sm font-medium"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Days */}
        <div className="grid grid-cols-7 gap-1">
          {/* Empty cells for days before the first day of month */}
          {Array.from({ length: firstDayOfMonth }).map((_, index) => (
            <div key={`empty-${index}`} className="h-10"></div>
          ))}

          {/* Actual days */}
          {Array.from({ length: daysInMonth }).map((_, index) => {
            const day = index + 1;

            return (
              <div key={day} className="flex justify-center items-center">
                <button
                  className={getDayClass(day)}
                  onClick={() => handleDayClick(day)}
                >
                  {day}
                </button>
              </div>
            );
          })}
        </div>
      </div>

      {/* Legend */}
      <div className="p-4 bg-white border-t border-b border-[#f2f2f2] drop-shadow-sm">
        <div className="flex flex-wrap gap-3">
          <div className="flex items-center">
            <div className="h-4 w-4 rounded-full bg-[#009639] mr-1"></div>
            <span className="text-xs text-[#333333]">Selected</span>
          </div>
          <div className="flex items-center">
            <div className="h-4 w-4 rounded-full bg-[#e6ffe6] mr-1"></div>
            <span className="text-xs text-[#333333]">Confirmed</span>
          </div>
          <div className="flex items-center">
            <div className="h-4 w-4 rounded-full bg-[#FFD700] mr-1"></div>
            <span className="text-xs text-[#333333]">Completed</span>
          </div>
          <div className="flex items-center">
            <div className="h-4 w-4 rounded-full bg-[#ffe6e6] mr-1"></div>
            <span className="text-xs text-[#333333]">Pending</span>
          </div>
          <div className="flex items-center">
            <div className="h-4 w-4 rounded-full bg-white border border-[#d6d9dd] mr-1"></div>
            <span className="text-xs text-[#333333]">Available</span>
          </div>
        </div>
      </div>

      {/* Conflict Warning */}
      {showConflictWarning && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-start shadow-sm">
          <AlertCircle
            size={20}
            className="text-yellow-500 mr-2 flex-shrink-0 mt-0.5"
          />
          <div>
            <p className="text-sm text-yellow-700 font-medium">
              Booking Conflict
            </p>
            <p className="text-xs text-yellow-600">
              {selectionMode === "range" 
                ? "The selected range contains unavailable dates. Please select a different range."
                : "This date is already booked or reserved for maintenance."
              }
            </p>
          </div>
        </div>
      )}

      {/* Booking Details */}
      <form onSubmit={handleSubmit}>
      {state?.errors && (
        <div className="bg-red-100 text-red-700 p-4 rounded">
          <ul>
            {Object.entries(state.errors).map(([field, messages]) =>
              Array.isArray(messages)
                ? messages.map((msg, i) => <li key={`${field}-${i}`}>{msg}</li>)
                : null
            )}
          </ul>
        </div>
      )}
        {state?.message && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 text-sm">
            {state?.message}
          </div>
        )}
        <div className="p-4">
          <div className="ride-card p-4 space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-[#333333] font-medium">
                {selectionMode === "range" ? "Selected Date Range" : "Selected Dates"}
              </label>
              {selectedDates.length > 0 && (
                <button
                  type="button"
                  onClick={clearSelection}
                  className="text-xs text-[#797879] hover:text-[#333333] underline"
                >
                  Clear Selection
                </button>
              )}
            </div>
            
            {selectedDates.length > 0 ? (
              <div>
                {selectionMode === "range" && selectedDates.length >= 2 ? (
                  <div className="bg-[#e6ffe6] text-[#009639] px-4 py-3 rounded-lg border border-[#009639]/20">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">
                          {monthName} {Math.min(...selectedDates)} - {monthName} {Math.max(...selectedDates)}
                        </p>
                        <p className="text-xs mt-1">
                          {selectedDates.length} {selectedDates.length === 1 ? 'day' : 'days'} selected
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-wrap gap-2">
                    {selectedDates.map((date) => (
                      <div
                        key={date}
                        className="bg-[#e6ffe6] text-[#009639] px-3 py-1 rounded-full text-sm shadow-sm border border-gray-100 flex items-center"
                      >
                        {monthName} {date}
                        {selectionMode === "individual" && (
                          <button
                            type="button"
                            onClick={() => setSelectedDates(selectedDates.filter(d => d !== date))}
                            className="ml-2 text-[#009639] hover:text-[#007A2F]"
                          >
                            ×
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <p className="text-[#797879] text-sm">
                {selectionMode === "range" 
                  ? "Select start and end dates for your booking"
                  : "Select individual dates for your booking"
                }
              </p>
            )}
          </div>
        </div>

        {/* Confirm Button */}
        <FormSubmit selectedDates={selectedDates} />
      </form>
    </div>
  );
}
