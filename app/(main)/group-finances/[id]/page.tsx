"use client";

import { useState, use } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  DollarSign,
  ChevronRight,
  AlertCircle,
  BarChart3,
  FileText,
  ArrowDownCircle,
  ArrowUpCircle,
  Filter,
} from "lucide-react";

export default function GroupFinancesScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const groupId = use(params).id;
  const [activeTab, setActiveTab] = useState("overview");

  // Mock data
  const finances = {
    balance: 1250,
    expenses: [
      { category: "Insurance", amount: 450, percentage: 36 },
      { category: "Maintenance Fund", amount: 350, percentage: 28 },
      { category: "Registration Fees", amount: 200, percentage: 16 },
      { category: "Pull Subscription", amount: 250, percentage: 20 },
    ],
    transactions: [
      {
        id: 1,
        type: "deposit",
        user: "<PERSON>",
        amount: 150,
        date: "Today",
        status: "completed",
      },
      {
        id: 2,
        type: "withdrawal",
        user: "System",
        amount: 75,
        date: "Yesterday",
        description: "Insurance payment",
        status: "completed",
      },
      {
        id: 3,
        type: "deposit",
        user: "<PERSON>",
        amount: 150,
        date: "May 15, 2023",
        status: "completed",
      },
      {
        id: 4,
        type: "withdrawal",
        user: "System",
        amount: 120,
        date: "May 10, 2023",
        description: "Maintenance service",
        status: "completed",
      },
      {
        id: 5,
        type: "deposit",
        user: "Sarah Johnson",
        amount: 150,
        date: "May 5, 2023",
        status: "completed",
      },
    ],
    pendingPayments: [
      {
        id: 1,
        user: "John Smith",
        amount: 150,
        dueDate: "May 25, 2023",
      },
    ],
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Group Finances</h1>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow-sm mb-4">
        <div className="flex px-2">
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "overview" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("overview")}
          >
            Overview
            {activeTab === "overview" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "transactions" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("transactions")}
          >
            Transactions
            {activeTab === "transactions" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
        </div>
      </div>

      {activeTab === "overview" && (
        <div className="p-4">
          {/* Bank Account Balance */}
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <DollarSign size={20} className="text-[#009639]" />
                </div>
                <h3 className="text-[#333333] font-medium">Account Balance</h3>
              </div>
              <button
                className="text-[#009639] text-sm flex items-center"
                onClick={() => router.push(`/account-details/${groupId}`)}
              >
                Details <ChevronRight size={16} />
              </button>
            </div>

            <div className="text-center mb-4">
              <p className="text-3xl font-bold text-[#009639]">
                R{finances.balance}
              </p>
              <p className="text-sm text-[#797879]">Available Balance</p>
            </div>

            <button
              className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3 rounded-full text-lg font-semibold shadow-md"
              onClick={() => router.push(`/payment/${groupId}`)}
            >
              Make Payment
            </button>
          </div>

          {/* Monthly Expenses */}
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <BarChart3 size={20} className="text-[#009639]" />
                </div>
                <h3 className="text-[#333333] font-medium">Monthly Expenses</h3>
              </div>
              <span className="text-sm text-[#797879]">Total: R1,250</span>
            </div>

            <div className="space-y-3">
              {finances.expenses.map((expense, index) => (
                <div key={index}>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-[#333333]">{expense.category}</span>
                    <span className="text-[#333333] font-medium">
                      R{expense.amount}
                    </span>
                  </div>
                  <div className="h-2 bg-[#f2f2f2] rounded-full overflow-hidden">
                    <div
                      className="h-full rounded-full"
                      style={{
                        width: `${expense.percentage}%`,
                        backgroundColor:
                          index === 0
                            ? "#009639"
                            : index === 1
                            ? "#33ab5f"
                            : index === 2
                            ? "#66c085"
                            : "#99d5ab",
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Pending Payments */}
          {finances.pendingPayments.length > 0 && (
            <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 bg-[#fff8e6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <AlertCircle size={20} className="text-[#FFB800]" />
                </div>
                <h3 className="text-[#333333] font-medium">Pending Payments</h3>
              </div>

              {finances.pendingPayments.map((payment) => (
                <div
                  key={payment.id}
                  className="bg-[#fff8e6] p-3 rounded-lg shadow-sm mb-2"
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-[#333333] font-medium">
                        {payment.user}
                      </p>
                      <p className="text-xs text-[#797879]">
                        Due: {payment.dueDate}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-[#333333] font-bold">
                        R{payment.amount}
                      </p>
                      <button
                        className="text-xs text-[#009639] font-medium"
                        onClick={() =>
                          router.push(`/payment-reminder/${payment.id}`)
                        }
                      >
                        Send Reminder
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Recent Transactions */}
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <FileText size={20} className="text-[#009639]" />
                </div>
                <h3 className="text-[#333333] font-medium">
                  Recent Transactions
                </h3>
              </div>
              <button
                className="text-[#009639] text-sm flex items-center"
                onClick={() => setActiveTab("transactions")}
              >
                View All <ChevronRight size={16} />
              </button>
            </div>

            <div className="space-y-3">
              {finances.transactions.slice(0, 3).map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm border border-gray-100"
                >
                  <div className="flex items-center">
                    {transaction.type === "deposit" ? (
                      <ArrowDownCircle
                        size={20}
                        className="text-green-500 mr-2"
                      />
                    ) : (
                      <ArrowUpCircle size={20} className="text-red-500 mr-2" />
                    )}
                    <div>
                      <p className="text-[#333333] font-medium">
                        {transaction.type === "deposit"
                          ? "Payment from"
                          : "Payment to"}{" "}
                        {transaction.user}
                      </p>
                      <p className="text-xs text-[#797879]">
                        {transaction.date}
                      </p>
                    </div>
                  </div>
                  <p
                    className={`font-medium ${
                      transaction.type === "deposit"
                        ? "text-green-500"
                        : "text-red-500"
                    }`}
                  >
                    {transaction.type === "deposit" ? "+" : "-"}R
                    {transaction.amount}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === "transactions" && (
        <div className="p-4">
          {/* Filter */}
          <div className="flex justify-between mb-4">
            <div className="relative flex-1 max-w-xs">
              <input
                type="text"
                placeholder="Search transactions"
                className="w-full pl-10 pr-4 py-2 rounded-full border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="#797879"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="11" cy="11" r="8" />
                  <line x1="21" y1="21" x2="16.65" y2="16.65" />
                </svg>
              </div>
            </div>
            <button className="bg-white p-2 rounded-full border border-[#d6d9dd] shadow-sm">
              <Filter size={20} className="text-[#009639]" />
            </button>
          </div>

          {/* Transactions List */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
            {finances.transactions.map((transaction) => (
              <div
                key={transaction.id}
                className="p-4 flex items-center justify-between border-b border-[#f2f2f2] last:border-b-0"
              >
                <div className="flex items-center">
                  {transaction.type === "deposit" ? (
                    <ArrowDownCircle
                      size={24}
                      className="text-green-500 mr-3"
                    />
                  ) : (
                    <ArrowUpCircle size={24} className="text-red-500 mr-3" />
                  )}
                  <div>
                    <p className="text-[#333333] font-medium">
                      {transaction.type === "deposit"
                        ? "Payment from"
                        : "Payment to"}{" "}
                      {transaction.user}
                    </p>
                    {transaction.description && (
                      <p className="text-sm text-[#797879]">
                        {transaction.description}
                      </p>
                    )}
                    <p className="text-xs text-[#797879]">{transaction.date}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p
                    className={`font-medium ${
                      transaction.type === "deposit"
                        ? "text-green-500"
                        : "text-red-500"
                    }`}
                  >
                    {transaction.type === "deposit" ? "+" : "-"}R
                    {transaction.amount}
                  </p>
                  <p className="text-xs text-[#797879]">{transaction.status}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Download Statement */}
          <button
            className="w-full border border-[#009639] text-[#009639] py-3 rounded-full text-lg font-medium mb-4 shadow-sm"
            onClick={() => console.log("Download statement")}
          >
            Download Statement
          </button>
        </div>
      )}
    </div>
  );
}
