"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Search,
  Filter,
  ChevronDown,
  Car,
  DollarSign,
  MapPin,
  Calendar,
  ChevronRight,
  X,
} from "lucide-react";

export default function VehicleSearchScreen() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    vehicleType: "all",
    priceRange: [0, 500000],
    year: [2010, 2023],
    make: "all",
    location: "all",
  });

  // Mock data - in a real app, this would come from an API
  const vehicles = [
    {
      id: 1,
      make: "Toyota",
      model: "Fortuner",
      year: 2022,
      price: 450000,
      location: "Cape Town, Western Cape",
      distance: "5 km away",
      type: "SUV",
      image: "/placeholder.svg?height=120&width=200",
      seller: {
        name: "Toyota SA",
        rating: 4.8,
        verified: true,
      },
    },
    {
      id: 2,
      make: "Volkswagen",
      model: "Polo",
      year: 2021,
      price: 250000,
      location: "Johannesburg, Gauteng",
      distance: "12 km away",
      type: "Hatchback",
      image: "/placeholder.svg?height=120&width=200",
      seller: {
        name: "VW South Africa",
        rating: 4.6,
        verified: true,
      },
    },
    {
      id: 3,
      make: "Toyota",
      model: "Hilux",
      year: 2020,
      price: 380000,
      location: "Durban, KwaZulu-Natal",
      distance: "20 km away",
      type: "Bakkie",
      image: "/placeholder.svg?height=120&width=200",
      seller: {
        name: "Toyota Durban",
        rating: 4.5,
        verified: true,
      },
    },
    {
      id: 4,
      make: "Ford",
      model: "Ranger",
      year: 2021,
      price: 420000,
      location: "Pretoria, Gauteng",
      distance: "3 km away",
      type: "Bakkie",
      image: "/placeholder.svg?height=120&width=200",
      seller: {
        name: "Ford Pretoria",
        rating: 4.7,
        verified: false,
      },
    },
    {
      id: 5,
      make: "Hyundai",
      model: "i20",
      year: 2022,
      price: 220000,
      location: "Bloemfontein, Free State",
      distance: "8 km away",
      type: "Hatchback",
      image: "/placeholder.svg?height=120&width=200",
      seller: {
        name: "Hyundai Bloemfontein",
        rating: 4.9,
        verified: true,
      },
    },
  ];

  const vehicleTypes = [
    "All",
    "SUV",
    "Sedan",
    "Bakkie",
    "Hatchback",
    "Crossover",
  ];
  const makes = [
    "All",
    "Toyota",
    "Volkswagen",
    "Ford",
    "Hyundai",
    "Nissan",
    "Mercedes-Benz",
  ];
  const locations = [
    "All",
    "Cape Town, Western Cape",
    "Johannesburg, Gauteng",
    "Durban, KwaZulu-Natal",
    "Pretoria, Gauteng",
    "Bloemfontein, Free State",
  ];

  const filteredVehicles = vehicles.filter((vehicle) => {
    const matchesSearch =
      searchQuery === "" ||
      vehicle.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vehicle.model.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType =
      filters.vehicleType === "all" ||
      vehicle.type.toLowerCase() === filters.vehicleType.toLowerCase();

    const matchesMake =
      filters.make === "all" ||
      vehicle.make.toLowerCase() === filters.make.toLowerCase();

    const matchesLocation =
      filters.location === "all" || vehicle.location.includes(filters.location);

    const matchesPrice =
      vehicle.price >= filters.priceRange[0] &&
      vehicle.price <= filters.priceRange[1];

    const matchesYear =
      vehicle.year >= filters.year[0] && vehicle.year <= filters.year[1];

    return (
      matchesSearch &&
      matchesType &&
      matchesMake &&
      matchesLocation &&
      matchesPrice &&
      matchesYear
    );
  });

  const handleFilterChange = (
    filterName: keyof typeof filters,
    value: string | number | number[]
  ) => {
    setFilters({
      ...filters,
      [filterName]: value,
    });
  };

  const resetFilters = () => {
    setFilters({
      vehicleType: "all",
      priceRange: [0, 500000],
      year: [2010, 2023],
      make: "all",
      location: "all",
    });
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#eef1f9"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Find Vehicles</h1>
      </div>

      {/* Search */}
      <div className="p-4 bg-white border-b border-[#f2f2f2]">
        <div className="flex space-x-2">
          <div className="flex-1 bg-[#f2f2f2] rounded-full px-4 py-2 flex items-center">
            <Search size={18} className="text-[#009639] mr-2" />
            <input
              type="text"
              placeholder="Search make, model, or keyword"
              className="flex-1 bg-transparent focus:outline-none text-[#333333] text-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <button
                className="text-[#797879]"
                onClick={() => setSearchQuery("")}
              >
                <X size={16} />
              </button>
            )}
          </div>
          <button
            className={`px-3 py-2 rounded-full flex items-center ${
              showFilters
                ? "bg-[#009639] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter size={18} className="mr-1" />
            <span>Filters</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="p-4 bg-white border-b border-[#f2f2f2]">
          <div className="space-y-4">
            <div>
              <label className="text-[#333333] font-medium mb-2 block">
                Vehicle Type
              </label>
              <div className="flex flex-wrap gap-2">
                {vehicleTypes.map((type) => (
                  <button
                    key={type}
                    className={`px-3 py-1 rounded-full text-sm ${
                      filters.vehicleType === type.toLowerCase()
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() =>
                      handleFilterChange("vehicleType", type.toLowerCase())
                    }
                  >
                    {type}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="text-[#333333] font-medium mb-2 block">
                Price Range
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="range"
                  min="0"
                  max="500000"
                  step="5000"
                  value={filters.priceRange[1]}
                  onChange={(e) =>
                    handleFilterChange("priceRange", [
                      filters.priceRange[0],
                      parseInt(e.target.value),
                    ])
                  }
                  className="w-full"
                />
                <span className="text-sm text-[#333333]">
                  R{filters.priceRange[0]} - R{filters.priceRange[1]}
                </span>
              </div>
            </div>

            <div>
              <label className="text-[#333333] font-medium mb-2 block">
                Year
              </label>
              <div className="flex items-center space-x-2">
                <select
                  value={filters.year[0]}
                  onChange={(e) =>
                    handleFilterChange("year", [
                      parseInt(e.target.value),
                      filters.year[1],
                    ])
                  }
                  className="ride-input flex-1"
                >
                  {Array.from({ length: 14 }, (_, i) => 2010 + i).map(
                    (year) => (
                      <option key={`from-${year}`} value={year}>
                        {year}
                      </option>
                    )
                  )}
                </select>
                <span>to</span>
                <select
                  value={filters.year[1]}
                  onChange={(e) =>
                    handleFilterChange("year", [
                      filters.year[0],
                      parseInt(e.target.value),
                    ])
                  }
                  className="ride-input flex-1"
                >
                  {Array.from({ length: 14 }, (_, i) => 2010 + i).map(
                    (year) => (
                      <option key={`to-${year}`} value={year}>
                        {year}
                      </option>
                    )
                  )}
                </select>
              </div>
            </div>

            <div>
              <label className="text-[#333333] font-medium mb-2 block">
                Make
              </label>
              <select
                value={filters.make}
                onChange={(e) => handleFilterChange("make", e.target.value)}
                className="ride-input w-full"
              >
                {makes.map((make) => (
                  <option key={make} value={make.toLowerCase()}>
                    {make}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-[#333333] font-medium mb-2 block">
                Location
              </label>
              <select
                value={filters.location}
                onChange={(e) => handleFilterChange("location", e.target.value)}
                className="ride-input w-full"
              >
                {locations.map((location) => (
                  <option key={location} value={location.toLowerCase()}>
                    {location}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex space-x-3">
              <button
                className="flex-1 py-2 border border-[#d6d9dd] rounded-full text-[#333333] shadow-sm"
                onClick={resetFilters}
              >
                Reset Filters
              </button>
              <button
                className="flex-1 py-2 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full shadow-md"
                onClick={() => setShowFilters(false)}
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Results Count */}
      <div className="p-4">
        <p className="text-[#333333] font-medium">
          {filteredVehicles.length} vehicles found
        </p>
      </div>

      {/* Vehicle List */}
      <div className="px-4 pb-24 space-y-3">
        {filteredVehicles.length > 0 ? (
          filteredVehicles.map((vehicle) => (
            <div
              key={vehicle.id}
              className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 p-4"
              onClick={() => router.push(`/vehicle-details/${vehicle.id}`)}
            >
              <div className="flex">
                <div className="w-24 h-20 bg-[#f2f2f2] rounded-lg overflow-hidden mr-3 flex-shrink-0">
                  <Image
                    src={vehicle.image}
                    alt={`${vehicle.make} ${vehicle.model}`}
                    width={96}
                    height={80}
                    className="object-cover w-full h-full"
                  />
                </div>
                <div className="flex-1">
                  <h3 className="text-[#333333] font-medium">
                    {vehicle.year} {vehicle.make} {vehicle.model}
                  </h3>
                  <div className="flex items-center mt-1">
                    <DollarSign size={14} className="text-[#009639] mr-1" />
                    <span className="text-[#333333] font-bold">
                      R{vehicle.price.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex items-center mt-1">
                    <MapPin size={12} className="text-[#797879] mr-1" />
                    <span className="text-xs text-[#797879]">
                      {vehicle.distance}
                    </span>
                  </div>
                </div>
                <div className="flex items-center">
                  <ChevronRight size={20} className="text-[#009639]" />
                </div>
              </div>
              <div className="mt-2 pt-2 border-t border-[#f2f2f2] flex items-center justify-between">
                <div className="flex items-center">
                  <Car size={14} className="text-[#797879] mr-1" />
                  <span className="text-xs text-[#797879]">{vehicle.type}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-xs text-[#797879] mr-1">
                    Seller: {vehicle.seller.name}
                  </span>
                  {vehicle.seller.verified && (
                    <span className="text-xs px-1 py-0.5 rounded-full bg-[#e6ffe6] text-[#009639] font-medium">
                      Verified
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 p-6 text-center">
            <p className="text-[#797879]">No vehicles found</p>
            <p className="text-[#797879] text-sm mt-1">
              Try adjusting your filters
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
