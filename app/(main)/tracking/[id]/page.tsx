"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Phone,
  MessageSquare,
  MapPin,
  Clock,
  ChevronRight,
} from "lucide-react";

export default function RideTrackingScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const riderId = use(params).id;

  const [arrivalTime, setArrivalTime] = useState(10);
  const [distance, setDistance] = useState(0.5);

  // Mock data - in a real app, this would come from an API
  const ride = {
    id: "R" + Math.floor(Math.random() * 10000),
    rider: {
      id: riderId,
      name:
        riderId === "1"
          ? "Jane Cooper"
          : riderId === "2"
          ? "Esther Howard"
          : riderId === "3"
          ? "Leslie Alexander"
          : "Robert Fox",
      rating: 4.9,
      phone: "+****************",
      avatar: "/placeholder.svg?height=80&width=80",
      carType: "Luxury Sedan",
      carImage: "/placeholder.svg?height=60&width=100",
      licensePlate: "ABC 123",
    },
    pickup: "Your current location",
    destination: "1640 Riverside Drive, Hill Valley",
    price: "$60",
  };

  // Simulate ride progress
  useEffect(() => {
    const timer = setInterval(() => {
      setArrivalTime((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });

      setDistance((prev) => {
        if (prev <= 0.1) {
          return 0;
        }
        return +(prev - 0.05).toFixed(1);
      });
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="flex flex-col h-screen bg-[#eef1f9]">
      {/* Status Bar */}
      <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#eef1f9"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white px-6 py-4 flex items-center border-b border-[#f2f2f2]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-[#333333]" />
        </button>
        <h1 className="text-xl font-bold text-[#333333]">Track Ride</h1>
      </div>

      {/* Map View */}
      <div className="relative flex-1 bg-[#eef1f9] overflow-hidden">
        {/* Map Background with Roads */}
        <div className="absolute inset-0 opacity-20">
          <svg
            width="100%"
            height="100%"
            viewBox="0 0 400 400"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M0,200 L400,150" stroke="#333" strokeWidth="1" />
            <path d="M100,0 L150,400" stroke="#333" strokeWidth="1" />
            <path d="M250,0 L300,400" stroke="#333" strokeWidth="1" />
            <path d="M0,100 L400,50" stroke="#333" strokeWidth="1" />
            <path d="M0,300 L400,350" stroke="#333" strokeWidth="1" />
          </svg>
        </div>

        {/* Blue Circle Area */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 rounded-full border-2 border-[#67b6ff] bg-[#d6e6fa] bg-opacity-20"></div>

        {/* Origin Marker */}
        <div className="absolute top-2/5 left-1/3">
          <div className="bg-[#0286ff] rounded-full p-2 shadow-md">
            <div className="w-2 h-2 bg-white rounded-full"></div>
          </div>
        </div>

        {/* Destination Marker */}
        <div className="absolute top-1/5 right-1/3">
          <div className="bg-[#0cc25f] rounded-full p-2 shadow-md">
            <MapPin size={16} className="text-white" />
          </div>
        </div>

        {/* Car Marker */}
        <div className="absolute top-1/3 left-2/5 ride-map-marker h-12 w-12 z-10">
          <div className="bg-white p-1 rounded-sm transform rotate-45">
            <svg width="20" height="10" viewBox="0 0 20 10" fill="#333333">
              <rect width="20" height="10" rx="2" />
            </svg>
          </div>
        </div>

        {/* Route Line */}
        <svg
          className="absolute inset-0"
          width="100%"
          height="100%"
          viewBox="0 0 400 400"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M150,200 C200,150 250,150 300,100"
            stroke="#0286ff"
            strokeWidth="4"
            fill="none"
            strokeLinecap="round"
          />
        </svg>
      </div>

      {/* Ride Info Panel */}
      <div className="bg-white rounded-t-3xl -mt-4 z-10">
        {/* ETA */}
        <div className="p-4 flex items-center justify-between border-b border-[#f2f2f2]">
          <div>
            <h3 className="text-lg font-semibold text-[#333333]">
              Arriving in {arrivalTime} min
            </h3>
            <p className="text-sm text-[#797879]">{distance} miles away</p>
          </div>
          <div className="flex space-x-2">
            <button className="w-10 h-10 bg-[#0286ff] rounded-full flex items-center justify-center">
              <Phone size={18} className="text-white" />
            </button>
            <button className="w-10 h-10 bg-[#0286ff] rounded-full flex items-center justify-center">
              <MessageSquare size={18} className="text-white" />
            </button>
          </div>
        </div>

        {/* Rider Info */}
        <div className="p-4 flex items-center border-b border-[#f2f2f2]">
          <div className="w-14 h-14 ride-avatar mr-4">
            <Image
              src={ride.rider.avatar}
              alt={ride.rider.name}
              width={56}
              height={56}
              className="object-cover"
            />
          </div>
          <div className="flex-1">
            <h3 className="text-base font-semibold text-[#333333]">
              {ride.rider.name}
            </h3>
            <div className="flex items-center">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="#ff5c00">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" />
              </svg>
              <span className="ml-1 text-xs text-[#797879]">
                {ride.rider.rating}
              </span>
              <span className="mx-1 text-[#d6d9dd]">•</span>
              <span className="text-xs text-[#797879]">
                {ride.rider.licensePlate}
              </span>
            </div>
          </div>
          <div className="flex-shrink-0">
            <Image
              src={ride.rider.carImage}
              alt="Car"
              width={80}
              height={48}
              className="object-contain"
            />
          </div>
        </div>

        {/* Trip Details */}
        <div className="p-4">
          <div className="flex items-start mb-3">
            <div className="flex-shrink-0 mr-3">
              <div className="flex flex-col items-center">
                <div className="w-3 h-3 rounded-full bg-[#0286ff]"></div>
                <div className="w-0.5 h-6 bg-[#d6d9dd] my-1"></div>
                <div className="w-3 h-3 rounded-full bg-[#0cc25f]"></div>
              </div>
            </div>
            <div className="flex-1">
              <div className="mb-2">
                <div className="text-xs text-[#797879] mb-0.5">From</div>
                <div className="text-sm text-[#333333] font-medium">
                  {ride.pickup}
                </div>
              </div>
              <div>
                <div className="text-xs text-[#797879] mb-0.5">To</div>
                <div className="text-sm text-[#333333] font-medium">
                  {ride.destination}
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center mt-2">
            <span className="text-sm text-[#333333] font-medium">
              Total Price
            </span>
            <span className="text-lg text-[#333333] font-bold">
              {ride.price}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
