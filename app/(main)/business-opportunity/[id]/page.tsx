"use client";

import React, { useState, use } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Building,
  Calendar,
  DollarSign,
  Car,
  MapPin,
  Clock,
  ChevronRight,
  Star,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";

export default function BusinessOpportunityScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}): React.ReactNode {
  const router = useRouter();
  const opportunityId = use(params).id;
  const [activeTab, setActiveTab] = useState("details");
  const [showApplyModal, setShowApplyModal] = useState(false);

  // Mock data - in a real app, this would come from an API
  const opportunity = {
    id: opportunityId,
    title:
      opportunityId === "1"
        ? "Corporate Fleet Partnership"
        : opportunityId === "2"
        ? "Weekend Tour Service"
        : "Delivery Service Partnership",
    company: {
      name:
        opportunityId === "1"
          ? "TechCorp Inc."
          : opportunityId === "2"
          ? "City Explorer Tours"
          : "Swift Delivery",
      logo: "/placeholder.svg?height=60&width=60",
      rating: 4.7,
      verified: true,
      founded: "2015",
      location: "Cape Town, Western Cape",
      description:
        opportunityId === "1"
          ? "Leading technology company with offices across South Africa."
          : opportunityId === "2"
          ? "Premier tour operator specializing in city tours."
          : "Fast-growing delivery service for local businesses.",
    },
    type:
      opportunityId === "1"
        ? "corporate"
        : opportunityId === "2"
        ? "tourism"
        : "delivery",
    duration:
      opportunityId === "1"
        ? "12 months"
        : opportunityId === "2"
        ? "6 months"
        : "3 months",
    startDate:
      opportunityId === "1"
        ? "2023-07-01"
        : opportunityId === "2"
        ? "2023-06-15"
        : "2023-06-01",
    vehicleRequirements: {
      type:
        opportunityId === "1"
          ? "Sedan or SUV"
          : opportunityId === "2"
          ? "SUV or Minivan"
          : "Hatchback or Sedan",
      year: "2018 or newer",
      condition: "Excellent",
      features:
        opportunityId === "1"
          ? ["Air conditioning", "Leather seats", "GPS navigation"]
          : opportunityId === "2"
          ? ["Air conditioning", "Spacious interior", "Good fuel economy"]
          : ["Good fuel economy", "Reliable", "Well maintained"],
    },
    financials: {
      estimatedMonthlyRevenue:
        opportunityId === "1" ? 8500 : opportunityId === "2" ? 12000 : 6000,
      paymentStructure:
        opportunityId === "1"
          ? "Fixed monthly payment"
          : opportunityId === "2"
          ? "Base payment plus per-tour bonus"
          : "Per-delivery commission",
      paymentFrequency:
        opportunityId === "1"
          ? "Monthly"
          : opportunityId === "2"
          ? "Bi-weekly"
          : "Weekly",
      expenses: {
        fuel:
          opportunityId === "1"
            ? "Reimbursed"
            : opportunityId === "2"
            ? "Included in payment"
            : "Owner responsibility",
        maintenance:
          opportunityId === "1"
            ? "Shared 50/50"
            : opportunityId === "2"
            ? "Owner responsibility"
            : "Owner responsibility",
        insurance:
          opportunityId === "1"
            ? "Provided by company"
            : opportunityId === "2"
            ? "Supplemental provided"
            : "Owner responsibility",
      },
    },
    requirements: [
      "Valid vehicle registration",
      "Comprehensive insurance",
      "Recent vehicle service history",
      "Vehicle inspection approval",
    ],
    applicationProcess: [
      "Submit application",
      "Vehicle inspection",
      "Contract review",
      "Onboarding",
    ],
    termsAndConditions: [
      "Vehicle must be available during specified hours",
      "Owner responsible for regular maintenance",
      "30-day notice required for contract termination",
      "Performance reviews conducted quarterly",
    ],
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-ZA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-ZA", {
      style: "currency",
      currency: "ZAR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleApply = () => {
    console.log("Applying for opportunity:", opportunityId);
    // In a real app, you would make an API call here
    router.push(`/application-submitted/${opportunityId}`);
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Business Opportunity</h1>
      </div>

      {/* Opportunity Header */}
      <div className=" px-6 pt-4 pb-8 mb-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center mb-4">
            <div className="w-16 h-16 bg-white rounded-full border-2 border-[#e6ffe6] overflow-hidden flex items-center justify-center mr-4 shadow-md">
              <Image
                src={opportunity.company.logo}
                alt={opportunity.company.name}
                width={64}
                height={64}
                className="object-cover"
              />
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-bold text-[#333333] mb-1">
                {opportunity.title}
              </h2>
              <div className="flex items-center">
                <span className="text-sm text-[#797879] mr-2">
                  {opportunity.company.name}
                </span>
                {opportunity.company.verified && (
                  <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#009639] font-medium">
                    Verified
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-4 mt-2 p-3 bg-[#f9f9f9] rounded-lg">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2 shadow-sm">
                <Calendar size={16} className="text-[#009639]" />
              </div>
              <div>
                <p className="text-xs text-[#797879]">Start Date</p>
                <p className="text-sm font-medium text-[#333333]">
                  {formatDate(opportunity.startDate)}
                </p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2 shadow-sm">
                <Clock size={16} className="text-[#009639]" />
              </div>
              <div>
                <p className="text-xs text-[#797879]">Duration</p>
                <p className="text-sm font-medium text-[#333333]">
                  {opportunity.duration}
                </p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2 shadow-sm">
                <MapPin size={16} className="text-[#009639]" />
              </div>
              <div>
                <p className="text-xs text-[#797879]">Location</p>
                <p className="text-sm font-medium text-[#333333]">
                  {opportunity.company.location}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow-sm mb-4 mx-4">
        <div className="flex px-2">
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "details" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("details")}
          >
            Details
            {activeTab === "details" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "financials" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("financials")}
          >
            Financials
            {activeTab === "financials" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "company" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("company")}
          >
            Company
            {activeTab === "company" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
        </div>
      </div>

      {/* Details Tab */}
      {activeTab === "details" && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">
              Vehicle Requirements
            </h3>
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Car size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Vehicle Type</p>
                  <p className="text-[#333333]">
                    {opportunity.vehicleRequirements.type}
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Calendar size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Year</p>
                  <p className="text-[#333333]">
                    {opportunity.vehicleRequirements.year}
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <CheckCircle size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Condition</p>
                  <p className="text-[#333333]">
                    {opportunity.vehicleRequirements.condition}
                  </p>
                </div>
              </div>
            </div>

            <h4 className="text-[#333333] font-medium mt-4 mb-2">
              Required Features
            </h4>
            <ul className="space-y-1">
              {opportunity.vehicleRequirements.features.map(
                (feature, index) => (
                  <li key={index} className="flex items-center">
                    <div className="w-1.5 h-1.5 bg-[#009639] rounded-full mr-2"></div>
                    <span className="text-sm text-[#333333]">{feature}</span>
                  </li>
                )
              )}
            </ul>
          </div>

          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">Requirements</h3>
            <ul className="space-y-2">
              {opportunity.requirements.map((requirement, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle
                    size={16}
                    className="text-[#009639] mr-2 mt-0.5"
                  />
                  <span className="text-sm text-[#333333]">{requirement}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">
              Application Process
            </h3>
            <div className="space-y-4">
              {opportunity.applicationProcess.map((step, index) => (
                <div key={index} className="flex items-start">
                  <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-sm font-medium text-[#009639]">
                      {index + 1}
                    </span>
                  </div>
                  <div className="flex-1">
                    <p className="text-[#333333]">{step}</p>
                    {index < opportunity.applicationProcess.length - 1 && (
                      <div className="h-6 w-0.5 bg-[#e6ffe6] ml-3 my-1"></div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Financials Tab */}
      {activeTab === "financials" && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">
              Revenue Potential
            </h3>
            <div className="p-4 bg-[#e6ffe6] rounded-lg mb-4">
              <p className="text-[#797879] text-xs mb-1">
                Estimated Monthly Revenue
              </p>
              <p className="text-2xl font-bold text-[#009639]">
                {formatCurrency(opportunity.financials.estimatedMonthlyRevenue)}
              </p>
            </div>
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <DollarSign size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Payment Structure</p>
                  <p className="text-[#333333]">
                    {opportunity.financials.paymentStructure}
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Calendar size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Payment Frequency</p>
                  <p className="text-[#333333]">
                    {opportunity.financials.paymentFrequency}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">Expenses</h3>
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start flex-1">
                  <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-sm font-medium text-[#009639]">
                      M
                    </span>
                  </div>
                  <div>
                    <p className="text-[#333333] font-medium">Maintenance</p>
                  </div>
                </div>
                <p className="text-[#333333]">
                  {opportunity.financials.expenses.maintenance}
                </p>
              </div>
              <div className="flex items-start justify-between">
                <div className="flex items-start flex-1">
                  <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-sm font-medium text-[#009639]">
                      I
                    </span>
                  </div>
                  <div>
                    <p className="text-[#333333] font-medium">Insurance</p>
                  </div>
                </div>
                <p className="text-[#333333]">
                  {opportunity.financials.expenses.insurance}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">
              Terms & Conditions
            </h3>
            <ul className="space-y-2">
              {opportunity.termsAndConditions.map((term, index) => (
                <li key={index} className="flex items-start">
                  <div className="w-1.5 h-1.5 bg-[#009639] rounded-full mr-2 mt-1.5"></div>
                  <span className="text-sm text-[#333333]">{term}</span>
                </li>
              ))}
            </ul>
            <div className="mt-4 p-3 bg-yellow-50 rounded-lg flex items-start">
              <AlertTriangle
                size={16}
                className="text-yellow-500 mr-2 flex-shrink-0 mt-0.5"
              />
              <p className="text-xs text-yellow-700">
                We recommend reviewing the full contract terms before applying.
                Full terms will be provided during the application process.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Company Tab */}
      {activeTab === "company" && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center mb-4">
              <div className="w-14 h-14 bg-white rounded-full border border-[#f2f2f2] overflow-hidden flex items-center justify-center mr-3">
                <Image
                  src={opportunity.company.logo}
                  alt={opportunity.company.name}
                  width={56}
                  height={56}
                  className="object-cover"
                />
              </div>
              <div>
                <h3 className="text-[#333333] font-medium">
                  {opportunity.company.name}
                </h3>
                <div className="flex items-center mt-1">
                  <Star size={14} className="text-[#ff5c00] mr-1" />
                  <span className="text-sm text-[#333333] mr-2">
                    {opportunity.company.rating}
                  </span>
                  {opportunity.company.verified && (
                    <span className="text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-800">
                      Verified
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Building size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Founded</p>
                  <p className="text-[#333333]">
                    {opportunity.company.founded}
                  </p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <MapPin size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Location</p>
                  <p className="text-[#333333]">
                    {opportunity.company.location}
                  </p>
                </div>
              </div>
            </div>
            <p className="text-[#333333] mt-4">
              {opportunity.company.description}
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">
              Other Opportunities
            </h3>
            <div className="space-y-3">
              {[1, 2, 3]
                .filter((id) => id.toString() !== opportunityId)
                .map((id) => (
                  <div
                    key={id}
                    className="p-3 border border-[#f2f2f2] rounded-lg flex items-center"
                    onClick={() => router.push(`/business-opportunity/${id}`)}
                  >
                    <div className="flex-1">
                      <h4 className="text-[#333333] font-medium">
                        {id === 1
                          ? "Corporate Fleet Partnership"
                          : id === 2
                          ? "Weekend Tour Service"
                          : "Delivery Service Partnership"}
                      </h4>
                      <p className="text-xs text-[#797879]">
                        {id === 1
                          ? "TechCorp Inc."
                          : id === 2
                          ? "City Explorer Tours"
                          : "Swift Delivery"}
                      </p>
                    </div>
                    <ChevronRight size={20} className="text-[#009639]" />
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}

      {/* Apply Button */}
      <div className=" bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] shadow-lg">
        <button
          className="w-full py-4 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full text-lg font-semibold shadow-md"
          onClick={() => setShowApplyModal(true)}
        >
          Apply Now
        </button>
      </div>

      {/* Apply Modal */}
      {showApplyModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
          <div className="bg-white rounded-xl p-6 w-full max-w-sm shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                <Building size={18} className="text-[#009639]" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-[#333333]">
                  Apply for {opportunity.title}
                </h3>
                <p className="text-xs text-[#797879]">
                  {opportunity.company.name}
                </p>
              </div>
            </div>

            <p className="text-[#797879] mb-4">
              By applying, you agree to share your vehicle information with{" "}
              {opportunity.company.name} for consideration.
            </p>
            <div className="p-4 bg-[#e6ffe6] rounded-lg mb-4">
              <h4 className="text-[#007A2F] font-medium mb-2">
                Required Information:
              </h4>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center mr-2 shadow-sm">
                    <CheckCircle size={12} className="text-[#009639]" />
                  </div>
                  <span className="text-sm text-[#333333]">
                    Vehicle details
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center mr-2 shadow-sm">
                    <CheckCircle size={12} className="text-[#009639]" />
                  </div>
                  <span className="text-sm text-[#333333]">
                    Contact information
                  </span>
                </li>
                <li className="flex items-center">
                  <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center mr-2 shadow-sm">
                    <CheckCircle size={12} className="text-[#009639]" />
                  </div>
                  <span className="text-sm text-[#333333]">
                    Availability schedule
                  </span>
                </li>
              </ul>
            </div>
            <div className="flex space-x-3">
              <button
                className="flex-1 py-3 border border-[#d6d9dd] rounded-full text-[#333333] font-medium"
                onClick={() => setShowApplyModal(false)}
              >
                Cancel
              </button>
              <button
                className="flex-1 py-3 bg-[#009639] text-white rounded-full shadow-md font-medium"
                onClick={handleApply}
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
