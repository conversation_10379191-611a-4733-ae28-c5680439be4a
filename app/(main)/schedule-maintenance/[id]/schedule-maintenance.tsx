"use client";
import FormSubmit from "./form-submit";
import { startTransition, useActionState, useEffect, useRef } from "react";

interface LoadingPageProps {
  action: (prevState: any, formData: FormData) => any;
  error?: string;
  vehicleID: number;
}

export default function ScheduleMaintenance({
  action,
  vehicleID,
}: LoadingPageProps) {
  const [state, formAction] = useActionState(action, {});
  const formRef = useRef<HTMLFormElement>(null);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    formData.set("vehicle_id", String(vehicleID));
    startTransition(() => {
      formAction(formData);
    });
  };
  useEffect(() => {
    if (state?.success) {
      formRef.current?.reset();
    }
  }, [state]);
  return (
    <form
      ref={formRef}
      onSubmit={handleSubmit}
      className="space-y-6 max-w-md mx-auto p-4"
    >
      {state?.errors && (
        <div className="bg-red-100 text-red-700 p-4 rounded">
          <ul>
            {Object.entries(state.errors).map(([field, messages]) =>
              Array.isArray(messages)
                ? messages.map((msg, i) => <li key={`${field}-${i}`}>{msg}</li>)
                : null
            )}
          </ul>
        </div>
      )}

      {state?.message && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 text-sm">
          {state.message}
        </div>
      )}

      {/* Name */}
      <div>
        <label htmlFor="name" className="font-medium block mb-2">
          Service Name
        </label>
        <input
          type="text"
          id="name"
          name="name"
          required
          className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] shadow-sm focus:outline-none focus:border-[#009639]"
        />
      </div>

      {/* Description (optional) */}
      <div>
        <label htmlFor="description" className="font-medium block mb-2">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] shadow-sm focus:outline-none focus:border-[#009639]"
        ></textarea>
      </div>

      {/* Due Date */}
      <div>
        <label htmlFor="due_date" className="font-medium block mb-2">
          Due Date
        </label>
        <input
          type="date"
          id="due_date"
          name="due_date"
          required
          className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] shadow-sm focus:outline-none focus:border-[#009639]"
        />
      </div>

      {/* Due Odometer */}
      <div>
        <label htmlFor="due_odometer" className="font-medium block mb-2">
          Odometer Reading
        </label>
        <input
          type="number"
          id="due_odometer"
          name="due_odometer"
          required
          className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] shadow-sm focus:outline-none focus:border-[#009639]"
        />
      </div>

      {/* Status */}
      <div>
        <label htmlFor="status" className="font-medium block mb-2">
          Status
        </label>
        <select
          id="status"
          name="status"
          required
          className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] bg-white shadow-sm focus:outline-none focus:border-[#009639]"
        >
          <option value="Scheduled">Scheduled</option>
          <option value="Pending">Pending</option>
          <option value="Completed">Completed</option>
        </select>
      </div>

      {/* Expected Cost */}
      <div>
        <label htmlFor="expected_cost" className="font-medium block mb-2">
          Expected Cost(R)
        </label>
        <input
          type="number"
          step="0.01"
          id="expected_cost"
          name="expected_cost"
          required
          className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] shadow-sm focus:outline-none focus:border-[#009639]"
        />
      </div>

      {/* Optional fields (shown for completeness, but can be omitted from UI if not needed initially) */}
      <div>
        <label htmlFor="service_provider" className="font-medium block mb-2">
          Service Provider
        </label>
        <input
          type="text"
          id="service_provider"
          name="service_provider"
          className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] shadow-sm focus:outline-none focus:border-[#009639]"
        />
      </div>

      <div>
        <label htmlFor="technician_notes" className="font-medium block mb-2">
          Technician Notes
        </label>
        <textarea
          id="technician_notes"
          name="technician_notes"
          className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] shadow-sm focus:outline-none focus:border-[#009639]"
        ></textarea>
      </div>

      {/* Checkbox: Is Scheduled */}
      <div className="flex items-center">
        <input
          type="checkbox"
          id="is_scheduled"
          name="is_scheduled"
          className="mr-2"
        />
        <label htmlFor="is_scheduled" className="text-sm font-medium">
          This service is already scheduled
        </label>
      </div>

      <FormSubmit />
    </form>
  );
}
