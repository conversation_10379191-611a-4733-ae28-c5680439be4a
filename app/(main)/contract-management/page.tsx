"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  FileText,
  Calendar,
  DollarSign,
  Car,
  Building,
  ChevronRight,
  Filter,
  Plus,
  CheckCircle,
  Clock,
  AlertCircle,
  BarChart,
} from "lucide-react";

export default function ContractManagementScreen() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("active");
  const [selectedType, setSelectedType] = useState<string | null>(null);

  // Mock data - in a real app, this would come from an API
  const contracts = [
    {
      id: 1,
      title: "Ride-Sharing Partnership",
      type: "partnership",
      status: "active",
      startDate: "2023-01-15",
      endDate: "2023-12-31",
      company: {
        name: "Urban Commute",
        logo: "/placeholder.svg?height=40&width=40",
      },
      vehicle: {
        id: "vehicle1",
        name: "Tesla Model 3",
      },
      group: {
        id: "group1",
        name: "Family SUV",
      },
      revenue: {
        total: 3500,
        current: 1800,
        projected: 5000,
      },
      paymentSchedule: "Monthly",
      nextPayment: {
        date: "2023-06-15",
        amount: 450,
      },
      terms: [
        "Vehicle available Monday-Friday, 9am-5pm",
        "Company responsible for insurance during operation",
        "Maintenance costs shared 50/50",
        "Revenue split: 70% to vehicle owners, 30% to company",
      ],
      performance: {
        rating: 4.8,
        metrics: {
          utilization: 85,
          customerSatisfaction: 4.7,
          onTimeRate: 92,
        },
      },
    },
    {
      id: 2,
      title: "Corporate Fleet Agreement",
      type: "corporate",
      status: "active",
      startDate: "2023-02-01",
      endDate: "2024-01-31",
      company: {
        name: "TechCorp Inc.",
        logo: "/placeholder.svg?height=40&width=40",
      },
      vehicle: {
        id: "vehicle2",
        name: "BMW X5",
      },
      group: {
        id: "group2",
        name: "Weekend Getaway",
      },
      revenue: {
        total: 5200,
        current: 2600,
        projected: 7800,
      },
      paymentSchedule: "Bi-weekly",
      nextPayment: {
        date: "2023-06-01",
        amount: 350,
      },
      terms: [
        "Vehicle available for executive transport",
        "Fixed monthly payment plus mileage bonus",
        "Company covers all maintenance and insurance",
        "24-hour notice required for bookings",
      ],
      performance: {
        rating: 4.5,
        metrics: {
          utilization: 70,
          customerSatisfaction: 4.6,
          onTimeRate: 95,
        },
      },
    },
    {
      id: 3,
      title: "Delivery Service Agreement",
      type: "delivery",
      status: "active",
      startDate: "2023-03-10",
      endDate: "2023-09-10",
      company: {
        name: "Swift Delivery",
        logo: "/placeholder.svg?height=40&width=40",
      },
      vehicle: {
        id: "vehicle3",
        name: "Toyota Prius",
      },
      group: {
        id: "group3",
        name: "Work Commute",
      },
      revenue: {
        total: 2800,
        current: 1400,
        projected: 4200,
      },
      paymentSchedule: "Weekly",
      nextPayment: {
        date: "2023-05-28",
        amount: 175,
      },
      terms: [
        "Vehicle available weekdays 10am-8pm",
        "Payment based on deliveries completed",
        "Company provides commercial insurance",
        "Fuel costs reimbursed by company",
      ],
      performance: {
        rating: 4.2,
        metrics: {
          utilization: 90,
          customerSatisfaction: 4.3,
          onTimeRate: 88,
        },
      },
    },
    {
      id: 4,
      title: "Weekend Tour Service",
      type: "tourism",
      status: "expired",
      startDate: "2022-06-01",
      endDate: "2023-05-01",
      company: {
        name: "City Explorer Tours",
        logo: "/placeholder.svg?height=40&width=40",
      },
      vehicle: {
        id: "vehicle2",
        name: "BMW X5",
      },
      group: {
        id: "group2",
        name: "Weekend Getaway",
      },
      revenue: {
        total: 8500,
        current: 8500,
        projected: 8500,
      },
      paymentSchedule: "Monthly",
      nextPayment: null,
      terms: [
        "Vehicle used for weekend city tours",
        "Fixed monthly payment plus tips",
        "Company provided driver and insurance",
        "Vehicle maintained to luxury standards",
      ],
      performance: {
        rating: 4.9,
        metrics: {
          utilization: 95,
          customerSatisfaction: 4.8,
          onTimeRate: 97,
        },
      },
    },
    {
      id: 5,
      title: "Film Production Vehicle",
      type: "entertainment",
      status: "expired",
      startDate: "2022-09-15",
      endDate: "2023-03-15",
      company: {
        name: "Spotlight Productions",
        logo: "/placeholder.svg?height=40&width=40",
      },
      vehicle: {
        id: "vehicle1",
        name: "Tesla Model 3",
      },
      group: {
        id: "group1",
        name: "Family SUV",
      },
      revenue: {
        total: 6200,
        current: 6200,
        projected: 6200,
      },
      paymentSchedule: "Project-based",
      nextPayment: null,
      terms: [
        "Vehicle used for film production",
        "Fixed project fee plus daily rate",
        "Company responsible for all damages",
        "Credit in film production",
      ],
      performance: {
        rating: 4.7,
        metrics: {
          utilization: 75,
          customerSatisfaction: 4.9,
          onTimeRate: 90,
        },
      },
    },
  ];

  const contractTypes = [
    { id: "partnership", label: "Partnership", icon: <Building size={16} /> },
    { id: "corporate", label: "Corporate", icon: <Building size={16} /> },
    { id: "delivery", label: "Delivery", icon: <Car size={16} /> },
    { id: "tourism", label: "Tourism", icon: <Car size={16} /> },
    { id: "entertainment", label: "Entertainment", icon: <Car size={16} /> },
  ];

  const filteredContracts = contracts.filter((contract) => {
    if (activeTab === "active" && contract.status !== "active") return false;
    if (activeTab === "expired" && contract.status !== "expired") return false;
    if (selectedType && contract.type !== selectedType) return false;
    return true;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-ZA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-ZA", {
      style: "currency",
      currency: "ZAR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#007A2F] font-medium">
            Active
          </span>
        );
      case "expired":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#f9f9f9] text-[#797879] font-medium">
            Expired
          </span>
        );
      case "pending":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#fff8e6] text-[#7A6500] font-medium">
            Pending
          </span>
        );
      default:
        return null;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle size={18} className="text-[#009639]" />;
      case "expired":
        return <Clock size={18} className="text-[#797879]" />;
      case "pending":
        return <Clock size={18} className="text-[#FFD700]" />;
      default:
        return <AlertCircle size={18} className="text-red-500" />;
    }
  };

  const getTotalRevenue = () => {
    return contracts
      .filter((contract) => contract.status === "active")
      .reduce((sum, contract) => sum + contract.revenue.current, 0);
  };

  const getProjectedRevenue = () => {
    return contracts
      .filter((contract) => contract.status === "active")
      .reduce((sum, contract) => sum + contract.revenue.projected, 0);
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Contract Management</h1>
        </div>
        <button
          className="bg-white text-[#009639] text-sm font-medium px-3 py-1.5 rounded-full shadow-sm flex items-center"
          onClick={() => router.push("/opportunities")}
        >
          <Plus size={14} className="mr-1" /> Find Opportunities
        </button>
      </div>

      {/* Revenue Summary */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-[#333333] font-medium">Revenue Summary</h3>
            <button
              className="bg-[#009639] text-white text-sm font-medium px-3 py-1.5 rounded-full shadow-sm flex items-center"
              onClick={() => router.push("/income-tracking/all")}
            >
              <BarChart size={14} className="mr-1" /> Details
            </button>
          </div>
          <div className="grid grid-cols-2 gap-3">
            <div className="p-3 bg-[#e6ffe6] rounded-lg shadow-sm">
              <p className="text-xs text-[#007A2F] mb-1 font-medium">
                Current Revenue
              </p>
              <p className="text-xl font-bold text-[#009639]">
                {formatCurrency(getTotalRevenue())}
              </p>
            </div>
            <div className="p-3 bg-[#f9f9f9] rounded-lg shadow-sm">
              <p className="text-xs text-[#797879] mb-1 font-medium">
                Projected Revenue
              </p>
              <p className="text-xl font-bold text-[#333333]">
                {formatCurrency(getProjectedRevenue())}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Contract Type Filter */}
      <div className="bg-white px-4 py-3 border-b border-[#f2f2f2] overflow-x-auto">
        <div className="flex space-x-2">
          <button
            className={`px-3 py-1.5 rounded-full text-sm whitespace-nowrap shadow-sm ${
              selectedType === null
                ? "bg-[#009639] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setSelectedType(null)}
          >
            All Types
          </button>
          {contractTypes.map((type) => (
            <button
              key={type.id}
              className={`flex items-center px-3 py-1.5 rounded-full text-sm whitespace-nowrap shadow-sm ${
                selectedType === type.id
                  ? "bg-[#009639] text-white"
                  : "bg-[#f2f2f2] text-[#333333]"
              }`}
              onClick={() => setSelectedType(type.id)}
            >
              <span
                className={`${
                  selectedType === type.id ? "text-white" : "text-[#009639]"
                } mr-1`}
              >
                {type.icon}
              </span>
              <span>{type.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow-sm mb-4">
        <div className="flex px-2">
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "active" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("active")}
          >
            Active
            {activeTab === "active" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "expired" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("expired")}
          >
            Expired
            {activeTab === "expired" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
        </div>
      </div>

      {/* Contracts List */}
      <div className="p-4">
        {filteredContracts.length > 0 ? (
          <div className="space-y-3">
            {filteredContracts.map((contract) => (
              <div
                key={contract.id}
                className="bg-white rounded-xl shadow-md p-4 border border-gray-100"
                onClick={() => router.push(`/contract-details/${contract.id}`)}
              >
                <div className="flex items-start">
                  <div className="w-10 h-10 bg-white rounded-full border border-[#f2f2f2] overflow-hidden flex items-center justify-center mr-3 flex-shrink-0">
                    <Image
                      src={contract.company.logo}
                      alt={contract.company.name}
                      width={40}
                      height={40}
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="text-[#333333] font-medium">
                        {contract.title}
                      </h3>
                      {getStatusBadge(contract.status)}
                    </div>
                    <p className="text-xs text-[#797879] mt-1">
                      {contract.company.name} • {contract.vehicle.name}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center">
                        <Calendar size={14} className="text-[#797879] mr-1" />
                        <span className="text-xs text-[#797879]">
                          {formatDate(contract.startDate)} -{" "}
                          {formatDate(contract.endDate)}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between mt-2">
                      <div className="flex items-center">
                        <DollarSign size={14} className="text-[#009639] mr-1" />
                        <span className="text-sm font-medium text-[#333333]">
                          {formatCurrency(contract.revenue.current)}
                          <span className="text-xs text-[#797879] ml-1">
                            earned
                          </span>
                        </span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-[#333333]">
                          {formatCurrency(contract.revenue.projected)}
                          <span className="text-xs text-[#797879] ml-1">
                            projected
                          </span>
                        </span>
                      </div>
                    </div>
                    {contract.nextPayment && (
                      <div className="mt-2 p-2 bg-[#e6ffe6] rounded-lg flex items-center justify-between shadow-sm">
                        <div className="flex items-center">
                          <Clock size={14} className="text-[#009639] mr-1" />
                          <span className="text-xs text-[#007A2F] font-medium">
                            Next Payment:{" "}
                            {formatDate(contract.nextPayment.date)}
                          </span>
                        </div>
                        <span className="text-sm font-medium text-[#009639]">
                          {formatCurrency(contract.nextPayment.amount)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <div className="w-16 h-16 bg-[#e6ffe6] rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
              <FileText size={32} className="text-[#009639]" />
            </div>
            <p className="text-[#333333] font-medium text-lg">
              No {activeTab} contracts
            </p>
            <p className="text-[#797879] text-sm mt-2">
              {activeTab === "active"
                ? "You don't have any active contracts"
                : "Expired contracts will appear here"}
            </p>
            {activeTab === "active" && (
              <button
                className="w-full py-3.5 mt-6 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full font-semibold shadow-md"
                onClick={() => router.push("/opportunities")}
              >
                Find Opportunities
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
