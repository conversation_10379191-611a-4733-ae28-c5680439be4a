"use server";
import { getVehicle } from "@/actions/vehicles";
import {
  VehicleReadWithModelAndParty,
  SimplifiedVehicleData,
} from "@/types/vehicles";
import {
  CategorizedMaintenance,
  MaintenanceItem,
  VehicleMaintenanceRead,
  VehicleServiceStatus,
} from "@/types/maintanance";
import { formatDateForInput } from "@/lib/utils";
import MaintenanceDetailsScreen from "./maintenace-details";
import { Amplify } from "aws-amplify";
import outputs from "@/amplify_outputs.json";

Amplify.configure(outputs);

export default async function MaintenanceDetails({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const vehicle = await getVehicle(+id);
  async function transformVehicleData(
    data: VehicleReadWithModelAndParty
  ): Promise<SimplifiedVehicleData> {
    const now = new Date();

    const maintenanceItems = data.maintenance_items ?? [];

    let lastServiceItem: VehicleMaintenanceRead | undefined;
    let nextServiceItem: VehicleMaintenanceRead | undefined;

    for (const item of maintenanceItems) {
      if (item.is_scheduled) {
        if (
          item.status === VehicleServiceStatus.COMPLETED &&
          item.completed_date &&
          (!lastServiceItem ||
            new Date(item.completed_date) >
              new Date(lastServiceItem.completed_date!))
        ) {
          lastServiceItem = item;
        }

        if (
          item.status === VehicleServiceStatus.SCHEDULED &&
          new Date(item.due_date) > now &&
          (!nextServiceItem ||
            new Date(item.due_date) < new Date(nextServiceItem.due_date))
        ) {
          nextServiceItem = item;
        }
      }
    }

    const lastService = lastServiceItem?.completed_date
      ? formatDateForInput(lastServiceItem.completed_date)
      : "—";

    const nextService = nextServiceItem?.due_date
      ? formatDateForInput(nextServiceItem.due_date)
      : "—";

    const odometer =
      lastServiceItem?.completed_odometer?.toLocaleString() ?? "—";

    return {
      id: data.id,
      name: `${data.model?.make?.name} ${data.model.model}`,
      year: data.manufacturing_year,
      image: data.media[0].media_path || undefined,
      odometer,
      lastService,
      nextService,
    };
  }

  const vehicleData = await transformVehicleData(vehicle);
  function mapStatus(
    status: VehicleServiceStatus
  ): "scheduled" | "pending" | "completed" {
    switch (status) {
      case VehicleServiceStatus.SCHEDULED:
        return "scheduled";
      case VehicleServiceStatus.PENDING:
        return "pending";
      case VehicleServiceStatus.COMPLETED:
        return "completed";
      default:
        return "pending";
    }
  }
  function formatCost(cost: number): string {
    return `R${cost.toLocaleString(undefined, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    })}`;
  }

  function convertAndCategorizeMaintenance(
    maintenanceItems: VehicleMaintenanceRead[]
  ): CategorizedMaintenance {
    const upcomingMaintenance: MaintenanceItem[] = [];
    const maintenanceHistory: MaintenanceItem[] = [];

    for (const item of maintenanceItems) {
      const converted: MaintenanceItem = {
        id: item.id,
        type: item.name,
        dueDate: formatDateForInput(item.due_date),
        dueKm: `${item.due_odometer.toLocaleString()} km`,
        description: item.description ?? "",
        estimatedCost: formatCost(item.expected_cost),
        status: mapStatus(item.status),
        provider: item.service_provider ?? "Unknown Provider",
        odometer:
          item.due_odometer?.toString() ||
          item.completed_odometer?.toString() ||
          "-",
      };

      if (item.status === VehicleServiceStatus.COMPLETED) {
        maintenanceHistory.push(converted);
      } else {
        upcomingMaintenance.push(converted);
      }
    }

    return { upcomingMaintenance, maintenanceHistory };
  }

  const { upcomingMaintenance, maintenanceHistory } =
    convertAndCategorizeMaintenance(vehicle.maintenance_items);

  return (
    <MaintenanceDetailsScreen
      upcomingMaintenance={upcomingMaintenance}
      maintenanceHistory={maintenanceHistory}
      vehicle={vehicleData}
    />
  );
}
