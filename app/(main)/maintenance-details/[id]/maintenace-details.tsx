"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Calendar,
  AlertTriangle,
  Car,
  FileText,
} from "lucide-react";
import PageWithBottomNav from "../../../../components/PageWithBottomNav";
import { formatDateForInput } from "@/lib/utils";
import { SimplifiedVehicleData } from "@/types/vehicles";
import { MaintenanceItem } from "@/types/maintanance";
import { getUrl } from "aws-amplify/storage";

export default function MaintenanceDetailsScreen({
  upcomingMaintenance,
  maintenanceHistory,
  vehicle,
}: {
  upcomingMaintenance: MaintenanceItem[];
  maintenanceHistory: MaintenanceItem[];
  vehicle: SimplifiedVehicleData;
}): React.ReactNode {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("upcoming");
  const [imageUrl, setImageUrl] = useState<string>();

  useEffect(() => {
    async function loadImages() {
      if (!vehicle.image) return;
      if (vehicle.image) {
        const result = await getUrl({ path: vehicle.image });
        const url = result.url.toString();
        setImageUrl(url);
      }
    }

    loadImages();
  }, [vehicle]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "scheduled":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#009639] shadow-sm">
            Scheduled
          </span>
        );
      case "pending":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#FFD700] text-[#333333] shadow-sm">
            Pending
          </span>
        );
      case "completed":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#009639] shadow-sm">
            Completed
          </span>
        );
      case "overdue":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#ffe6e6] text-[#d32f2f] shadow-sm">
            Overdue
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <PageWithBottomNav>
      <div className="min-h-screen bg-[#f5f5f5]">
        {/* Header */}
        <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">
            Maintenance Schedule Details
          </h1>
        </div>

        {/* Vehicle Info */}
        <div className="bg-white px-6 py-4 flex items-center border-b border-[#f2f2f2]">
          <div className="w-16 h-12 bg-[#f2f2f2] rounded-lg overflow-hidden mr-3">
            <Image
              src={imageUrl || "/placeholder.svg"}
              alt={vehicle.name}
              width={64}
              height={48}
              className="object-cover"
            />
          </div>
          <div>
            <h2 className="text-[#333333] font-medium">
              {vehicle.name} ({vehicle.year})
            </h2>
            <p className="text-xs text-[#797879]">
              Odometer: {vehicle.odometer}
            </p>
          </div>
        </div>

        {/* Service Summary */}
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">Service Summary</h3>

            <div className="space-y-3">
              <div className="flex items-start">
                <Calendar size={18} className="text-[#009639] mr-3 mt-0.5" />
                <div>
                  <p className="text-[#797879] text-xs">Last Service</p>
                  <p className="text-[#333333]">
                    {formatDateForInput(vehicle.lastService)}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <Calendar size={18} className="text-[#009639] mr-3 mt-0.5" />
                <div>
                  <p className="text-[#797879] text-xs">Next Service</p>
                  <p className="text-[#333333]">
                    {formatDateForInput(vehicle.nextService)}
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-[#e6ffe6] rounded-lg flex items-start">
              <AlertTriangle
                size={18}
                className="text-[#009639] mr-2 flex-shrink-0 mt-0.5"
              />
              <p className="text-sm text-[#333333]">
                Regular maintenance is required to keep the vehicle in good
                condition and maintain warranty coverage.
              </p>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white border-b border-[#f2f2f2]">
          <div className="flex mx-4">
            <button
              className={`flex-1 py-3 text-sm font-medium ${
                activeTab === "upcoming"
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-[#797879]"
              }`}
              onClick={() => setActiveTab("upcoming")}
            >
              Upcoming
            </button>
            <button
              className={`flex-1 py-3 text-sm font-medium ${
                activeTab === "history"
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-[#797879]"
              }`}
              onClick={() => setActiveTab("history")}
            >
              History
            </button>
          </div>
        </div>

        {/* Upcoming Maintenance Tab */}
        {activeTab === "upcoming" && (
          <div className="p-4">
            {upcomingMaintenance.map((item) => (
              <div
                key={item.id}
                className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100"
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-[#333333] font-medium">{item.type}</h3>
                  {getStatusBadge(item.status)}
                </div>

                <div className="space-y-2 mb-3">
                  <div className="flex items-center text-sm">
                    <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2 shadow-sm">
                      <Calendar size={12} className="text-[#009639]" />
                    </div>
                    <span className="text-[#333333]">Due: {item.dueDate}</span>
                  </div>

                  <div className="flex items-center text-sm">
                    <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2 shadow-sm">
                      <Car size={12} className="text-[#009639]" />
                    </div>
                    <span className="text-[#333333]">or at {item.dueKm}</span>
                  </div>
                </div>

                <p className="text-sm text-[#797879] mb-3">
                  {item.description}
                </p>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-[#333333]">
                    Est. Cost: {item.estimatedCost}
                  </span>
                </div>
              </div>
            ))}

            <button
              className="w-full py-3 bg-[#009639] text-white rounded-full text-xl font-semibold "
              onClick={() => router.push(`/schedule-maintenance/${vehicle.id}`)}
            >
              Schedule New Maintenance
            </button>
          </div>
        )}

        {/* Maintenance History Tab */}
        {activeTab === "history" && (
          <div className="p-4">
            {maintenanceHistory.map((item) => (
              <div
                key={item.id}
                className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100"
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-[#333333] font-medium">{item.type}</h3>
                  <span className="text-xs text-[#797879]">{item.dueDate}</span>
                </div>

                <div className="flex items-center text-sm mb-3">
                  <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2 shadow-sm">
                    <Car size={12} className="text-[#009639]" />
                  </div>
                  <span className="text-[#333333]">
                    Odometer: {item.odometer}
                  </span>
                </div>

                <p className="text-sm text-[#797879] mb-3">
                  {item.description}
                </p>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-[#333333]">
                    Cost: {item.estimatedCost}
                  </span>
                </div>
              </div>
            ))}

            <div className="bg-white rounded-xl shadow-md p-4 flex items-center justify-center border border-gray-100">
              <button
                className="text-[#009639] text-sm font-medium flex items-center"
                onClick={() => router.push(`/maintenance-schedule`)}
              >
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2 shadow-sm">
                  <FileText size={12} className="text-[#009639]" />
                </div>
                View All Maintenance Records
              </button>
            </div>
          </div>
        )}
      </div>
    </PageWithBottomNav>
  );
}
