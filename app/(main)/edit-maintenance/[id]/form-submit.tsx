"use client";

import { useFormStatus } from "react-dom";

export default function FormSubmit() {
  const status = useFormStatus();
  if (status.pending) {
    return (
      <>
        <button
          type="submit"
          className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-4 rounded-full text-xl font-semibold mt-8 shadow-md flex justify-center items-center"
        >
          <div className="border-t-4 border-b-4 border-white w-6 h-6 border-solid rounded-full animate-spin"></div>
        </button>
      </>
    );
  }

  return (
    <>
      <button
        type="submit"
        className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-4 rounded-full text-xl font-semibold mt-8 shadow-md"
      >
        Save
      </button>
    </>
  );
}
