"use client";

import { useState, use } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  DollarSign,
  BarChart3,
  Calendar,
  ChevronRight,
  FileText,
  Users,
  TrendingUp,
  Filter,
} from "lucide-react";

export default function IncomeTrackingScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const groupId = use(params).id;
  const [activeTab, setActiveTab] = useState("summary");
  const [selectedPeriod, setSelectedPeriod] = useState("month");

  // Mock data - in a real app, this would come from an API
  const incomeData = {
    summary: {
      totalRevenue: 24500,
      previousPeriod: 21000,
      percentageChange: 16.7,
      breakdown: [
        { category: "Ride Sharing", amount: 18000, percentage: 73 },
        { category: "Corporate Rental", amount: 4500, percentage: 18 },
        { category: "Events", amount: 2000, percentage: 9 },
      ],
    },
    distribution: [
      { member: "<PERSON>", percentage: 33, amount: 8085 },
      { member: "<PERSON><PERSON><PERSON>", percentage: 33, amount: 8085 },
      { member: "Thandi Dlamini", percentage: 34, amount: 8330 },
    ],
    history: [
      {
        id: 1,
        date: "May 20, 2023",
        amount: 3500,
        source: "Ride Sharing - Cape Town",
        status: "Paid",
      },
      {
        id: 2,
        date: "May 15, 2023",
        amount: 4500,
        source: "Corporate Rental - Johannesburg",
        status: "Paid",
      },
      {
        id: 3,
        date: "May 10, 2023",
        amount: 2000,
        source: "Events - Durban",
        status: "Paid",
      },
      {
        id: 4,
        date: "May 5, 2023",
        amount: 3500,
        source: "Ride Sharing - Pretoria",
        status: "Paid",
      },
      {
        id: 5,
        date: "May 1, 2023",
        amount: 11000,
        source: "Monthly Total - South Africa",
        status: "Paid",
      },
    ],
    schedule: [
      {
        id: 1,
        date: "June 1, 2023",
        amount: 8000,
        description: "Monthly profit distribution - South Africa",
      },
      {
        id: 2,
        date: "July 1, 2023",
        amount: null,
        description: "Monthly profit distribution - South Africa",
      },
    ],
    tax: {
      yearToDate: 24500,
      taxRate: 28,
      estimatedTax: 6860,
    },
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Income Tracking</h1>
      </div>

      {/* Period Selector */}
      <div className="bg-white px-4 py-3 flex justify-between border-b border-[#f2f2f2]">
        <div className="flex space-x-2">
          {["week", "month", "quarter", "year"].map((period) => (
            <button
              key={period}
              className={`px-3 py-1 rounded-full text-sm ${
                selectedPeriod === period
                  ? "bg-[#009639] text-white"
                  : "bg-[#f2f2f2] text-[#333333]"
              }`}
              onClick={() => setSelectedPeriod(period)}
            >
              {period.charAt(0).toUpperCase() + period.slice(1)}
            </button>
          ))}
        </div>
        <button className="p-1">
          <Filter size={20} className="text-[#009639]" />
        </button>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow-sm mb-4">
        <div className="flex">
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "summary" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("summary")}
          >
            Summary
            {activeTab === "summary" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "distribution" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("distribution")}
          >
            Distribution
            {activeTab === "distribution" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "history" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("history")}
          >
            History
            {activeTab === "history" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
        </div>
      </div>

      {activeTab === "summary" && (
        <div className="p-4">
          {/* Revenue Summary */}
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <DollarSign size={24} className="text-[#009639] mr-2" />
                <h3 className="text-[#333333] font-medium">Revenue Summary</h3>
              </div>
              <span className="text-sm text-[#797879]">
                {selectedPeriod === "month"
                  ? "May 2023"
                  : selectedPeriod === "week"
                  ? "May 15-21, 2023"
                  : "Q2 2023"}
              </span>
            </div>

            <div className="text-center mb-4">
              <p className="text-3xl font-bold text-[#333333]">
                R{incomeData.summary.totalRevenue}
              </p>
              <div className="flex items-center justify-center mt-1">
                <TrendingUp size={16} className="text-[#009639] mr-1" />
                <p className="text-sm text-[#009639]">
                  {incomeData.summary.percentageChange}% from previous{" "}
                  {selectedPeriod}
                </p>
              </div>
            </div>

            <div className="h-40 bg-[#f9f9f9] rounded-lg mb-4 flex items-center justify-center">
              <p className="text-[#797879]">Revenue chart would go here</p>
            </div>
          </div>

          {/* Earnings Breakdown */}
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center mb-4">
              <BarChart3 size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">Earnings Breakdown</h3>
            </div>

            <div className="space-y-3">
              {incomeData.summary.breakdown.map((item, index) => (
                <div key={index}>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-[#333333]">{item.category}</span>
                    <span className="text-[#333333] font-medium">
                      R{item.amount}
                    </span>
                  </div>
                  <div className="h-2 bg-[#f2f2f2] rounded-full overflow-hidden">
                    <div
                      className="h-full rounded-full"
                      style={{
                        width: `${item.percentage}%`,
                        backgroundColor:
                          index === 0
                            ? "#009639"
                            : index === 1
                            ? "#00b347"
                            : "#00d154",
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Tax Information */}
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <FileText size={20} className="text-[#009639] mr-2" />
                <h3 className="text-[#333333] font-medium">Tax Information</h3>
              </div>
              <button
                className="text-[#009639] text-sm flex items-center"
                onClick={() => router.push(`/tax-details/${groupId}`)}
              >
                Details <ChevronRight size={16} />
              </button>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-[#797879]">Year-to-Date Income</span>
                <span className="text-[#333333] font-medium">
                  R{incomeData.tax.yearToDate}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-[#797879]">Tax Rate</span>
                <span className="text-[#333333] font-medium">
                  {incomeData.tax.taxRate}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-[#797879]">Estimated Tax</span>
                <span className="text-[#333333] font-medium">
                  R{incomeData.tax.estimatedTax}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === "distribution" && (
        <div className="p-4">
          {/* Profit Distribution */}
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center mb-4">
              <Users size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                Profit Distribution
              </h3>
            </div>

            <div className="h-4 bg-[#f2f2f2] rounded-full overflow-hidden flex mb-3">
              {incomeData.distribution.map((member, index) => (
                <div
                  key={index}
                  className="h-full"
                  style={{
                    width: `${member.percentage}%`,
                    backgroundColor:
                      index === 0
                        ? "#009639"
                        : index === 1
                        ? "#00b347"
                        : "#00d154",
                  }}
                ></div>
              ))}
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              {incomeData.distribution.map((member, index) => (
                <div key={index} className="flex items-center text-xs">
                  <div
                    className="w-3 h-3 rounded-full mr-1"
                    style={{
                      backgroundColor:
                        index === 0
                          ? "#009639"
                          : index === 1
                          ? "#00b347"
                          : "#00d154",
                    }}
                  ></div>
                  <span>
                    {member.member.split(" ")[0]} ({member.percentage}%)
                  </span>
                </div>
              ))}
            </div>

            <div className="space-y-3">
              {incomeData.distribution.map((member, index) => (
                <div
                  key={index}
                  className="p-3 bg-[#f9f9f9] rounded-lg flex justify-between items-center"
                >
                  <div>
                    <p className="text-[#333333] font-medium">
                      {member.member}
                    </p>
                    <p className="text-xs text-[#797879]">
                      {member.percentage}% ownership
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-[#333333] font-bold">R{member.amount}</p>
                    <p className="text-xs text-[#797879]">
                      This {selectedPeriod}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Payment Schedule */}
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center mb-4">
              <Calendar size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">Payment Schedule</h3>
            </div>

            <div className="space-y-3">
              {incomeData.schedule.map((payment) => (
                <div
                  key={payment.id}
                  className="p-3 bg-[#f9f9f9] rounded-lg flex justify-between items-center"
                >
                  <div>
                    <p className="text-[#333333] font-medium">{payment.date}</p>
                    <p className="text-xs text-[#797879]">
                      {payment.description}
                    </p>
                  </div>
                  <div className="text-right">
                    {payment.amount ? (
                      <p className="text-[#333333] font-bold">
                        R{payment.amount}
                      </p>
                    ) : (
                      <p className="text-[#797879]">TBD</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === "history" && (
        <div className="p-4">
          {/* Earnings History */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
            {incomeData.history.map((transaction, index) => (
              <div
                key={transaction.id}
                className={`p-4 flex items-center justify-between ${
                  index < incomeData.history.length - 1
                    ? "border-b border-[#f2f2f2]"
                    : ""
                }`}
              >
                <div>
                  <p className="text-[#333333] font-medium">
                    R{transaction.amount}
                  </p>
                  <p className="text-xs text-[#797879]">{transaction.source}</p>
                  <p className="text-xs text-[#797879]">{transaction.date}</p>
                </div>
                <div className="text-right">
                  <span className="px-2 py-1 bg-[#e6ffe6] text-[#007A2F] rounded-full text-xs font-medium">
                    {transaction.status}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Download Report */}
          <button
            className="w-full border border-[#009639] text-[#009639] py-3 rounded-full text-lg font-medium mb-4 shadow-sm"
            onClick={() => console.log("Download earnings report")}
          >
            Download Earnings Report
          </button>
        </div>
      )}
    </div>
  );
}
