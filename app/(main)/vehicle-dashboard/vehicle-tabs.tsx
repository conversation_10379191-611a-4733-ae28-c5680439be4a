"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  Calendar,
  Search,
  Filter,
  ChevronRight,
  Clock,
  Wrench,
  RefreshCw,
} from "lucide-react";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import type { CompanyOwnershipReadWithRelations } from "@/types/company-ownerships";
import { useVehicleMediaUrls } from "@/hooks/use-vehicle-images-urls";
import { useVehicleMaintenance } from "@/hooks/use-vehicle-maintenance";
import DocPostForm from "./post-form-doc";
import ImagePostForm from "./post-form-image";
import {
  getStatusColor,
  getStatusLabel,
  formatDateForInput,
} from "@/lib/utils";
import { addVehicleDocument } from "@/actions/vehicle-documents";
import { addVehicleMedia } from "@/actions/vehicle-media";
import { VehicleMaintenanceRead } from "@/types/maintanance";
interface VehicleMaintenanceReadWithVehicleName extends VehicleMaintenanceRead {
  vehicleName: string;
}

type VehicleDashboardTabsProps = {
  vehicles: VehicleReadWithModelAndParty[];
  ownerships: CompanyOwnershipReadWithRelations[];
};

export default function VehicleDashboardTabs({
  vehicles,
  ownerships,
}: VehicleDashboardTabsProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("owned");
  const imageUrls = useVehicleMediaUrls(vehicles);
  const [filteredItems, setFilteredItems] = useState<
    VehicleMaintenanceReadWithVehicleName[]
  >([]);
  const maintenanceItems = useVehicleMaintenance(vehicles);

  useEffect(() => {
    const filtered = maintenanceItems.filter(
      (item) => item.status === "Scheduled" || item.status === "Pending"
    );
    setFilteredItems(filtered);
  }, [maintenanceItems]);

  const getOwnership = (
    vehicle: VehicleReadWithModelAndParty,
    ownerships: CompanyOwnershipReadWithRelations[]
  ) => {
    const ownership = ownerships.find(
      (o) => o.company?.party_id === vehicle.party_id
    );
    return ownership?.fraction ?? null;
  };

  const filteredVehicles = vehicles.filter((vehicle) =>
    vehicle.model.model.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen">
      {/* Search and Filter */}
      <div className="p-4">
        <div className="flex space-x-2">
          <div className="flex-1 bg-white rounded-full px-4 py-2 flex items-center shadow-md">
            <Search size={18} className="text-[#797879] mr-2" />
            <input
              type="text"
              placeholder="Search vehicles by model"
              className="flex-1 bg-transparent focus:outline-none text-[#333333] text-sm"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {/* <button className="bg-white p-2 rounded-full shadow-sm">
            <Filter size={18} className="text-[#333333]" />
          </button> */}
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4">
        <div className="flex border-b border-[#f2f2f2] mb-4">
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "owned"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("owned")}
          >
            Vehicles
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "maintenance"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("maintenance")}
          >
            Maintenance
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "documents"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("documents")}
          >
            Docs
          </button>
          <button
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === "images"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("images")}
          >
            Images
          </button>
        </div>
      </div>

      {/* Vehicle List */}
      {activeTab === "owned" && (
        <div className="px-4 pb-8">
          <div className="space-y-4">
            {filteredVehicles.map((vehicle) => (
              <div
                key={vehicle.id}
                className="ride-card overflow-hidden drop-shadow-sm rounded-xl border border-gray-100"
              >
                <div className="h-40 bg-[#f2f2f2] relative">
                  <Image
                    src={imageUrls[vehicle.id]?.[0] || "/placeholder.svg"}
                    alt={vehicle.model?.model || "Vehicle image"}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute bottom-3 left-3 flex space-x-2">
                    <div
                      className={`px-3 py-1 rounded-full text-xs font-medium shadow-sm ${getStatusColor(
                        vehicle?.is_active ? "available" : ""
                      )}`}
                    >
                      {getStatusLabel(vehicle?.is_active ? "available" : "")}
                    </div>
                    <div className="bg-white px-3 py-1 rounded-full text-xs font-medium text-[#333333] shadow-sm">
                      {getOwnership(vehicle, ownerships)}% Ownership
                    </div>
                  </div>
                  <button
                    className="absolute top-3 right-3 bg-white p-2 rounded-full shadow-sm"
                    onClick={() => router.push(`/vehicle-status/${vehicle.id}`)}
                  >
                    <ChevronRight size={18} className="text-[#009639]" />
                  </button>
                </div>

                <div className="p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-[#333333] font-semibold">
                      {vehicle?.model?.make?.name} {vehicle.model?.model}
                    </h3>
                    <span className="text-xs text-[#797879]">
                      {vehicle?.country_of_registration}
                    </span>
                  </div>

                  <div className="flex items-center mb-3">
                    <Calendar size={16} className="text-[#009639] mr-1" />
                    <span className="text-sm text-[#333333]">
                      <span className="font-medium">
                        {/*bookings.daysRemaining ||*/ 0}
                      </span>{" "}
                      days remaining this month
                    </span>
                  </div>

                  <div className="flex justify-start gap-4 pl-1">
                    <button
                      className="bg-[#009639] text-white py-1 px-4 rounded-full text-sm font-medium flex items-center h-9 shadow-sm"
                      onClick={() =>
                        router.push(`/booking-calendar/${vehicle.id}`)
                      }
                    >
                      <Calendar size={18} className="mr-2" />
                      <span>Book</span>
                    </button>
                    <button
                      className="bg-[#e6ffe6] text-[#009639] py-1 px-4 rounded-full text-sm font-medium flex items-center h-9 shadow-sm"
                      onClick={() =>
                        router.push(`/vehicle-handover/${vehicle.id}`)
                      }
                    >
                      <RefreshCw size={18} className="mr-2" />
                      <span>Handover</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Maintenance Tab */}
      {activeTab === "maintenance" && (
        <div className="px-4 pb-8">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-[#333333] font-medium">Upcoming Maintenance</h3>
            <button
              className="text-[#009639] text-sm font-medium hover:underline"
              onClick={() => router.push("/maintenance-schedule")}
            >
              View All
            </button>
          </div>

          <div className="space-y-3">
            {filteredItems.map((item) => (
              <div
                key={item.id}
                className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
              >
                <div className="flex items-start">
                  <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                    <Wrench size={18} className="text-[#009639]" />
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3>{item.vehicleName}</h3>
                        <h5>{item.name}</h5>
                        <p className="text-xs text-[#797879]">
                          {item.description}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 ml-auto">
                        <span
                          className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(item.status)}`}
                        >
                          {getStatusLabel(item.status)}
                        </span>
                        <a
                          href={`/edit-maintenance/${item.id}`}
                          className="text-[#009639] hover:text-[#00732e] transition-colors"
                        >
                          <button className="p-1 rounded-md hover:bg-[#e6ffe6] transition-colors">
                            <svg
                              className="w-5 h-5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                              />
                            </svg>
                          </button>
                        </a>
                        <button
                          className="text-[#009639] text-sm font-medium flex items-center rounded-full px-3 py-1"
                          onClick={() =>
                            router.push(`/maintenance-details/${item.id}`)
                          }
                        >
                          <ChevronRight size={18} />
                        </button>
                      </div>
                    </div>
                    <div className="flex items-center mt-2">
                      <Clock size={14} className="text-[#797879] mr-1" />
                      <span className="text-xs text-[#797879]">
                        {formatDateForInput(item.due_date)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      {activeTab === "documents" && (
        <DocPostForm action={addVehicleDocument} vehicles={vehicles} />
      )}
      {activeTab === "images" && (
        <ImagePostForm action={addVehicleMedia} vehicles={vehicles} />
      )}
    </div>
  );
}
