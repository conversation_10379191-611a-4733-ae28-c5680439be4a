"use client";

import { DocumentDelete, DocumentUpload } from "@/lib/utils";
import { FileText, Upload, X } from "lucide-react";
import {
  startTransition,
  useActionState,
  useRef,
  useState,
  useEffect,
} from "react";
import FormSubmit from "./form-submit";
import { DocumentType } from "@/types/vehicles";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import VehicleDocumentList from "./vehicle-documents-list";

const DocumentTypeLabels: Record<DocumentType, string> = {
  [DocumentType.REGISTRATION]: "Registration",
  [DocumentType.INSURANCE]: "Insurance",
  [DocumentType.INSPECTION]: "Inspection",
  [DocumentType.OTHER]: "Other",
};

type PostFormProps = {
  vehicles: VehicleReadWithModelAndParty[];
  action: (prevState: any, formData: FormData) => any;
};

export default function PostForm({ action, vehicles }: PostFormProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [documents, setDocuments] = useState<File[]>([]);
  const [state, formAction] = useActionState(action, {});
  const [documentUrl, setDocumentUrl] = useState("");
  const [error, setError] = useState("");

  const handleDocumentUpload = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];

    if (file) {
      try {
        const upf = await DocumentUpload(file, "vehicle");

        if (upf && upf.path) {
          setDocumentUrl(upf.path);
          setDocuments([file]);
        }
      } catch (err: any) {
        console.error(err);
        if (err && err.message) {
          setError(err.message);
        } else {
          setError("An error occurred. Please try again.");
        }
      }
    }
  };

  useEffect(() => {
    if (state?.success) {
      setDocuments([]);
      setDocumentUrl("");
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  }, [state]);

  async function handleRemoveDocument(index: number, path: string) {
    try {
      await DocumentDelete(path);
    } catch (error) {
      console.error("Error deleting file from S3:", error);
    }
    setDocuments((prev) => prev.filter((_, i) => i !== index));
  }
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    if (documentUrl) formData.set("documentUrl", documentUrl);

    startTransition(() => {
      formAction(formData);
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-w-2xl mx-auto pt-6">
      {state?.errors && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 text-sm">
          <ul>
            {state.errors.map((error: string, index: number) => (
              <li key={index} className="list-disc list-inside">
                {error}
              </li>
            ))}
          </ul>
        </div>
      )}
      {state?.message && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 text-sm">
          {state?.message}
        </div>
      )}
      <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-[#333333] font-medium">Documents</h3>
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
            onChange={handleDocumentUpload}
          />
        </div>

        {documents.length > 0 ? (
          <ul className="space-y-4">
            {documents.map((file, index) => (
              <li
                key={index}
                className="bg-[#f9f9f9] p-4 rounded-xl border border-[#e5e7eb] shadow-sm"
              >
                <div className="flex items-start justify-between gap-4 mb-3">
                  {/* File icon + file name (take more space) */}
                  <div className="flex flex-1 items-center gap-2 min-w-0">
                    <FileText size={20} className="text-[#009639] shrink-0" />
                    <span className="text-sm text-[#333333] font-medium break-all truncate">
                      {file.name}
                    </span>
                  </div>
                  <div className="flex items-start justify-between gap-4 mb-3">
                    {/* Select input (takes less space) */}
                    <select
                      name="documentType"
                      required
                      className="w-40 px-1 py-2.5 rounded-lg border border-[#d6d9dd] focus:outline-none focus:ring-2 focus:ring-[#009639] bg-white"
                    >
                      <option value="">Document Type</option>
                      {Object.values(DocumentType).map((type) => (
                        <option key={type} value={type}>
                          {DocumentTypeLabels[type]}
                        </option>
                      ))}
                    </select>

                    <select
                      name="vehicleID"
                      id="vehicleID"
                      required
                      className="w-full px-4 py-2.5 rounded-lg border border-[#d6d9dd] focus:outline-none focus:ring-2 focus:ring-[#009639] bg-white mb-6"
                    >
                      <option value="">Choose a Vehicle</option>
                      {vehicles?.map((vehicle) => (
                        <option key={vehicle.id} value={vehicle.id}>
                          {vehicle?.model?.make?.name} {vehicle.model.model}{" "}
                          (ID: {vehicle.id})
                        </option>
                      ))}
                    </select>
                  </div>
                  {/* Delete button */}
                  <button
                    type="button"
                    className="hover:bg-red-50 rounded-full p-1 transition shrink-0"
                    onClick={async () => {
                      await handleRemoveDocument(index, documentUrl);
                    }}
                  >
                    <X size={14} className="text-red-500" />
                  </button>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div
            className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload size={32} className="text-[#009639] mb-2" />
            <p className="text-[#797879] text-center">
              Upload your vehicle registration, insurance, and other important
              documents
            </p>
          </div>
        )}
      </div>
      <FormSubmit />
      <VehicleDocumentList vehicles={vehicles} />
    </form>
  );
}
