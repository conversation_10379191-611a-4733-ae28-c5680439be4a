"use client";
import React, { useEffect, useState } from "react";

import { VehicleDocumentRead } from "@/types/vehicles";
import { ChevronRight, FileText, Calendar, X, Loader } from "lucide-react";
import { formatDateForInput } from "@/lib/utils";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import { getUrl } from "aws-amplify/storage";
import { DocumentDelete } from "@/lib/utils";
import { deleteVehicleDocument } from "@/actions/vehicle-documents";

interface VehicleDocumentWithUrl extends VehicleDocumentRead {
  signedUrl?: string;
}

export default function VehicleDocumentList({
  vehicles,
}: {
  vehicles: VehicleReadWithModelAndParty[];
}) {
  const [vehicleDocuments, setVehicleDocuments] = useState<
    VehicleDocumentWithUrl[]
  >([]);
  const [deletingId, setDeletingId] = useState<number | null>(null);

  const getDocumentStatusBadge = (status: string) => {
    switch (status) {
      case "valid":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-800">
            Valid
          </span>
        );
      case "expiring":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-800">
            Expiring Soon
          </span>
        );
      case "expired":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-red-100 text-red-800">
            Expired
          </span>
        );
      default:
        return null;
    }
  };

  const getVehicleName = (vehicleId: number) => {
    const vehicle = vehicles.find((v) => v.id === vehicleId);
    return vehicle
      ? `${vehicle.model.make?.name} ${vehicle.model.model} (${vehicle.vehicle_registration})`
      : "Unknown Vehicle";
  };

  useEffect(() => {
    const fetchSignedUrls = async () => {
      const media = vehicles.flatMap((vehicle) => vehicle.vehicle_documents);
      const withUrls: VehicleDocumentWithUrl[] = await Promise.all(
        media.map(async (m) => {
          try {
            const urlResult = await getUrl({ path: m.media_path });
            return { ...m, signedUrl: urlResult.url.toString() };
          } catch (err) {
            console.error(`Failed to get signed URL for ${m.media_path}`, err);
            return { ...m, signedUrl: undefined };
          }
        })
      );
      setVehicleDocuments(withUrls);
    };

    fetchSignedUrls();
  }, [vehicles]);

  async function handleDeleteDosument(media: VehicleDocumentWithUrl) {
    const confirmed = window.confirm(
      `Are you sure you want to delete the document "${media.media_path}"?`
    );
    if (!confirmed) return;

    setDeletingId(media.id);
    try {
      await DocumentDelete(media.media_path);
      await deleteVehicleDocument(media.id);
      setVehicleDocuments((docs) => docs.filter((doc) => doc.id !== media.id));
    } catch (error) {
      console.error("Error deleting file from S3:", error);
    } finally {
      setDeletingId(null);
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
      {vehicleDocuments.map((doc) => (
        <div
          className="p-4 border-b border-[#f2f2f2] last:border-b-0"
          key={doc.id}
        >
          <div className="flex items-start">
            <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
              <FileText size={18} className="text-[#009639]" />
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h3 className="text-[#333333] font-medium">
                  {doc.document_type}
                </h3>
                {getDocumentStatusBadge(
                  doc?.expiration_date ? "expiring" : "valid"
                )}
              </div>
              <p className="text-xs text-[#797879] mt-1">
                {getVehicleName(doc.vehicle_id)}
              </p>
              <p className="text-xs text-[#797879] mt-1">{doc.name}</p>
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center">
                  <Calendar size={14} className="text-[#797879] mr-1" />
                  <span className="text-xs text-[#797879]">
                    {formatDateForInput(doc.created_at)}
                  </span>
                </div>
                {doc.expiration_date && (
                  <span className="text-xs text-[#797879]">
                    Expires: {formatDateForInput(doc.expiration_date)}
                  </span>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2 ml-2">
              {doc.signedUrl && (
                <a
                  href={doc.signedUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-[#797879] hover:text-[#333]"
                  aria-label={`Open document ${doc.name}`}
                >
                  <ChevronRight size={18} />
                </a>
              )}
              <button
                onClick={() => handleDeleteDosument(doc)}
                className="p-1 rounded hover:bg-red-100 text-red-600 flex-shrink-0"
                aria-label={`Delete document ${doc.name}`}
                type="button"
                disabled={deletingId === doc.id}
              >
                {deletingId === doc.id ? (
                  <Loader size={18} className="animate-spin" />
                ) : (
                  <X size={18} />
                )}
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
