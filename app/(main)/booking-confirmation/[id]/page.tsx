"use server";

import { getBooking } from "@/actions/bookings";
import { getIndividualByPartyId } from "@/actions/individuals";
import BookingConfirmationScreen from "./booking-confirmation";
import { getVehicle } from "@/actions/vehicles";
import { getContactPointsByPartyId } from "@/actions/contact-points";

export default async function BookingConfirmation({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const booking = await getBooking(+id);
  if (!booking) {
    return <p>Booking not found.</p>;
  }
  const individual = await getIndividualByPartyId(booking.party_id);
  if (!individual) {
    return <p>Individual not found.</p>;
  }
  const vehicle = await getVehicle(booking.vehicle_id);
  if (!vehicle) {
    return <p>Vehicle not found.</p>;
  }

  const contacts = await getContactPointsByPartyId(booking.party_id);
  return (
    <BookingConfirmationScreen
      booking={booking}
      individual={individual}
      vehicle={vehicle}
      contacts={contacts}
    />
  );
}
