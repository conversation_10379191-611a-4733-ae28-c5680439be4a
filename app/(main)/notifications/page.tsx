"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Bell,
  Car,
  Calendar,
  Wallet,
  Users,
  Settings,
  Check,
  ChevronRight,
  Filter,
} from "lucide-react";

export default function NotificationsScreen() {
  const router = useRouter();
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);

  // Mock data - in a real app, this would come from an API
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: "booking",
      title: "Booking Confirmed",
      message: "Your booking for Toyota Fortuner on May 20 has been confirmed.",
      timestamp: "2023-05-15T10:30:00",
      isRead: false,
      actionRequired: false,
      relatedId: "booking1",
    },
    {
      id: 2,
      type: "maintenance",
      title: "Maintenance Reminder",
      message: "Volkswagen Polo is due for maintenance in 3 days.",
      timestamp: "2023-05-14T14:20:00",
      isRead: false,
      actionRequired: true,
      relatedId: "vehicle2",
    },
    {
      id: 3,
      type: "payment",
      title: "Payment Due",
      message: "Your monthly contribution of R1200 is due in 2 days.",
      timestamp: "2023-05-14T09:15:00",
      isRead: true,
      actionRequired: true,
      relatedId: "payment1",
    },
    {
      id: 4,
      type: "group",
      title: "New Group Message",
      message: "Robert Fox: I'll be using the car this weekend.",
      timestamp: "2023-05-13T16:45:00",
      isRead: true,
      actionRequired: false,
      relatedId: "group1",
    },
    {
      id: 5,
      type: "vehicle",
      title: "Vehicle Status Update",
      message: "Toyota Fortuner has been returned by Esther Howard.",
      timestamp: "2023-05-12T11:30:00",
      isRead: true,
      actionRequired: false,
      relatedId: "vehicle3",
    },
    {
      id: 6,
      type: "booking",
      title: "Booking Reminder",
      message: "Your booking for Volkswagen Polo starts tomorrow at 9:00 AM.",
      timestamp: "2023-05-11T08:00:00",
      isRead: true,
      actionRequired: false,
      relatedId: "booking2",
    },
    {
      id: 7,
      type: "maintenance",
      title: "Maintenance Completed",
      message: "Toyota Fortuner maintenance has been completed.",
      timestamp: "2023-05-10T15:20:00",
      isRead: true,
      actionRequired: false,
      relatedId: "vehicle1",
    },
    {
      id: 8,
      type: "payment",
      title: "Payment Received",
      message: "Payment of R1200 has been received from Robert Fox.",
      timestamp: "2023-05-09T10:10:00",
      isRead: true,
      actionRequired: false,
      relatedId: "payment2",
    },
  ]);

  const filterTypes = [
    { id: "booking", label: "Bookings", icon: <Calendar size={16} /> },
    { id: "vehicle", label: "Vehicles", icon: <Car size={16} /> },
    { id: "maintenance", label: "Maintenance", icon: <Settings size={16} /> },
    { id: "payment", label: "Payments", icon: <Wallet size={16} /> },
    { id: "group", label: "Groups", icon: <Users size={16} /> },
  ];

  const filteredNotifications = notifications.filter((notification) => {
    if (showUnreadOnly && notification.isRead) return false;
    if (activeFilter && notification.type !== activeFilter) return false;
    return true;
  });

  const markAsRead = (id: number) => {
    setNotifications(
      notifications.map((notification) =>
        notification.id === id
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(
      notifications.map((notification) => ({ ...notification, isRead: true }))
    );
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "booking":
        return <Calendar size={20} className="text-[#009639]" />;
      case "vehicle":
        return <Car size={20} className="text-[#009639]" />;
      case "maintenance":
        return <Settings size={20} className="text-[#009639]" />;
      case "payment":
        return <Wallet size={20} className="text-[#009639]" />;
      case "group":
        return <Users size={20} className="text-[#009639]" />;
      default:
        return <Bell size={20} className="text-[#009639]" />;
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInDays = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (diffInDays === 0) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else if (diffInDays === 1) {
      return "Yesterday";
    } else if (diffInDays < 7) {
      return date.toLocaleDateString([], { weekday: "long" });
    } else {
      return date.toLocaleDateString([], {
        month: "short",
        day: "numeric",
      });
    }
  };

  const handleNotificationClick = (notification: any) => {
    markAsRead(notification.id);

    // Navigate based on notification type
    switch (notification.type) {
      case "booking":
        router.push(`/booking-details/${notification.relatedId}`);
        break;
      case "vehicle":
      case "maintenance":
        router.push(`/vehicle-status/${notification.relatedId}`);
        break;
      case "payment":
        router.push(`/payment/${notification.relatedId}`);
        break;
      case "group":
        router.push(`/group-chat/${notification.relatedId}`);
        break;
      default:
        break;
    }
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Status Bar */}
      {/* <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
        <div className="font-semibold">9:41</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 20 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1 1H19"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M4 5H16"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <path
                d="M7 9H13"
                stroke="#333333"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </div>
          <div className="h-3 w-4">
            <svg
              viewBox="0 0 16 12"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z"
                fill="#333333"
              />
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z"
                fill="#ffffff"
              />
            </svg>
          </div>
        </div>
      </div> */}

      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Notifications</h1>
        </div>
        <button
          className="text-white text-sm font-medium"
          onClick={markAllAsRead}
        >
          Mark all as read
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white px-4 py-3 border-b border-[#f2f2f2] overflow-x-auto">
        <div className="flex space-x-2">
          <button
            className={`px-3 py-1 rounded-full text-sm whitespace-nowrap flex items-center ${
              showUnreadOnly
                ? "bg-[#009639] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setShowUnreadOnly(!showUnreadOnly)}
          >
            <Bell size={14} className="mr-1" />
            Unread
          </button>
          {filterTypes.map((filter) => (
            <button
              key={filter.id}
              className={`flex items-center px-3 py-1 rounded-full text-sm whitespace-nowrap ${
                activeFilter === filter.id
                  ? "bg-[#009639] text-white"
                  : "bg-[#f2f2f2] text-[#333333]"
              }`}
              onClick={() =>
                setActiveFilter(activeFilter === filter.id ? null : filter.id)
              }
            >
              {filter.icon}
              <span className="ml-1">{filter.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Notifications List */}
      <div className="p-4">
        {filteredNotifications.length > 0 ? (
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 border-b border-[#f2f2f2] last:border-b-0 ${
                  !notification.isRead ? "bg-[#f9fff9]" : ""
                }`}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                      !notification.isRead ? "bg-[#e6ffe6]" : "bg-[#f2f2f2]"
                    }`}
                  >
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3
                        className={`text-[#333333] ${
                          !notification.isRead ? "font-medium" : ""
                        }`}
                      >
                        {notification.title}
                      </h3>
                      <span className="text-xs text-[#797879]">
                        {formatTime(notification.timestamp)}
                      </span>
                    </div>
                    <p className="text-sm text-[#797879] mt-1">
                      {notification.message}
                    </p>
                    {notification.actionRequired && (
                      <div className="mt-2">
                        <span className="text-xs px-2 py-0.5 rounded-full bg-[#fff9e6] text-[#7A5A00] font-medium">
                          Action Required
                        </span>
                      </div>
                    )}
                  </div>
                  {!notification.isRead && (
                    <div className="ml-2 w-2 h-2 rounded-full bg-[#009639] self-start mt-2"></div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <Bell size={40} className="text-[#d6d9dd] mx-auto mb-3" />
            <p className="text-[#333333] font-medium">No notifications</p>
            <p className="text-[#797879] text-sm mt-1">
              {activeFilter || showUnreadOnly
                ? "Try adjusting your filters"
                : "You're all caught up!"}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
