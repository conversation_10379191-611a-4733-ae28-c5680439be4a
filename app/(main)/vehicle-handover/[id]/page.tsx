"use server";
import { getVehicle } from "@/actions/vehicles";
import VehicleHandoverScreen from "./vehicle-handover";
import { addPossessionsFull } from "@/actions/vehicle-inspections";
import { getUserAttributes } from "@/lib/amplifyServerUtils";
import MissingParty from "@/components/missing-party";

export default async function VehicleHandover({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;
  const vehicle = await getVehicle(+id);
  return (
    <VehicleHandoverScreen
      action={addPossessionsFull}
      vehicle={vehicle}
      party_id={+dbId}
    />
  );
}
