"use client";

import { useFormStatus } from "react-dom";

export default function FormSubmit() {
  const status = useFormStatus();
  if (status.pending) {
    return (
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2]">
        <button
          type="submit"
          className="w-full bg-[#009639] text-white py-4 rounded-full text-xl font-semibold shadow-sm"
        >
          <div className="border-t-4 border-b-4 border-white w-6 h-6 border-solid rounded-full animate-spin"></div>
        </button>
      </div>
    );
  }

  return (
    <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2]">
      <button
        type="submit"
        className="w-full bg-[#009639] text-white py-4 rounded-full text-xl font-semibold shadow-sm"
      >
        Complete Handover
      </button>
    </div>
  );
}
