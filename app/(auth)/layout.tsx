"use client";
import outputs from "@/amplify_outputs.json";
import StoreProvider from "@/app/StoreProvider";
import { Amplify } from "aws-amplify";
import { cognitoUserPoolsTokenProvider } from 'aws-amplify/auth/cognito';
import { CookieStorage } from 'aws-amplify/utils';
Amplify.configure(outputs);
cognitoUserPoolsTokenProvider.setKeyValueStorage(new CookieStorage());

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <StoreProvider>
      <div className="min-h-screen">{children}</div>
    </StoreProvider>
  );
}
