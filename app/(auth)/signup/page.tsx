"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Eye, EyeOff } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { signup } from "@/actions/auth";
import { useAppDispatch } from "@/lib/hooks";
import { setCredentials } from "@/lib/features/auth/authSlice";

// Define the validation schema with Zod
const signupSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  phone: z
    .string()
    .min(1, "Phone number is required")
    .regex(
      /^\+?[1-9]\d{1,14}$/,
      "Please enter a valid phone number with country code (e.g., +2712345678)"
    ),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(
      /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*])/,
      "Password must include letters, numbers, and symbols"
    ),
  agreeTerms: z
    .boolean()
    .refine(
      (val) => val === true,
      "You must agree to the Terms of Service and Privacy Policy"
    ),
});

// Infer the form data type from the schema
type SignupFormData = z.infer<typeof signupSchema>;

export default function SignupScreen() {
  const router = useRouter();
  const dispatch = useAppDispatch();

  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // Initialize react-hook-form with zod resolver
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignupFormData>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      password: "",
      agreeTerms: false,
    },
  });

  const onSubmit = async (data: SignupFormData) => {
    setIsLoading(true);
    setError("");
    try {
      const { nextStep, userId } = await signup(data);
      dispatch(setCredentials({ sub: userId }));
      if (nextStep.signUpStep === "CONFIRM_SIGN_UP") {
        router.push(
          `/verification?deliveryMedium=${nextStep.codeDeliveryDetails.deliveryMedium}&destination=${nextStep.codeDeliveryDetails.destination}`
        );
      }
      if (nextStep.signUpStep === "DONE") {
        router.push("/home");
      }
      router.push("/verification");
    } catch (err: any) {
      setError(err.message || "An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white px-6 py-8">
      {/* Header */}
      <div className="flex items-center mb-8">
        <Button
          variant="ghost"
          className="bg-[#e6ffe6] rounded-full p-2 mr-4 shadow-sm"
          onClick={() => router.push("/welcome")}
        >
          <ArrowLeft size={24} className="text-[#009639]" />
        </Button>
        <h1 className="text-2xl font-bold text-[#333333]">Create Account</h1>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div>
          <Label
            htmlFor="firstName"
            className="block text-sm font-medium text-[#333333] mb-1"
          >
            First Name
          </Label>
          <Input
            type="text"
            id="firstName"
            {...register("firstName")}
            className="w-full p-3 border border-[#e6e6e6] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
            placeholder="Enter your first name"
          />
          {errors.firstName && (
            <p className="mt-1 text-sm text-red-600">
              {errors.firstName.message}
            </p>
          )}
        </div>
        <div>
          <Label
            htmlFor="lastName"
            className="block text-sm font-medium text-[#333333] mb-1"
          >
            Last Name
          </Label>
          <Input
            type="text"
            id="lastName"
            {...register("lastName")}
            className="w-full p-3 border border-[#e6e6e6] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
            placeholder="Enter your last name"
          />
          {errors.lastName && (
            <p className="mt-1 text-sm text-red-600">
              {errors.lastName.message}
            </p>
          )}
        </div>
        <div>
          <Label
            htmlFor="email"
            className="block text-sm font-medium text-[#333333] mb-1"
          >
            Email Address
          </Label>
          <Input
            type="email"
            id="email"
            {...register("email")}
            className="w-full p-3 border border-[#e6e6e6] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
            placeholder="Enter your email address"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
          )}
        </div>

        <div>
          <Label
            htmlFor="phone"
            className="block text-sm font-medium text-[#333333] mb-1"
          >
            Phone Number
          </Label>
          <Input
            type="tel"
            id="phone"
            {...register("phone")}
            className="w-full p-3 border border-[#e6e6e6] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
            placeholder="Enter your phone number"
          />
          {errors.phone && (
            <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
          )}
        </div>

        <div>
          <Label
            htmlFor="password"
            className="block text-sm font-medium text-[#333333] mb-1"
          >
            Password
          </Label>
          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              id="password"
              {...register("password")}
              className="w-full p-3 border border-[#e6e6e6] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              placeholder="Create a password"
            />
            <Button
              type="button"
              variant="ghost"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#797879]"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </Button>
          </div>
          <p className="mt-1 text-xs text-[#797879]">
            Password must be at least 8 characters long with a mix of letters,
            numbers, and symbols.
          </p>
          {errors.password && (
            <p className="mt-1 text-sm text-red-600">
              {errors.password.message}
            </p>
          )}
        </div>

        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="agreeTerms"
              type="checkbox"
              {...register("agreeTerms")}
              className="h-4 w-4 text-[#009639] focus:ring-[#009639] border-gray-300 rounded"
            />
          </div>
          <div className="ml-3 text-sm">
            <Label htmlFor="agreeTerms" className="text-[#797879]">
              I agree to the{" "}
              <Button
                type="button"
                variant="link"
                className="text-[#009639] font-medium"
                onClick={() => router.push("/terms")}
              >
                Terms of Service
              </Button>{" "}
              and{" "}
              <Button
                type="button"
                variant="link"
                className="text-[#009639] font-medium"
                onClick={() => router.push("/privacy")}
              >
                Privacy Policy
              </Button>
            </Label>
          </div>
          {errors.agreeTerms && (
            <p className="mt-1 text-sm text-red-600">
              {errors.agreeTerms.message}
            </p>
          )}
        </div>

        {error && <p className="text-sm text-red-600">{error}</p>}

        <Button
          type="submit"
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3 rounded-full font-semibold shadow-md"
        >
          {isLoading ? "Creating Account..." : "Create Account"}
        </Button>
      </form>

      {/* Login link */}
      <div className="mt-8 text-center">
        <p className="text-[#797879]">
          Already have an account?{" "}
          <Button
            variant="link"
            className="text-[#009639] font-medium"
            onClick={() => router.push("/login")}
          >
            Log In
          </Button>
        </p>
      </div>
    </div>
  );
}
