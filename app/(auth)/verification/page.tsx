"use client";

import type React from "react";

import { confirmUserSignUp, resendCode } from "@/actions/auth";
import { selectCurrentUser } from "@/lib/features/auth/authSlice";
import { useAppSelector } from "@/lib/hooks";
import { ArrowLeft } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useRef, useState } from "react";

export function VerificationScreenContent() {
  const router = useRouter();
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const searchParams = useSearchParams();

  const deliveryMedium = searchParams.get("deliveryMedium");
  const destination = searchParams.get("destination");
  const [resentCode, setResendCode] = useState(false);
  const [error, setError] = useState("");

  const auth = useAppSelector(selectCurrentUser);
  const email = auth.email || auth.sub || "";
  const handlePaste = (index: number, e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedValue = e.clipboardData.getData("Text");
    const digits = pastedValue.split("").filter((char) => /^\d+$/.test(char));

    const newOtp = [...otp];
    digits.forEach((digit, i) => {
      if (index + i < otp.length) {
        newOtp[index + i] = digit;
      }
    });

    setOtp(newOtp);

    // Move the focus to the next empty input after pasting
    const nextEmptyIndex = newOtp.findIndex(
      (value, idx) => idx > index && !value
    );
    inputRefs.current[
      nextEmptyIndex !== -1 ? nextEmptyIndex : otp.length - 1
    ]?.focus();
  };

  const handleChange = (index: number, value: string) => {
    // Only allow numbers
    if (value && !/^\d+$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value.slice(0, 1);
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    // Move to previous input on backspace if current input is empty
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleResendCode = async () => {
    try {
      setResendCode(true);
      await resendCode(email);
      setResendCode(false);
    } catch (err: any) {
      if (err && err.message) {
        setError(err.message);
      } else {
        setError("An error occurred. Please try again.");
      }
    }
  };

  const handleVerify = async () => {
    try {
      const otpValue = otp.join("");
      const nextStep = await confirmUserSignUp({
        username: email,
        confirmationCode: otpValue,
      });

      if (nextStep.signUpStep === "DONE") {
        router.push("/home");
      }
    } catch (err: any) {
      if (err && err.message) {
        setError(err.message);
      } else {
        setError("An error occurred. Please try again.");
      }
    }
  };

  return (
    <div className="min-h-screen bg-white px-6 py-8">
      {/* Header */}
      <div className="flex items-center mb-8">
        <button
          className="bg-[#e6ffe6] rounded-full p-2 mr-4 shadow-sm"
          onClick={() => router.back()}
        >
          <ArrowLeft size={24} className="text-[#009639]" />
        </button>
        <h1 className="text-2xl font-bold text-[#333333]">Verification</h1>
      </div>

      <div className="max-w-md mx-auto">
        {/* Instructions */}
        <p className="text-[#797879] mb-8">
          We've sent a verification code to your{" "}
          {deliveryMedium ? deliveryMedium?.toLowerCase() : "email"}
          {destination && `(${destination})`} . Please enter the code below.
        </p>
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4 text-sm text-[#797879] mb-8">
            {error}
          </div>
        )}

        {/* OTP Input */}
        <div className="flex justify-between mb-8">
          {otp.map((digit, index) => (
            <input
              key={index}
              ref={(el) => (inputRefs.current[index] = el)}
              type="text"
              value={digit}
              onChange={(e) => handleChange(index, e.target.value)}
              onPaste={(e) => handlePaste(index, e)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              className="w-16 h-16 text-center text-2xl font-bold rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] bg-white"
              maxLength={1}
            />
          ))}
        </div>

        {/* Timer and Resend */}
        <div className="flex justify-center items-center mb-8">
          <button
            className="text-[#009639] font-medium"
            onClick={handleResendCode}
          >
            Resend Code
          </button>
        </div>

        {/* Verify Button */}
        <button
          className="w-full bg-[#009639] text-white py-4 rounded-full text-xl font-semibold shadow-sm flex justify-center items-center"
          onClick={handleVerify}
          disabled={otp.some((digit) => !digit) || resentCode}
        >
          {resentCode ? (
            <div className="border-t-4 border-b-4 border-white w-6 h-6 border-solid rounded-full animate-spin"></div>
          ) : (
            "Verify"
          )}
        </button>
      </div>
    </div>
  );
}
export default function VerificationScreen() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <VerificationScreenContent />
    </Suspense>
  );
}
