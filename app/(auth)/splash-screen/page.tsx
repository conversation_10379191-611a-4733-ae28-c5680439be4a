"use client";

import React from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

// Component to inject CSS animations
function AnimationStyles() {
  return (
    <style jsx global>{`
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes float {
        0% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
        100% {
          transform: translateY(0px);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 1s ease-out forwards;
      }

      .animate-float {
        animation: float 3s ease-in-out infinite;
      }
    `}</style>
  );
}

export default function SplashScreen() {
  const router = useRouter();

  return (
    <div className="h-screen flex flex-col items-center justify-center px-6 relative overflow-hidden">
      {/* Full green gradient background with pattern */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#009639] to-[#007A2F] z-0"></div>

      {/* Subtle pattern overlay */}
      <div className="absolute inset-0 opacity-5 z-0">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage:
              "url('data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 0C6.716 0 0 6.716 0 15c0 8.284 6.716 15 15 15 8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15zm0 5c5.514 0 10 4.486 10 10s-4.486 10-10 10S5 20.514 5 15 9.486 5 15 5z' fill='%23ffffff' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E')",
            backgroundSize: "30px 30px",
          }}
        ></div>
      </div>

      {/* Simple decorative elements */}
      <div className="absolute top-1/4 left-1/4 w-40 h-40 rounded-full bg-white/5 z-0"></div>
      <div className="absolute bottom-1/4 right-1/4 w-60 h-60 rounded-full bg-white/3 z-0"></div>

      {/* Content container */}
      <div className="flex flex-col items-center justify-center h-full w-full max-w-md z-10 relative">
        {/* Logo - White version - Prominently placed in the middle - LARGER */}
        <div className="w-full h-2/5 relative mb-16 animate-float">
          <Image
            src="/images/Poolly.White.Svg.svg"
            alt="Poolly Logo"
            fill
            className="object-contain"
            priority
          />
        </div>

        {/* All text under the logo as one paragraph */}
        <div className="w-full mb-8">
          {/* Get Started Button */}
          <button
            className="w-full bg-white text-[#009639] py-4 rounded-full text-xl font-semibold shadow-md transition-transform duration-300 hover:scale-105 animate-fadeIn mt-8"
            onClick={() => router.push("/welcome")}
          >
            Get Started
          </button>
        </div>
      </div>
      <AnimationStyles />
    </div>
  );
}
