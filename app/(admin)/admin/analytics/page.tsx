"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Chart,
  Line,
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart,
  Area,
} from "recharts";
import {
  ArrowRight,
  MoreHorizontal,
  Download,
  Calendar,
  Filter,
  ChevronDown,
  Users,
  Car,
  DollarSign,
  TrendingUp,
  ShieldCheck,
  FileText,
} from "lucide-react";

// Mock data for charts
const statisticsData = [
  { name: "Active Users", value: 1247, color: "#009639", icon: "👥" },
  { name: "Active Groups", value: 86, color: "#007A2F", icon: "👪" },
  { name: "Vehicles", value: 123, color: "#FFD700", icon: "🚗" },
  { name: "Partners", value: 32, color: "#007A2F", icon: "🤝" },
];

// User acquisition data (monthly new users)
const userAcquisitionData = [
  { month: "Jan", newUsers: 120, activeUsers: 980, churnRate: 2.1 },
  { month: "Feb", newUsers: 145, activeUsers: 1050, churnRate: 1.8 },
  { month: "Mar", newUsers: 165, activeUsers: 1120, churnRate: 2.3 },
  { month: "Apr", newUsers: 190, activeUsers: 1180, churnRate: 2.5 },
  { month: "May", newUsers: 210, activeUsers: 1220, churnRate: 2.8 },
  { month: "Jun", newUsers: 180, activeUsers: 1240, churnRate: 3.0 },
  { month: "Jul", newUsers: 160, activeUsers: 1230, churnRate: 2.7 },
  { month: "Aug", newUsers: 140, activeUsers: 1210, churnRate: 2.5 },
  { month: "Sep", newUsers: 155, activeUsers: 1225, churnRate: 2.3 },
  { month: "Oct", newUsers: 175, activeUsers: 1235, churnRate: 2.6 },
  { month: "Nov", newUsers: 195, activeUsers: 1245, churnRate: 2.4 },
  { month: "Dec", newUsers: 150, activeUsers: 1247, churnRate: 2.2 },
];

// Transaction volume data
const transactionVolumeData = [
  { month: "Jan", vehicleSharing: 45, maintenance: 28, insurance: 32 },
  { month: "Feb", vehicleSharing: 52, maintenance: 24, insurance: 35 },
  { month: "Mar", vehicleSharing: 58, maintenance: 32, insurance: 30 },
  { month: "Apr", vehicleSharing: 63, maintenance: 36, insurance: 38 },
  { month: "May", vehicleSharing: 70, maintenance: 40, insurance: 42 },
  { month: "Jun", vehicleSharing: 75, maintenance: 38, insurance: 45 },
  { month: "Jul", vehicleSharing: 80, maintenance: 42, insurance: 48 },
  { month: "Aug", vehicleSharing: 76, maintenance: 45, insurance: 50 },
  { month: "Sep", vehicleSharing: 72, maintenance: 40, insurance: 46 },
  { month: "Oct", vehicleSharing: 68, maintenance: 38, insurance: 44 },
  { month: "Nov", vehicleSharing: 65, maintenance: 35, insurance: 42 },
  { month: "Dec", vehicleSharing: 60, maintenance: 30, insurance: 40 },
];

// User engagement data
const userEngagementData = [
  { metric: "Daily Active Users", value: 520 },
  { metric: "Weekly Active Users", value: 850 },
  { metric: "Monthly Active Users", value: 1247 },
  { metric: "Avg. Session Duration", value: 8.5 },
  { metric: "Avg. Sessions per User", value: 12 },
  { metric: "Retention Rate", value: 78 },
];

// Financial performance data
const financialPerformanceData = [
  { month: "Jan", revenue: 125000, expenses: 85000, profit: 40000 },
  { month: "Feb", revenue: 135000, expenses: 88000, profit: 47000 },
  { month: "Mar", revenue: 142000, expenses: 90000, profit: 52000 },
  { month: "Apr", revenue: 150000, expenses: 92000, profit: 58000 },
  { month: "May", revenue: 160000, expenses: 95000, profit: 65000 },
  { month: "Jun", revenue: 168000, expenses: 98000, profit: 70000 },
  { month: "Jul", revenue: 175000, expenses: 100000, profit: 75000 },
  { month: "Aug", revenue: 172000, expenses: 99000, profit: 73000 },
  { month: "Sep", revenue: 165000, expenses: 97000, profit: 68000 },
  { month: "Oct", revenue: 158000, expenses: 94000, profit: 64000 },
  { month: "Nov", revenue: 152000, expenses: 92000, profit: 60000 },
  { month: "Dec", revenue: 145000, expenses: 90000, profit: 55000 },
];

// Compliance metrics data
const complianceMetricsData = [
  { category: "Vehicle Documentation", compliant: 92, nonCompliant: 8 },
  { category: "Insurance Coverage", compliant: 95, nonCompliant: 5 },
  { category: "User Verification", compliant: 88, nonCompliant: 12 },
  { category: "Payment Compliance", compliant: 90, nonCompliant: 10 },
  { category: "Dispute Resolution", compliant: 85, nonCompliant: 15 },
];

// Revenue sources data
const revenueSourcesData = [
  { name: "Membership Fees", value: 35, color: "#009639" },
  { name: "Transaction Fees", value: 25, color: "#FFD700" },
  { name: "Partner Commissions", value: 20, color: "#007A2F" },
  { name: "Premium Services", value: 15, color: "#00BF63" },
  { name: "Other", value: 5, color: "#004D1F" },
];

// Vehicle usage data by province
const vehicleUsageByProvinceData = [
  { province: "Gauteng", usage: 42 },
  { province: "Western Cape", usage: 28 },
  { province: "KwaZulu-Natal", usage: 15 },
  { province: "Eastern Cape", usage: 8 },
  { province: "Free State", usage: 4 },
  { province: "Mpumalanga", usage: 3 },
];

// Monthly data for vehicle usage
const monthlyData = [
  { month: "Jan", easy: 20, medium: 15, hard: 10 },
  { month: "Feb", easy: 15, medium: 10, hard: 5 },
  { month: "Mar", easy: 18, medium: 12, hard: 8 },
  { month: "Apr", easy: 22, medium: 14, hard: 6 },
  { month: "May", easy: 25, medium: 18, hard: 12 },
  { month: "Jun", easy: 28, medium: 20, hard: 15 },
  { month: "Jul", easy: 30, medium: 22, hard: 18 },
  { month: "Aug", easy: 27, medium: 19, hard: 14 },
  { month: "Sep", easy: 24, medium: 16, hard: 10 },
  { month: "Oct", easy: 26, medium: 18, hard: 12 },
  { month: "Nov", easy: 23, medium: 15, hard: 9 },
  { month: "Dec", easy: 20, medium: 13, hard: 7 },
];

// Vehicle type data
const questionTypeData = [
  { type: "Sedan", value: 35 },
  { type: "SUV", value: 25 },
  { type: "Hatchback", value: 20 },
  { type: "Bakkie", value: 15 },
  { type: "Minibus", value: 5 },
];

// Vehicle statistics
const certificatesData = [
  { name: "Registered", value: 85, color: "#009639" },
  { name: "Active", value: 75, color: "#FFD700" },
  { name: "Compliant", value: 90, color: "#007A2F" },
];

// Top performing groups
const prizeQuizzesData = [
  { name: "Johannesburg Commuters", date: "15/01/2025", passRate: 92 },
  { name: "Cape Town Carpoolers", date: "10/01/2025", passRate: 88 },
  { name: "Pretoria Partners", date: "05/01/2025", passRate: 85 },
];

// Registration statistics
const registrationData = [
  { name: "Individual Users", value: 1247, color: "#009639" },
  { name: "Group Admins", value: 86, color: "#007A2F" },
  { name: "Partner Companies", value: 32, color: "#FFD700" },
];

export default function AnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState("Last 12 Months");
  const [reportType, setReportType] = useState("All Reports");

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Analytics Dashboard
        </h1>
        <p className="text-gray-500">
          Comprehensive reports and analytics for business performance
          monitoring
        </p>
      </div>

      {/* Filter Controls */}
      <div className="bg-white rounded-xl shadow-sm p-4 mb-6">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <select
                className="appearance-none pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
              >
                <option>Last 7 Days</option>
                <option>Last 30 Days</option>
                <option>Last 90 Days</option>
                <option>Last 12 Months</option>
                <option>Year to Date</option>
                <option>Custom Range</option>
              </select>
              <Calendar
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={16}
              />
              <ChevronDown
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={16}
              />
            </div>

            <div className="relative">
              <select
                className="appearance-none pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                value={reportType}
                onChange={(e) => setReportType(e.target.value)}
              >
                <option>All Reports</option>
                <option>User Acquisition</option>
                <option>Transaction Volume</option>
                <option>User Engagement</option>
                <option>Financial Performance</option>
                <option>Compliance Metrics</option>
              </select>
              <Filter
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={16}
              />
              <ChevronDown
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={16}
              />
            </div>
          </div>

          <button className="bg-white border border-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 flex items-center">
            <Download size={16} className="mr-2" />
            Export Reports
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {statisticsData.map((stat, index) => (
          <div key={index} className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-lg flex items-center justify-center mr-3`}
                  style={{ backgroundColor: `${stat.color}20` }}
                >
                  <span className="text-xl">{stat.icon}</span>
                </div>
                <span className="text-gray-600 font-medium">{stat.name}</span>
              </div>
              <button className="text-gray-400">
                <ArrowRight size={18} />
              </button>
            </div>
            <div className="flex items-center">
              <h3 className="text-2xl font-bold mr-3">{stat.value}</h3>
              <div className="h-10">
                <ResponsiveContainer width={60} height="100%">
                  <LineChart
                    data={Array(10)
                      .fill(0)
                      .map((_, i) => ({ value: Math.random() * 10 + 5 }))}
                  >
                    <Line
                      type="monotone"
                      dataKey="value"
                      stroke={stat.color}
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* User Acquisition and Transaction Volume Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* User Acquisition Chart */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <Users size={20} className="text-[#009639] mr-2" />
              <h3 className="text-lg font-semibold">User Acquisition</h3>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-2">Last 12 Months</span>
              <button className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
                <Download size={14} />
              </button>
            </div>
          </div>

          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={userAcquisitionData}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="month"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: "#888", fontSize: 12 }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: "#888", fontSize: 12 }}
                />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="newUsers"
                  name="New Users"
                  stroke="#009639"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                />
                <Line
                  type="monotone"
                  dataKey="activeUsers"
                  name="Active Users"
                  stroke="#FFD700"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  activeDot={{ r: 5 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Transaction Volume Chart */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <Car size={20} className="text-[#009639] mr-2" />
              <h3 className="text-lg font-semibold">Transaction Volume</h3>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-2">Last 12 Months</span>
              <button className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
                <Download size={14} />
              </button>
            </div>
          </div>

          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={transactionVolumeData}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="month"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: "#888", fontSize: 12 }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: "#888", fontSize: 12 }}
                />
                <Tooltip />
                <Legend />
                <Bar
                  dataKey="vehicleSharing"
                  name="Vehicle Sharing"
                  fill="#009639"
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="maintenance"
                  name="Maintenance"
                  fill="#FFD700"
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="insurance"
                  name="Insurance"
                  fill="#007A2F"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Financial Performance and Compliance Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Financial Performance Chart */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <DollarSign size={20} className="text-[#009639] mr-2" />
              <h3 className="text-lg font-semibold">Financial Performance</h3>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-2">Last 12 Months</span>
              <button className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
                <Download size={14} />
              </button>
            </div>
          </div>

          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={financialPerformanceData}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="month"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: "#888", fontSize: 12 }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: "#888", fontSize: 12 }}
                />
                <Tooltip formatter={(value) => `R${value.toLocaleString()}`} />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="revenue"
                  name="Revenue"
                  stroke="#009639"
                  fill="#009639"
                  fillOpacity={0.2}
                />
                <Area
                  type="monotone"
                  dataKey="expenses"
                  name="Expenses"
                  stroke="#FFD700"
                  fill="#FFD700"
                  fillOpacity={0.2}
                />
                <Area
                  type="monotone"
                  dataKey="profit"
                  name="Profit"
                  stroke="#007A2F"
                  fill="#007A2F"
                  fillOpacity={0.2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Compliance Metrics Chart */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <ShieldCheck size={20} className="text-[#009639] mr-2" />
              <h3 className="text-lg font-semibold">Compliance Metrics</h3>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-2">Current Status</span>
              <button className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
                <Download size={14} />
              </button>
            </div>
          </div>

          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={complianceMetricsData}
                layout="vertical"
                barSize={20}
              >
                <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                <XAxis
                  type="number"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: "#888", fontSize: 12 }}
                  domain={[0, 100]}
                />
                <YAxis
                  type="category"
                  dataKey="category"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: "#888", fontSize: 12 }}
                  width={150}
                />
                <Tooltip />
                <Legend />
                <Bar
                  dataKey="compliant"
                  name="Compliant"
                  stackId="a"
                  fill="#009639"
                />
                <Bar
                  dataKey="nonCompliant"
                  name="Non-Compliant"
                  stackId="a"
                  fill="#FFD700"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Revenue Sources and User Engagement */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Revenue Sources Chart */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <TrendingUp size={20} className="text-[#009639] mr-2" />
              <h3 className="text-lg font-semibold">Revenue Sources</h3>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-2">Current Period</span>
              <button className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
                <Download size={14} />
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={revenueSourcesData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {revenueSourcesData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>

            <div className="flex flex-col justify-center">
              {revenueSourcesData.map((source, index) => (
                <div key={index} className="flex items-center mb-3">
                  <div
                    className="w-4 h-4 rounded-full mr-2"
                    style={{ backgroundColor: source.color }}
                  ></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{source.name}</p>
                  </div>
                  <p className="text-sm font-bold">{source.value}%</p>
                </div>
              ))}
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Total Revenue: <span className="font-bold">R1,847,000</span>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* User Engagement Metrics */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <Users size={20} className="text-[#009639] mr-2" />
              <h3 className="text-lg font-semibold">User Engagement Metrics</h3>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-2">Current Period</span>
              <button className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
                <Download size={14} />
              </button>
            </div>
          </div>

          <div className="space-y-4">
            {userEngagementData.map((metric, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <p className="text-sm font-medium text-gray-600">
                    {metric.metric}
                  </p>
                  <p className="text-lg font-bold">
                    {typeof metric.value === "number" && metric.value % 1 === 0
                      ? metric.value
                      : metric.value}
                    {metric.metric.includes("Rate") ? "%" : ""}
                    {metric.metric.includes("Duration") ? " min" : ""}
                  </p>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full bg-[#009639]"
                    style={{
                      width: `${
                        metric.metric.includes("Rate")
                          ? metric.value
                          : (metric.value /
                              (metric.metric.includes("Active")
                                ? 1500
                                : metric.metric.includes("Duration")
                                ? 15
                                : 20)) *
                            100
                      }%`,
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Vehicle Statistics and Top Performing Groups */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Vehicle Statistics */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <Car size={20} className="text-[#009639] mr-2" />
              <h3 className="text-lg font-semibold">Vehicle Statistics</h3>
            </div>
            <button className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
              <Download size={14} />
            </button>
          </div>

          <div className="grid grid-cols-3 gap-4">
            {certificatesData.map((cert, index) => (
              <div key={index} className="flex flex-col items-center">
                <div className="mb-2">
                  <div className="h-24 w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={[cert]} barSize={30}>
                        <Bar
                          dataKey="value"
                          fill={cert.color}
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-500">{cert.name}</p>
                  <p className="text-lg font-semibold">{cert.value}%</p>
                  <p className="text-xs text-gray-400">
                    <span className="text-green-500">↑</span> 5% from last month
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Performing Groups */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <Users size={20} className="text-[#009639] mr-2" />
              <h3 className="text-lg font-semibold">Top Performing Groups</h3>
            </div>
            <button className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
              <Download size={14} />
            </button>
          </div>

          <div className="space-y-4">
            {prizeQuizzesData.map((quiz, index) => (
              <div key={index} className="flex items-center">
                <div className="w-10 h-10 rounded-lg bg-yellow-100 flex items-center justify-center mr-3">
                  <span className="text-xl">🏆</span>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">{quiz.name}</p>
                  <p className="text-xs text-gray-500">{quiz.date}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{quiz.passRate}%</p>
                  <p className="text-xs text-gray-500">Efficiency</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Registration Statistics */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <Users size={20} className="text-[#009639] mr-2" />
              <h3 className="text-lg font-semibold">Registration Statistics</h3>
            </div>
            <button className="w-6 h-6 bg-gray-200 rounded flex items-center justify-center">
              <Download size={14} />
            </button>
          </div>

          <div className="mb-4">
            <h4 className="text-2xl font-bold mb-1">1,365</h4>
            <div className="flex items-center">
              <span className="text-green-500 text-sm mr-1">↑</span>
              <span className="text-green-500 text-sm">
                9.4% from last month
              </span>
            </div>
          </div>

          <div className="space-y-3">
            {registrationData.map((reg, index) => (
              <div key={index}>
                <div className="flex justify-between items-center mb-1">
                  <div className="flex items-center">
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: reg.color }}
                    ></div>
                    <span className="text-sm text-gray-600">{reg.name}</span>
                  </div>
                  <span className="text-sm font-medium">{reg.value}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full"
                    style={{
                      width: `${
                        (reg.value /
                          Math.max(...registrationData.map((d) => d.value))) *
                        100
                      }%`,
                      backgroundColor: reg.color,
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Export Options */}
      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <FileText size={20} className="text-[#009639] mr-2" />
            <h3 className="text-lg font-semibold">Export Reports</h3>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg flex flex-col items-center justify-center">
            <Download size={24} className="text-[#009639] mb-2" />
            <p className="text-sm font-medium">User Acquisition Report</p>
            <p className="text-xs text-gray-500">PDF, Excel, CSV</p>
          </button>
          <button className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg flex flex-col items-center justify-center">
            <Download size={24} className="text-[#009639] mb-2" />
            <p className="text-sm font-medium">Financial Performance Report</p>
            <p className="text-xs text-gray-500">PDF, Excel, CSV</p>
          </button>
          <button className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg flex flex-col items-center justify-center">
            <Download size={24} className="text-[#009639] mb-2" />
            <p className="text-sm font-medium">Compliance Metrics Report</p>
            <p className="text-xs text-gray-500">PDF, Excel, CSV</p>
          </button>
        </div>
      </div>
    </div>
  );
}
