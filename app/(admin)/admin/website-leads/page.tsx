"use client";

import { useEffect, useState } from 'react';
import { getFormSubmissions, getFormSubmissionStats } from '@/db/queries';
import type { FormSubmission, FormType } from '@/db/queries';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CalendarDays, Mail, Phone, Building, CheckCircle, XCircle, ChevronRight, Globe, TrendingUp, Clock, FileText } from 'lucide-react';
import { format } from 'date-fns';
import React from 'react';

interface FormStats {
  formType: FormType;
  count: number;
  emailsSent: number;
}

const formTypeLabels: Record<FormType, string> = {
  business: 'Business Solutions',
  'co-own': 'Co-Own',
  management: 'Management',
  rideshare: 'Rideshare',
  monetise: 'Monetise',
  waitlist: 'Waitlist'
};

const formTypeColors: Record<FormType, string> = {
  business: 'bg-blue-500',
  'co-own': 'bg-green-500',
  management: 'bg-purple-500',
  rideshare: 'bg-yellow-500',
  monetise: 'bg-red-500',
  waitlist: 'bg-gray-500'
};

// Component to render form-specific details
function FormDetails({ submission }: { submission: FormSubmission }) {
  const formData = submission.formData as any;

  // Filter out null/undefined values and format the data
  const cleanFormData = Object.entries(formData)
    .filter(([key, value]) => value !== null && value !== undefined && value !== '')
    .reduce((acc, [key, value]) => {
      // Handle arrays (like salary ranges, business incomes)
      if (Array.isArray(value)) {
        if (value.length > 0 && value.some(item => item !== null && item !== undefined && item !== '')) {
          acc[key] = value.filter(item => item !== null && item !== undefined && item !== '').join(', ');
        }
      } else {
        acc[key] = String(value);
      }
      return acc;
    }, {} as Record<string, string>);

  // If no data to show
  if (Object.keys(cleanFormData).length === 0) {
    return <p className="text-gray-500 text-sm p-4">No additional details available</p>;
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-1/3">Field</TableHead>
            <TableHead>Value</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Object.entries(cleanFormData).map(([key, value]) => (
            <TableRow key={key}>
              <TableCell className="font-medium">{formatColumnHeader(key)}</TableCell>
              <TableCell>{value}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

// Helper function to format column headers
function formatColumnHeader(key: string): string {
  // Convert camelCase to Title Case
  return key
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
}

export default function WebsiteLeadsPage() {
  const [submissions, setSubmissions] = useState<FormSubmission[]>([]);
  const [stats, setStats] = useState<FormStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedType, setSelectedType] = useState<FormType | 'all'>('all');
  const [expandedRow, setExpandedRow] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        const [submissionsData, statsData] = await Promise.all([
          getFormSubmissions({ limit: 100 }),
          getFormSubmissionStats()
        ]);
        
        setSubmissions(submissionsData);
        setStats(statsData as FormStats[]);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const filteredSubmissions = selectedType === 'all' 
    ? submissions 
    : submissions.filter(sub => sub.formType === selectedType);

  // Calculate total emails sent
  const totalEmailsSent = stats.reduce((sum, stat) => sum + stat.emailsSent, 0);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#009639] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading website leads...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Website Leads</h1>
        <p className="text-gray-600 mt-1">Manage and track form submissions from your website</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Leads - Most Prominent */}
        <Card className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-white/90 flex items-center">
              <Globe className="h-4 w-4 mr-2" />
              Total Leads
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold">{submissions.length}</p>
                <p className="text-xs text-white/80">All-time submissions</p>
              </div>
              <TrendingUp className="h-8 w-8 text-white/60" />
            </div>
          </CardContent>
        </Card>

        {/* Total Emails Sent */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Emails Sent
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-semibold text-gray-900">{totalEmailsSent}</p>
                <p className="text-xs text-gray-500">Follow-up emails</p>
              </div>
              <Mail className="h-6 w-6 text-green-500" />
            </div>
          </CardContent>
        </Card>

        {/* Pending Emails */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Pending Emails
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-semibold text-gray-900">{submissions.length - totalEmailsSent}</p>
                <p className="text-xs text-gray-500">Awaiting follow-up</p>
              </div>
              <Clock className="h-6 w-6 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        {/* Form Types */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">
              Form Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-semibold text-gray-900">{stats.length}</p>
                <p className="text-xs text-gray-500">Active form types</p>
              </div>
              <FileText className="h-6 w-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Form Type Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {stats.map((stat) => (
          <Card key={stat.formType} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                {formTypeLabels[stat.formType]}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xl font-semibold text-gray-900">{stat.count}</p>
                  <p className="text-xs text-gray-500">{stat.emailsSent} emails sent</p>
                </div>
                <div className={`w-3 h-3 rounded-full ${formTypeColors[stat.formType]}`} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Leads Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Submissions</CardTitle>
              <CardDescription>
                Latest form submissions from your website
              </CardDescription>
            </div>
            <Tabs value={selectedType} onValueChange={(value) => setSelectedType(value as FormType | 'all')}>
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                {stats.map((stat) => (
                  <TabsTrigger key={stat.formType} value={stat.formType}>
                    {formTypeLabels[stat.formType]} ({stat.count})
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Contact</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Email Status</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead className="w-[50px]">Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSubmissions.map((submission) => (
                  <React.Fragment key={submission.id}>
                    <TableRow 
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => setExpandedRow(expandedRow === submission.id ? null : submission.id)}
                    >
                      <TableCell>
                        <div className="space-y-1">
                          <p className="font-medium text-gray-900">{submission.fullName}</p>
                          <div className="flex items-center text-sm text-gray-500">
                            <Mail className="h-3 w-3 mr-1" />
                            {submission.email}
                          </div>
                          {submission.phoneNumber && (
                            <div className="flex items-center text-sm text-gray-500">
                              <Phone className="h-3 w-3 mr-1" />
                              {submission.phoneNumber}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary" className={`${formTypeColors[submission.formType as FormType]} text-white`}>
                          {formTypeLabels[submission.formType as FormType]}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center text-sm text-gray-500">
                          <CalendarDays className="h-3 w-3 mr-1" />
                          {format(new Date(submission.submittedAt), 'MMM dd, yyyy')}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {submission.emailSent ? (
                            <div className="flex items-center text-green-600">
                              <CheckCircle className="h-4 w-4 mr-1" />
                              <span className="text-sm">Sent</span>
                            </div>
                          ) : (
                            <div className="flex items-center text-red-600">
                              <XCircle className="h-4 w-4 mr-1" />
                              <span className="text-sm">Pending</span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {submission.companyName ? (
                          <div className="flex items-center text-sm text-gray-600">
                            <Building className="h-3 w-3 mr-1" />
                            {submission.companyName}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center justify-center">
                          <ChevronRight 
                            className={`h-4 w-4 transition-transform ${expandedRow === submission.id ? 'rotate-90' : ''}`} 
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                    {expandedRow === submission.id && (
                      <TableRow>
                        <TableCell colSpan={6} className="bg-white p-0">
                          <div className="p-4">
                            <h4 className="font-medium text-gray-900 mb-3 text-sm">
                              {formTypeLabels[submission.formType as FormType]} Details
                            </h4>
                            <div className="bg-white rounded-md border overflow-hidden">
                              <FormDetails submission={submission} />
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 