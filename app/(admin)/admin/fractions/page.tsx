"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
  Percent,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function FractionsPage() {
  // Mock data - replace with actual API call
  const transactions = [
    {
      id: 1,
      vehicle: "Toyota Fortuner",
      group: "Family SUV",
      user: "Sipho Nkosi",
      type: "Purchase",
      fractionSize: "20%",
      amount: 45000,
      date: "2024-01-15",
      status: "Completed",
    },
    {
      id: 2,
      vehicle: "VW Polo",
      group: "Weekend Getaway",
      user: "Lerato Mabaso",
      type: "Sale",
      fractionSize: "15%",
      amount: 32000,
      date: "2024-01-20",
      status: "Pending",
    },
    {
      id: 3,
      vehicle: "Honda Civic",
      group: "Work Commute",
      user: "<PERSON> Both<PERSON>",
      type: "Purchase",
      fractionSize: "25%",
      amount: 55000,
      date: "2024-01-25",
      status: "Completed",
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Fractional Transactions
        </h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search transactions..."
              className="pl-10 w-[300px]"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Transactions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold">42</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Purchases</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <ArrowUpRight className="h-5 w-5 text-green-600" />
              <span className="text-2xl font-bold text-green-600">28</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Sales</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <ArrowDownRight className="h-5 w-5 text-blue-600" />
              <span className="text-2xl font-bold text-blue-600">14</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Transaction Volume</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-[#009639]">R 1.2M</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vehicle</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Fraction Size</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>
                    <Link
                      href={`/admin/vehicles/${transaction.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {transaction.vehicle}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/groups/${transaction.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {transaction.group}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/users/${transaction.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {transaction.user}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        transaction.type === "Purchase"
                          ? "bg-green-100 text-green-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {transaction.type}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Percent className="h-3 w-3 text-gray-500" />
                      {transaction.fractionSize}
                    </div>
                  </TableCell>
                  <TableCell>R {transaction.amount.toLocaleString()}</TableCell>
                  <TableCell>{transaction.date}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        transaction.status === "Completed"
                          ? "bg-green-100 text-green-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {transaction.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/fractions/${transaction.id}`}
                      className="text-[#009639] hover:text-[#007A2F] font-medium"
                    >
                      View Details
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
