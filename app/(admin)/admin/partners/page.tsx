"use client";

import React, { useState } from "react";
import {
  Search,
  Plus,
  Filter,
  ChevronDown,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Building,
  Phone,
  Mail,
  MapPin,
  Star,
  FileText,
  Shield,
  Car,
  Wrench,
  Briefcase,
  DollarSign,
} from "lucide-react";
import Image from "next/image";

// Mock partner data
const partnersData = [
  {
    id: 1,
    name: "SafeDrive Insurance",
    logo: "/placeholder.svg?height=60&width=60",
    type: "Insurance",
    location: "Cape Town",
    contactPerson: "<PERSON>",
    email: "<EMAIL>",
    phone: "+27 123 456 789",
    rating: 4.8,
    contractStatus: "Active",
    contractExpiry: "31/12/2025",
  },
  {
    id: 2,
    name: "QuickFix Mechanics",
    logo: "/placeholder.svg?height=60&width=60",
    type: "Mechanical",
    location: "Johannesburg",
    contactPerson: "Sarah Johnson",
    email: "<EMAIL>",
    phone: "+27 987 654 321",
    rating: 4.5,
    contractStatus: "Active",
    contractExpiry: "15/11/2025",
  },
  {
    id: 3,
    name: "LegalShield Services",
    logo: "/placeholder.svg?height=60&width=60",
    type: "Legal",
    location: "Durban",
    contactPerson: "<PERSON>",
    email: "<EMAIL>",
    phone: "+27 456 789 123",
    rating: 4.7,
    contractStatus: "Active",
    contractExpiry: "20/10/2025",
  },
  {
    id: 4,
    name: "CarFinance Solutions",
    logo: "/placeholder.svg?height=60&width=60",
    type: "Financial",
    location: "Pretoria",
    contactPerson: "Emily Davis",
    email: "<EMAIL>",
    phone: "+27 789 123 456",
    rating: 4.2,
    contractStatus: "Pending Renewal",
    contractExpiry: "28/02/2025",
  },
  {
    id: 5,
    name: "AutoCare Specialists",
    logo: "/placeholder.svg?height=60&width=60",
    type: "Mechanical",
    location: "Bloemfontein",
    contactPerson: "David Wilson",
    email: "<EMAIL>",
    phone: "+27 321 654 987",
    rating: 4.0,
    contractStatus: "Active",
    contractExpiry: "10/09/2025",
  },
  {
    id: 6,
    name: "SecureWheel Insurance",
    logo: "/placeholder.svg?height=60&width=60",
    type: "Insurance",
    location: "Cape Town",
    contactPerson: "Lisa Taylor",
    email: "<EMAIL>",
    phone: "+27 654 987 321",
    rating: 4.6,
    contractStatus: "Active",
    contractExpiry: "15/08/2025",
  },
  {
    id: 7,
    name: "LegalDrive Attorneys",
    logo: "/placeholder.svg?height=60&width=60",
    type: "Legal",
    location: "Johannesburg",
    contactPerson: "Robert Johnson",
    email: "<EMAIL>",
    phone: "+27 234 567 890",
    rating: 4.3,
    contractStatus: "Expired",
    contractExpiry: "15/01/2025",
  },
  {
    id: 8,
    name: "VehicleFinance Bank",
    logo: "/placeholder.svg?height=60&width=60",
    type: "Financial",
    location: "Durban",
    contactPerson: "Amanda Miller",
    email: "<EMAIL>",
    phone: "+27 567 890 123",
    rating: 4.4,
    contractStatus: "Active",
    contractExpiry: "20/07/2025",
  },
];

export default function PartnerManagement() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPartners, setSelectedPartners] = useState<number[]>([]);
  const [typeFilter, setTypeFilter] = useState("All");
  const [statusFilter, setStatusFilter] = useState("All");

  // Handle select all partners
  const handleSelectAll = () => {
    if (selectedPartners.length === partnersData.length) {
      setSelectedPartners([]);
    } else {
      setSelectedPartners(partnersData.map((partner) => partner.id));
    }
  };

  // Handle select individual partner
  const handleSelectPartner = (partnerId: number) => {
    if (selectedPartners.includes(partnerId)) {
      setSelectedPartners(selectedPartners.filter((id) => id !== partnerId));
    } else {
      setSelectedPartners([...selectedPartners, partnerId]);
    }
  };

  // Filter partners based on search query and filters
  const filteredPartners = partnersData.filter((partner) => {
    const matchesSearch =
      partner.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      partner.contactPerson.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = typeFilter === "All" || partner.type === typeFilter;
    const matchesStatus =
      statusFilter === "All" || partner.contractStatus === statusFilter;

    return matchesSearch && matchesType && matchesStatus;
  });

  // Get partner type icon
  const getPartnerTypeIcon = (type: string) => {
    switch (type) {
      case "Insurance":
        return <Shield size={16} className="mr-2 text-[#009639]" />;
      case "Mechanical":
        return <Wrench size={16} className="mr-2 text-[#007A2F]" />;
      case "Legal":
        return <Briefcase size={16} className="mr-2 text-[#009639]" />;
      case "Financial":
        return <DollarSign size={16} className="mr-2 text-[#007A2F]" />;
      default:
        return <Building size={16} className="mr-2 text-[#009639]" />;
    }
  };

  // Get contract status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-600";
      case "Pending Renewal":
        return "bg-yellow-100 text-yellow-600";
      case "Expired":
        return "bg-red-100 text-red-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  // Render star rating
  const renderRating = (rating: number) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    return (
      <div className="flex items-center">
        {Array.from({ length: 5 }).map((_, index) => (
          <Star
            key={index}
            size={14}
            className={
              index < fullStars
                ? "text-yellow-400 fill-yellow-400"
                : index === fullStars && hasHalfStar
                ? "text-yellow-400 fill-yellow-400"
                : "text-gray-300"
            }
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">{rating.toFixed(1)}</span>
      </div>
    );
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Partner Management</h1>
        <p className="text-gray-500">Manage service partners and contracts</p>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Service Partners</h2>

          <div className="flex items-center space-x-4">
            <button className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm">
              <Filter size={16} className="mr-2" />
              Filters
            </button>

            <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center">
              <Plus size={16} className="mr-2" />
              Add New Partner
            </button>
          </div>
        </div>

        <div className="flex flex-wrap gap-4 mb-6">
          <div className="relative">
            <Search
              size={18}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
            <input
              type="text"
              placeholder="Search partners..."
              className="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="relative">
            <select
              className="appearance-none pl-4 pr-10 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="All">All Types</option>
              <option value="Insurance">Insurance</option>
              <option value="Mechanical">Mechanical</option>
              <option value="Legal">Legal</option>
              <option value="Financial">Financial</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <ChevronDown size={16} className="text-gray-400" />
            </div>
          </div>

          <div className="relative">
            <select
              className="appearance-none pl-4 pr-10 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="All">All Statuses</option>
              <option value="Active">Active</option>
              <option value="Pending Renewal">Pending Renewal</option>
              <option value="Expired">Expired</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <ChevronDown size={16} className="text-gray-400" />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    className="rounded text-[#009639] focus:ring-[#009639]"
                    checked={selectedPartners.length === partnersData.length}
                    onChange={handleSelectAll}
                  />
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Partner
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Type
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Contact
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Location
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Rating
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Contract Status
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Expiry Date
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredPartners.map((partner) => (
                <tr
                  key={partner.id}
                  className="border-b border-gray-200 hover:bg-gray-50"
                >
                  <td className="px-4 py-4">
                    <input
                      type="checkbox"
                      className="rounded text-[#009639] focus:ring-[#009639]"
                      checked={selectedPartners.includes(partner.id)}
                      onChange={() => handleSelectPartner(partner.id)}
                    />
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center mr-3">
                        <Building size={20} className="text-gray-500" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {partner.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {partner.contactPerson}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center">
                      {getPartnerTypeIcon(partner.type)}
                      <span className="text-sm text-gray-600">
                        {partner.type}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-600">
                      <div className="flex items-center mb-1">
                        <Mail size={14} className="mr-1 text-gray-400" />
                        <span>{partner.email}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone size={14} className="mr-1 text-gray-400" />
                        <span>{partner.phone}</span>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin size={14} className="mr-1 text-gray-400" />
                      {partner.location}
                    </div>
                  </td>
                  <td className="px-4 py-4">{renderRating(partner.rating)}</td>
                  <td className="px-4 py-4">
                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(
                        partner.contractStatus
                      )}`}
                    >
                      {partner.contractStatus}
                    </span>
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar size={14} className="mr-1 text-gray-400" />
                      {partner.contractExpiry}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex space-x-2">
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <Eye size={16} />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <FileText size={16} />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <Edit size={16} />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <MoreHorizontal size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-500">
            Showing 1 to {filteredPartners.length} of {partnersData.length}{" "}
            entries
          </div>

          <div className="flex items-center space-x-1">
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
              &lt;
            </button>
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm bg-[#009639] text-white">
              1
            </button>
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
              &gt;
            </button>
          </div>

          <div className="flex items-center">
            <span className="text-sm text-gray-500 mr-2">Show</span>
            <select className="appearance-none pl-2 pr-6 py-1 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent">
              <option>8</option>
              <option>16</option>
              <option>24</option>
              <option>32</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
}
