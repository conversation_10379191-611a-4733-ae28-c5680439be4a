"use client";

import React, { useState } from "react";
import {
  Bar<PERSON>hart3,
  Users,
  MoreVertical,
  Search,
  Car,
  Calendar,
  Filter,
  ChevronDown,
  ArrowRight,
  Download,
  DollarSign,
  TrendingUp,
  ShieldCheck,
  FileText,
  Bell,
  AlertCircle,
  CheckCircle,
  Clock,
  UserPlus,
  Activity,
} from "lucide-react";
import {
  BarChart,
  Bar,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from "recharts";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Mock data for charts
const platformOverviewData = [
  { name: "Active Users", value: 1247, color: "#009639", icon: <Users size={18} /> },
  { name: "Active Groups", value: 86, color: "#007A2F", icon: <Users size={18} /> },
  { name: "Vehicles", value: 123, color: "#FFD700", icon: <Car size={18} /> },
  { name: "New Signups (Today)", value: 24, color: "#20c997", icon: <UserPlus size={18} /> },
];

const weeklySignupData = [
  { name: "Mon", value: 24 },
  { name: "Tue", value: 36 },
  { name: "Wed", value: 42 },
  { name: "Thu", value: 37 },
  { name: "Fri", value: 45 },
  { name: "Sat", value: 32 },
  { name: "Sun", value: 28 },
];

const monthlySignupData = [
  { name: "Jan", value: 320 },
  { name: "Feb", value: 350 },
  { name: "Mar", value: 410 },
  { name: "Apr", value: 490 },
  { name: "May", value: 470 },
  { name: "Jun", value: 520 },
  { name: "Jul", value: 550 },
  { name: "Aug", value: 570 },
  { name: "Sep", value: 610 },
  { name: "Oct", value: 670 },
  { name: "Nov", value: 720 },
  { name: "Dec", value: 790 },
];

const revenueData = [
  { name: "Jan", value: 125000 },
  { name: "Feb", value: 135000 },
  { name: "Mar", value: 142000 },
  { name: "Apr", value: 150000 },
  { name: "May", value: 160000 },
  { name: "Jun", value: 168000 },
  { name: "Jul", value: 175000 },
  { name: "Aug", value: 172000 },
  { name: "Sep", value: 165000 },
  { name: "Oct", value: 158000 },
  { name: "Nov", value: 152000 },
  { name: "Dec", value: 145000 },
];

const userActivityData = [
  { name: "Mon", active: 780, new: 45 },
  { name: "Tue", active: 820, new: 52 },
  { name: "Wed", active: 860, new: 58 },
  { name: "Thu", active: 830, new: 48 },
  { name: "Fri", active: 850, new: 55 },
  { name: "Sat", active: 790, new: 42 },
  { name: "Sun", active: 760, new: 38 },
];

const vehicleTypeData = [
  { name: "Sedan", value: 35, color: "#009639" },
  { name: "SUV", value: 25, color: "#007A2F" },
  { name: "Hatchback", value: 20, color: "#FFD700" },
  { name: "Bakkie", value: 15, color: "#20c997" },
  { name: "Minibus", value: 5, color: "#6c757d" },
];

// Recent activity data
const recentActivityData = [
  {
    id: 1,
    type: "user_signup",
    user: "Thabo Molefe",
    time: "10 minutes ago",
    icon: <UserPlus size={16} className="text-green-500" />,
  },
  {
    id: 2,
    type: "group_created",
    user: "Sipho Nkosi",
    time: "25 minutes ago",
    icon: <Users size={16} className="text-blue-500" />,
  },
  {
    id: 3,
    type: "vehicle_added",
    user: "Lerato Mabaso",
    time: "1 hour ago",
    icon: <Car size={16} className="text-yellow-500" />,
  },
  {
    id: 4,
    type: "booking_completed",
    user: "Mandla Zulu",
    time: "2 hours ago",
    icon: <CheckCircle size={16} className="text-green-500" />,
  },
  {
    id: 5,
    type: "dispute_raised",
    user: "Nomsa Ndlovu",
    time: "3 hours ago",
    icon: <AlertCircle size={16} className="text-red-500" />,
  },
];

// System alerts data
const systemAlertsData = [
  {
    id: 1,
    title: "Server Load High",
    description: "Server load reached 85% capacity",
    severity: "warning",
    time: "15 minutes ago",
  },
  {
    id: 2,
    title: "Payment Gateway Issue",
    description: "Intermittent failures in payment processing",
    severity: "critical",
    time: "45 minutes ago",
  },
  {
    id: 3,
    title: "New User Spike",
    description: "Unusual spike in new user registrations",
    severity: "info",
    time: "2 hours ago",
  },
];

// Pending approvals data
const pendingApprovalsData = [
  {
    id: 1,
    type: "Vehicle Registration",
    user: "Johan Botha",
    time: "Submitted 2 hours ago",
    status: "pending",
  },
  {
    id: 2,
    type: "Group Formation",
    user: "Andile Ngcobo",
    time: "Submitted 5 hours ago",
    status: "pending",
  },
  {
    id: 3,
    type: "Partner Application",
    user: "Francois du Plessis",
    time: "Submitted 1 day ago",
    status: "pending",
  },
];

export default function AdminDashboard() {
  const [timeRange, setTimeRange] = useState("weekly");
  const [signupData, setSignupData] = useState(weeklySignupData);

  // Switch between weekly and monthly signup data
  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    if (range === "weekly") {
      setSignupData(weeklySignupData);
    } else {
      setSignupData(monthlySignupData);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">Dashboard</h1>

      {/* Platform Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {platformOverviewData.map((item, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500">{item.name}</p>
                  <p className="text-2xl font-bold mt-1">{item.value}</p>
                </div>
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: `${item.color}20` }}
                >
                  <div className="text-[#009639]">{item.icon}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Signups Chart */}
        <Card className="lg:col-span-2">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg flex items-center">
                <Users size={18} className="text-[#009639] mr-2" />
                User Signups
              </CardTitle>
              <Tabs value={timeRange} onValueChange={handleTimeRangeChange} className="w-auto">
                <TabsList className="h-8">
                  <TabsTrigger value="weekly" className="text-xs px-3 py-1">Weekly</TabsTrigger>
                  <TabsTrigger value="monthly" className="text-xs px-3 py-1">Monthly</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={signupData}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="value" fill="#009639" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Revenue Overview */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <DollarSign size={18} className="text-[#009639] mr-2" />
              Revenue Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <p className="text-sm text-gray-500">Total Revenue</p>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    +8.2%
                  </Badge>
                </div>
                <p className="text-2xl font-bold">R 1,845,000</p>
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-1">
                  <p className="text-sm text-gray-500">Monthly Revenue</p>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    +5.4%
                  </Badge>
                </div>
                <p className="text-xl font-bold">R 145,000</p>
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-1">
                  <p className="text-sm text-gray-500">Average Per Vehicle</p>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    +3.1%
                  </Badge>
                </div>
                <p className="text-xl font-bold">R 12,500</p>
              </div>
              
              <div className="h-[100px] mt-4">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={revenueData.slice(-6)}>
                    <defs>
                      <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#009639" stopOpacity={0.8} />
                        <stop offset="95%" stopColor="#009639" stopOpacity={0.1} />
                      </linearGradient>
                    </defs>
                    <Area
                      type="monotone"
                      dataKey="value"
                      stroke="#009639"
                      fill="url(#colorRevenue)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
              
              <Button variant="outline" className="w-full flex items-center justify-center gap-2">
                <Download size={14} />
                Download Report
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* User Activity */}
        <Card className="lg:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Activity size={18} className="text-[#009639] mr-2" />
              User Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={userActivityData}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="active"
                    stroke="#009639"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="new"
                    stroke="#FFD700"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Types */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Car size={18} className="text-[#009639] mr-2" />
              Vehicle Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full flex items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={vehicleTypeData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {vehicleTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="lg:col-span-2">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg flex items-center">
                <Clock size={18} className="text-[#009639] mr-2" />
                Recent Activity
              </CardTitle>
              <Button variant="ghost" size="sm" className="text-[#009639]">
                View All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivityData.map((activity) => (
                <div key={activity.id} className="flex items-start p-3 border border-gray-100 rounded-lg">
                  <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                    {activity.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium">
                          {activity.user}{" "}
                          <span className="text-gray-500 font-normal">
                            {activity.type === "user_signup"
                              ? "joined the platform"
                              : activity.type === "group_created"
                              ? "created a new group"
                              : activity.type === "vehicle_added"
                              ? "added a new vehicle"
                              : activity.type === "booking_completed"
                              ? "completed a booking"
                              : "raised a dispute"}
                          </span>
                        </p>
                      </div>
                      <span className="text-xs text-gray-400">{activity.time}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* System Alerts */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Bell size={18} className="text-[#009639] mr-2" />
              System Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {systemAlertsData.map((alert) => (
                <div key={alert.id} className="p-3 border border-gray-100 rounded-lg">
                  <div className="flex justify-between items-start mb-1">
                    <p className="text-sm font-medium">{alert.title}</p>
                    <Badge
                      variant="outline"
                      className={
                        alert.severity === "critical"
                          ? "bg-red-50 text-red-700 border-red-200"
                          : alert.severity === "warning"
                          ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                          : "bg-blue-50 text-blue-700 border-blue-200"
                      }
                    >
                      {alert.severity}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-500 mb-1">{alert.description}</p>
                  <p className="text-xs text-gray-400">{alert.time}</p>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4">
              Resolve All Alerts
            </Button>
          </CardContent>
        </Card>

        {/* Pending Approvals */}
        <Card className="lg:col-span-2">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg flex items-center">
                <Clock size={18} className="text-[#009639] mr-2" />
                Pending Approvals
              </CardTitle>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/admin/approvals" className="text-[#009639] flex items-center">
                  View All <ArrowRight size={14} className="ml-1" />
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pendingApprovalsData.map((approval) => (
                <div key={approval.id} className="border border-gray-100 rounded-lg p-4">
                  <div className="flex justify-between">
                    <p className="text-sm font-medium">{approval.type}</p>
                    <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                      Pending
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">By {approval.user}</p>
                  <p className="text-xs text-gray-400 mt-1">{approval.time}</p>
                  <div className="flex space-x-2 mt-3">
                    <Button variant="default" size="sm" className="bg-[#009639] hover:bg-[#007A2F]">
                      Approve
                    </Button>
                    <Button variant="outline" size="sm">
                      Reject
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Links */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <TrendingUp size={18} className="text-[#009639] mr-2" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <Button variant="outline" asChild className="h-auto py-4 flex flex-col items-center justify-center">
                <Link href="/admin/users">
                  <Users size={24} className="text-[#009639] mb-2" />
                  <p className="text-sm font-medium">Manage Users</p>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-auto py-4 flex flex-col items-center justify-center">
                <Link href="/admin/groups">
                  <Users size={24} className="text-[#009639] mb-2" />
                  <p className="text-sm font-medium">Manage Groups</p>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-auto py-4 flex flex-col items-center justify-center">
                <Link href="/admin/vehicles">
                  <Car size={24} className="text-[#009639] mb-2" />
                  <p className="text-sm font-medium">Manage Vehicles</p>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-auto py-4 flex flex-col items-center justify-center">
                <Link href="/admin/analytics">
                  <BarChart3 size={24} className="text-[#009639] mb-2" />
                  <p className="text-sm font-medium">View Analytics</p>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-auto py-4 flex flex-col items-center justify-center">
                <Link href="/admin/settings">
                  <ShieldCheck size={24} className="text-[#009639] mb-2" />
                  <p className="text-sm font-medium">Security Settings</p>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-auto py-4 flex flex-col items-center justify-center">
                <Link href="/admin/help">
                  <FileText size={24} className="text-[#009639] mb-2" />
                  <p className="text-sm font-medium">Help Center</p>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
