"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Search, 
  Filter, 
  Plus, 
  Car, 
  Users, 
  DollarSign, 
  BarChart2, 
  ShoppingCart, 
  TrendingUp, 
  Clock, 
  Percent, 
  ArrowUpRight,
  CheckCircle,
  AlertTriangle,
  Activity
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function VehiclesPage() {
  const [activeTab, setActiveTab] = useState("group");

  // Mock data - replace with actual API call
  const groupVehicles = [
    {
      id: 1,
      name: "Toyota Fortuner",
      group: "Family Car Share",
      status: "Active",
      ownership: "Full",
      maintenanceStatus: "Up to date",
      lastService: "2024-01-10",
    },
    {
      id: 2,
      name: "VW Polo",
      group: "Business Fleet",
      status: "Pending Handover",
      ownership: "Full",
      maintenanceStatus: "Service Due",
      lastService: "2023-12-15",
    },
    {
      id: 3,
      name: "Honda CR-V",
      group: "Neighborhood Share",
      status: "Active",
      ownership: "Full",
      maintenanceStatus: "Up to date",
      lastService: "2024-02-05",
    },
    {
      id: 4,
      name: "Hyundai i10",
      group: "City Commuters",
      status: "Active",
      ownership: "Full",
      maintenanceStatus: "Up to date",
      lastService: "2024-01-22",
    },
  ];

  // Mock data for marketplace vehicles
  const marketplaceVehicles = [
    {
      id: 5,
      name: "BMW X3",
      listingDate: "2024-03-15",
      status: "Available",
      ownership: "Fractional",
      percentageSold: 45,
      price: "R 650,000",
      sharePrice: "R 65,000",
      interestLevel: "High",
    },
    {
      id: 6,
      name: "Mercedes C-Class",
      listingDate: "2024-03-01",
      status: "Selling Fast",
      ownership: "Fractional",
      percentageSold: 75,
      price: "R 720,000",
      sharePrice: "R 72,000",
      interestLevel: "Very High",
    },
    {
      id: 7,
      name: "Audi Q5",
      listingDate: "2024-03-10",
      status: "Available",
      ownership: "Fractional",
      percentageSold: 30,
      price: "R 680,000",
      sharePrice: "R 68,000",
      interestLevel: "Medium",
    },
    {
      id: 8,
      name: "Toyota Land Cruiser",
      listingDate: "2024-02-28",
      status: "Almost Sold",
      ownership: "Fractional",
      percentageSold: 90,
      price: "R 950,000",
      sharePrice: "R 95,000",
      interestLevel: "Very High",
    },
  ];

  // Marketplace metrics
  const marketplaceMetrics = {
    totalListings: marketplaceVehicles.length,
    averageSellTime: "18 days",
    averageInterestRate: "High",
    totalValue: "R 3,000,000",
    averageSharesSold: "60%",
    monthlyGrowth: "+15%",
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Vehicle Management</h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search vehicles..."
              className="pl-10 w-[300px]"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Vehicle
          </Button>
        </div>
      </div>

      <Tabs defaultValue="group" className="mb-6" onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="group" className="px-6">
            <Users className="h-4 w-4 mr-2" />
            Group Vehicles
          </TabsTrigger>
          <TabsTrigger value="marketplace" className="px-6">
            <ShoppingCart className="h-4 w-4 mr-2" />
            Marketplace Vehicles
          </TabsTrigger>
        </TabsList>

        <TabsContent value="group">
          {/* Group Vehicles Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Total Group Vehicles</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Car className="h-5 w-5 text-[#009639]" />
                  <span className="text-2xl font-bold">{groupVehicles.length}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Active</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-green-600">
                    {groupVehicles.filter(v => v.status === "Active").length}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Pending Handover</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-yellow-600">
                    {groupVehicles.filter(v => v.status === "Pending Handover").length}
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Maintenance Due</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-red-600">
                    {groupVehicles.filter(v => v.maintenanceStatus === "Service Due").length}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Group Vehicles Table */}
          <Card>
            <CardHeader>
              <CardTitle>Group Vehicles</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vehicle</TableHead>
                    <TableHead>Group</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Ownership</TableHead>
                    <TableHead>Maintenance</TableHead>
                    <TableHead>Last Service</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {groupVehicles.map((vehicle) => (
                    <TableRow key={vehicle.id}>
                      <TableCell className="font-medium">{vehicle.name}</TableCell>
                      <TableCell>
                        <Link
                          href={`/admin/groups/${vehicle.id}`}
                          className="text-[#009639] hover:text-[#007A2F]"
                        >
                          {vehicle.group}
                        </Link>
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            vehicle.status === "Active"
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {vehicle.status}
                        </span>
                      </TableCell>
                      <TableCell>{vehicle.ownership}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            vehicle.maintenanceStatus === "Up to date"
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {vehicle.maintenanceStatus}
                        </span>
                      </TableCell>
                      <TableCell>{vehicle.lastService}</TableCell>
                      <TableCell>
                        <Link
                          href={`/admin/vehicles/${vehicle.id}`}
                          className="text-[#009639] hover:text-[#007A2F] font-medium"
                        >
                          View Details
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="marketplace">
          {/* Marketplace Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Total Listings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <ShoppingCart className="h-5 w-5 text-[#009639]" />
                  <span className="text-2xl font-bold">{marketplaceMetrics.totalListings}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Total Value</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-[#009639]" />
                  <span className="text-2xl font-bold">{marketplaceMetrics.totalValue}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Avg. Sell Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-[#009639]" />
                  <span className="text-2xl font-bold">{marketplaceMetrics.averageSellTime}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Monthly Growth</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-[#009639]" />
                  <span className="text-2xl font-bold text-green-600">{marketplaceMetrics.monthlyGrowth}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Marketplace Activity & Progress */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <Activity className="h-5 w-5 text-[#009639] mr-2" />
                  <CardTitle>Marketplace Activity</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                        <ArrowUpRight size={16} className="text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">New Listings</p>
                        <p className="text-xs text-gray-500">Last 7 days</p>
                      </div>
                    </div>
                    <span className="text-lg font-bold">3</span>
                  </div>
                  
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <Users size={16} className="text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">New Investors</p>
                        <p className="text-xs text-gray-500">Last 7 days</p>
                      </div>
                    </div>
                    <span className="text-lg font-bold">12</span>
                  </div>
                  
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                        <Percent size={16} className="text-yellow-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">Shares Sold</p>
                        <p className="text-xs text-gray-500">Last 7 days</p>
                      </div>
                    </div>
                    <span className="text-lg font-bold">24</span>
                  </div>
                  
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                        <CheckCircle size={16} className="text-purple-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">Completed Sales</p>
                        <p className="text-xs text-gray-500">Last 7 days</p>
                      </div>
                    </div>
                    <span className="text-lg font-bold">1</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="flex items-center">
                  <BarChart2 className="h-5 w-5 text-[#009639] mr-2" />
                  <CardTitle>Marketplace Progress</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Average Shares Sold</span>
                      <span className="text-sm font-medium text-green-600">{marketplaceMetrics.averageSharesSold}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-[#009639] h-2 rounded-full" 
                        style={{ width: marketplaceMetrics.averageSharesSold }}
                      ></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium">Interest Level</span>
                      <span className="text-sm font-medium text-green-600">{marketplaceMetrics.averageInterestRate}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-1/3 h-2 bg-[#009639] rounded-l-full"></div>
                      <div className="w-1/3 h-2 bg-[#20c997]"></div>
                      <div className="w-1/3 h-2 bg-[#e6ffe6] rounded-r-full"></div>
                    </div>
                    <div className="flex justify-between mt-1">
                      <span className="text-xs text-gray-500">Low</span>
                      <span className="text-xs text-gray-500">Medium</span>
                      <span className="text-xs text-gray-500">High</span>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-3">Listing Status Overview</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center mb-1">
                          <AlertTriangle size={14} className="text-yellow-600 mr-1" />
                          <span className="text-xs font-medium">Available</span>
                        </div>
                        <span className="text-lg font-bold">2</span>
                      </div>
                      
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center mb-1">
                          <TrendingUp size={14} className="text-blue-600 mr-1" />
                          <span className="text-xs font-medium">Selling Fast</span>
                        </div>
                        <span className="text-lg font-bold">1</span>
                      </div>
                      
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center mb-1">
                          <CheckCircle size={14} className="text-green-600 mr-1" />
                          <span className="text-xs font-medium">Almost Sold</span>
                        </div>
                        <span className="text-lg font-bold">1</span>
                      </div>
                      
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center mb-1">
                          <Clock size={14} className="text-gray-600 mr-1" />
                          <span className="text-xs font-medium">Pending</span>
                        </div>
                        <span className="text-lg font-bold">0</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Marketplace Vehicles Table */}
          <Card>
            <CardHeader>
              <CardTitle>Marketplace Vehicles</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vehicle</TableHead>
                    <TableHead>Listing Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Total Price</TableHead>
                    <TableHead>Share Price</TableHead>
                    <TableHead>Sold</TableHead>
                    <TableHead>Interest Level</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {marketplaceVehicles.map((vehicle) => (
                    <TableRow key={vehicle.id}>
                      <TableCell className="font-medium">{vehicle.name}</TableCell>
                      <TableCell>{vehicle.listingDate}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            vehicle.status === "Available"
                              ? "bg-blue-100 text-blue-800"
                              : vehicle.status === "Selling Fast"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-green-100 text-green-800"
                          }`}
                        >
                          {vehicle.status}
                        </span>
                      </TableCell>
                      <TableCell>{vehicle.price}</TableCell>
                      <TableCell>{vehicle.sharePrice}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                            <div
                              className="bg-[#009639] h-2 rounded-full"
                              style={{ width: `${vehicle.percentageSold}%` }}
                            ></div>
                          </div>
                          <span className="text-xs">{vehicle.percentageSold}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            vehicle.interestLevel === "High" || vehicle.interestLevel === "Very High"
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {vehicle.interestLevel}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Link
                          href={`/admin/vehicles/${vehicle.id}`}
                          className="text-[#009639] hover:text-[#007A2F] font-medium"
                        >
                          View Details
                        </Link>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
