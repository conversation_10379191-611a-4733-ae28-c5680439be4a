"use client";

import React from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Car,
  Users,
  Calendar,
  Activity,
  Wrench,
  Shield,
  FileText,
  DollarSign,
  Clock,
  MapPin,
  AlertTriangle,
  CheckCircle,
  User,
  Edit,
  Plus,
  Eye,
  Trash2,
  ArrowRight,
  Image as ImageIcon,
} from "lucide-react";
import Link from "next/link";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export default function VehicleDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const vehicleId = params.id;

  // Mock data - replace with actual API call
  const vehicle = {
    id: vehicleId,
    name: "Toyota Fortuner",
    model: "2.8 GD-6 4x4 VX AT",
    year: "2023",
    registration: "CA 123-456",
    vin: "JTMHV05J604123456",
    status: "Active",
    maintenanceStatus: "Up to date",
    lastService: "2024-01-10",
    nextService: "2024-07-10",
    ownership: "Fractional",
    group: {
      id: "1",
      name: "Family Car Share",
      members: 4,
    },
    location: "Cape Town",
    purchaseDate: "2023-05-15",
    purchasePrice: "R 750,000",
    currentValue: "R 720,000",
    mileage: "15,000",
    fuelType: "Diesel",
    transmission: "Automatic",
    color: "Graphite Grey",
    image: "/placeholder.svg?height=300&width=500",
    licensePlate: "CA 123-456",
    engine: "2.8L Turbo Diesel",
    seats: "7",
    registrationExpiry: "2025-05-14",
    insuranceExpiry: "2024-05-14",
    make: "Toyota",
    owners: [
      {
        id: 1,
        name: "Sipho Nkosi",
        fraction: "30%",
        since: "2023-05-15",
      },
      {
        id: 2,
        name: "Lerato Mabaso",
        fraction: "25%",
        since: "2023-05-15",
      },
      {
        id: 3,
        name: "Johan Botha",
        fraction: "25%",
        since: "2023-06-10",
      },
      {
        id: 4,
        name: "Thandi Ndlovu",
        fraction: "20%",
        since: "2023-07-22",
      },
    ],
    maintenanceHistory: [
      {
        id: 1,
        type: "Regular Service",
        date: "2024-01-10",
        cost: "3,500",
        provider: "Toyota Cape Town",
        description:
          "10,000 km service including oil change, filters, and inspection",
        status: "Completed",
        mileage: "10,000",
      },
      {
        id: 2,
        type: "Tire Replacement",
        date: "2023-09-15",
        cost: "8,200",
        provider: "Tiger Wheel & Tyre",
        description: "Replaced all four tires with Bridgestone Dueler H/T",
        status: "Completed",
        mileage: "7,500",
      },
      {
        id: 3,
        type: "Brake Inspection",
        date: "2023-07-20",
        cost: "1,200",
        provider: "Toyota Cape Town",
        description: "Inspection of brake system, minor adjustments",
        status: "Completed",
        mileage: "5,000",
      },
    ],
    bookingHistory: [
      {
        id: 1,
        user: "Sipho Nkosi",
        userId: "1",
        userName: "Sipho Nkosi",
        startDate: "2024-02-01",
        endDate: "2024-02-03",
        status: "Completed",
      },
      {
        id: 2,
        user: "Lerato Mabaso",
        userId: "2",
        userName: "Lerato Mabaso",
        startDate: "2024-02-10",
        endDate: "2024-02-12",
        status: "Upcoming",
      },
      {
        id: 3,
        user: "Johan Botha",
        userId: "3",
        userName: "Johan Botha",
        startDate: "2024-02-18",
        endDate: "2024-02-20",
        status: "Upcoming",
      },
    ],
    transactions: [
      {
        id: 1,
        type: "Fraction Purchase",
        date: "2023-05-15",
        amount: "R 225,000",
        user: "Sipho Nkosi",
        description: "Initial 30% fraction purchase",
      },
      {
        id: 2,
        type: "Maintenance Fee",
        date: "2024-01-10",
        amount: "R 3,500",
        user: "Group Pool",
        description: "Regular service payment",
      },
      {
        id: 3,
        type: "Insurance Payment",
        date: "2024-01-05",
        amount: "R 2,800",
        user: "Group Pool",
        description: "Monthly insurance premium",
      },
    ],
    documents: [
      {
        id: 1,
        name: "Vehicle Registration",
        date: "2023-05-15",
        type: "Legal",
      },
      {
        id: 2,
        name: "Insurance Policy",
        date: "2023-05-15",
        type: "Insurance",
      },
      {
        id: 3,
        name: "Service History",
        date: "2024-01-10",
        type: "Maintenance",
      },
    ],
    images: [
      "/placeholder.svg?height=300&width=500",
      "/placeholder.svg?height=300&width=500",
      "/placeholder.svg?height=300&width=500",
      "/placeholder.svg?height=300&width=500",
    ],
    notes: [
      {
        id: 1,
        title: "Maintenance Reminder",
        date: "2024-01-15",
        content:
          "Next service due in July 2024. Remember to schedule appointment at Toyota Cape Town.",
        addedBy: "System",
      },
      {
        id: 2,
        title: "Tire Pressure Check",
        date: "2024-02-01",
        content:
          "Checked all tire pressures. Front: 32 PSI, Rear: 35 PSI. All within recommended range.",
        addedBy: "Sipho Nkosi",
      },
    ],
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Pending Handover":
        return "bg-yellow-100 text-yellow-800";
      case "Maintenance":
        return "bg-orange-100 text-orange-800";
      case "Inactive":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get maintenance status badge
  const getMaintenanceBadge = (status: string) => {
    switch (status) {
      case "Up to date":
        return {
          bg: "bg-green-100",
          text: "text-green-800",
          icon: <CheckCircle size={14} className="mr-1" />,
        };
      case "Service Due":
        return {
          bg: "bg-yellow-100",
          text: "text-yellow-800",
          icon: <AlertTriangle size={14} className="mr-1" />,
        };
      case "Overdue":
        return {
          bg: "bg-red-100",
          text: "text-red-800",
          icon: <AlertTriangle size={14} className="mr-1" />,
        };
      default:
        return {
          bg: "bg-gray-100",
          text: "text-gray-800",
          icon: null,
        };
    }
  };

  // Get transaction type badge
  const getTransactionTypeBadge = (type: string) => {
    switch (type) {
      case "Fraction Purchase":
        return "bg-green-100 text-green-800";
      case "Fraction Sale":
        return "bg-blue-100 text-blue-800";
      case "Maintenance Fee":
        return "bg-orange-100 text-orange-800";
      case "Insurance Payment":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Get booking status badge
  const getBookingStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800";
      case "Upcoming":
        return "bg-blue-100 text-blue-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="p-6">
      <Button
        variant="ghost"
        className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
        onClick={() => router.back()}
      >
        <ArrowLeft size={16} className="mr-2" />
        Back to Vehicles
      </Button>

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">{vehicle.name}</h1>
          <p className="text-gray-500">Vehicle ID: {vehicleId}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <Edit size={16} />
            Edit Vehicle
          </Button>
          <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
            <Wrench size={16} />
            Schedule Maintenance
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Vehicle Status */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Car size={18} className="text-[#009639] mr-2" />
              Vehicle Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Status:</span>
                <span
                  className={`px-2 py-1 rounded-full text-xs ${getStatusBadge(
                    vehicle.status
                  )}`}
                >
                  {vehicle.status}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">
                  Ownership:
                </span>
                <span className="text-sm font-medium">{vehicle.ownership}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">
                  Maintenance:
                </span>
                <span
                  className={`px-2 py-1 rounded-full text-xs flex items-center ${getMaintenanceBadge(
                    vehicle.maintenanceStatus
                  ).bg} ${getMaintenanceBadge(vehicle.maintenanceStatus).text}`}
                >
                  {getMaintenanceBadge(vehicle.maintenanceStatus).icon}
                  {vehicle.maintenanceStatus}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Group Information */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Users size={18} className="text-[#009639] mr-2" />
              Group Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Group:</span>
                <Link
                  href={`/admin/groups/${vehicle.group.id}`}
                  className="text-sm font-medium text-[#009639] hover:text-[#007A2F]"
                >
                  {vehicle.group.name}
                </Link>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Members:</span>
                <span className="text-sm font-medium">{vehicle.group.members}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Location:</span>
                <span className="text-sm font-medium">{vehicle.location}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Details */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <FileText size={18} className="text-[#009639] mr-2" />
              Vehicle Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">
                  Make & Model:
                </span>
                <span className="text-sm font-medium">
                  {vehicle.make} {vehicle.model}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Year:</span>
                <span className="text-sm font-medium">{vehicle.year}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">
                  License Plate:
                </span>
                <span className="text-sm font-medium">{vehicle.licensePlate}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="details" className="mb-6">
        <TabsList className="mb-4">
          <TabsTrigger value="details" className="px-4">
            <FileText size={16} className="mr-2" />
            Details
          </TabsTrigger>
          <TabsTrigger value="maintenance" className="px-4">
            <Wrench size={16} className="mr-2" />
            Maintenance
          </TabsTrigger>
          <TabsTrigger value="bookings" className="px-4">
            <Calendar size={16} className="mr-2" />
            Bookings
          </TabsTrigger>
          <TabsTrigger value="images" className="px-4">
            <ImageIcon size={16} className="mr-2" />
            Images
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Vehicle Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-md font-semibold mb-3">Basic Information</h3>
                  <div className="space-y-3">
                    <div className="flex">
                      <span className="text-sm font-medium w-32">VIN:</span>
                      <span className="text-sm text-gray-600">{vehicle.vin}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Engine:</span>
                      <span className="text-sm text-gray-600">{vehicle.engine}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Transmission:</span>
                      <span className="text-sm text-gray-600">{vehicle.transmission}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Color:</span>
                      <span className="text-sm text-gray-600">{vehicle.color}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Mileage:</span>
                      <span className="text-sm text-gray-600">{vehicle.mileage} km</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-md font-semibold mb-3">Additional Details</h3>
                  <div className="space-y-3">
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Fuel Type:</span>
                      <span className="text-sm text-gray-600">{vehicle.fuelType}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Seats:</span>
                      <span className="text-sm text-gray-600">{vehicle.seats}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Registration:</span>
                      <span className="text-sm text-gray-600">
                        Valid until {vehicle.registrationExpiry}
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Insurance:</span>
                      <span className="text-sm text-gray-600">
                        Valid until {vehicle.insuranceExpiry}
                      </span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Purchase Date:</span>
                      <span className="text-sm text-gray-600">{vehicle.purchaseDate}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Maintenance History</CardTitle>
                <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
                  <Plus size={16} />
                  Add Maintenance Record
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Mileage</TableHead>
                    <TableHead>Cost</TableHead>
                    <TableHead>Provider</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {vehicle.maintenanceHistory.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>{record.date}</TableCell>
                      <TableCell>{record.type}</TableCell>
                      <TableCell>{record.description}</TableCell>
                      <TableCell>{record.mileage} km</TableCell>
                      <TableCell>R {record.cost}</TableCell>
                      <TableCell>{record.provider}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Eye size={16} className="text-gray-500" />
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Edit size={16} className="text-gray-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bookings">
          <Card>
            <CardHeader>
              <CardTitle>Booking History</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Booking ID</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {vehicle.bookingHistory.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell>#{booking.id}</TableCell>
                      <TableCell>
                        <Link
                          href={`/admin/users/${booking.userId}`}
                          className="text-[#009639] hover:text-[#007A2F]"
                        >
                          {booking.userName}
                        </Link>
                      </TableCell>
                      <TableCell>{booking.startDate}</TableCell>
                      <TableCell>{booking.endDate}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${getBookingStatusBadge(
                            booking.status
                          )}`}
                        >
                          {booking.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm" className="h-8 p-0">
                          <Link
                            href={`/admin/bookings/${booking.id}`}
                            className="flex items-center text-[#009639]"
                          >
                            View <ArrowRight size={14} className="ml-1" />
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="images">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Vehicle Images</CardTitle>
                <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
                  <Plus size={16} />
                  Upload Image
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {vehicle.images.map((image: string, index: number) => (
                  <div key={index} className="relative group rounded-lg overflow-hidden border border-gray-200">
                    <img
                      src={image}
                      alt={`${vehicle.name} - Image ${index + 1}`}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 bg-white text-gray-700">
                          <Eye size={16} />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 bg-white text-gray-700">
                          <Edit size={16} />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 bg-white text-red-500">
                          <Trash2 size={16} />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                <div className="border-2 border-dashed border-gray-200 rounded-lg flex items-center justify-center h-48 cursor-pointer hover:bg-gray-50">
                  <div className="flex flex-col items-center text-gray-400">
                    <Plus size={24} className="mb-2" />
                    <span className="text-sm">Add Image</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Vehicle Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {vehicle.notes.map((note: any) => (
              <div key={note.id} className="p-4 border border-gray-100 rounded-lg">
                <div className="flex justify-between mb-2">
                  <span className="text-sm font-medium">{note.title}</span>
                  <span className="text-xs text-gray-500">{note.date}</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">{note.content}</p>
                <div className="flex items-center text-xs text-gray-500">
                  <User size={12} className="mr-1" />
                  Added by {note.addedBy}
                </div>
              </div>
            ))}
            <div className="flex justify-end">
              <Button variant="outline" className="flex items-center gap-2">
                <Plus size={16} />
                Add Note
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
