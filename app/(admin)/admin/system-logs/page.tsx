"use client";

import React, { useState } from "react";
import {
  Search,
  Filter,
  Download,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  RefreshCw,
} from "lucide-react";

interface Log {
  id: string;
  timestamp: string;
  level: "info" | "warning" | "error" | "success";
  message: string;
  source: string;
  details: string;
}

const mockLogs: Log[] = [
  {
    id: "1",
    timestamp: "2024-01-20 10:30:15",
    level: "error",
    message: "Failed to process payment transaction",
    source: "Payment Service",
    details: "Transaction ID: 1234567890",
  },
  {
    id: "2",
    timestamp: "2024-01-20 10:29:45",
    level: "success",
    message: "New user registration completed",
    source: "Auth Service",
    details: "User ID: USER_123",
  },
  {
    id: "3",
    timestamp: "2024-01-20 10:28:30",
    level: "warning",
    message: "High CPU usage detected",
    source: "System Monitor",
    details: "CPU Usage: 85%",
  },
  {
    id: "4",
    timestamp: "2024-01-20 10:27:15",
    level: "info",
    message: "Scheduled maintenance started",
    source: "Maintenance Service",
    details: "Maintenance ID: MAINT_456",
  },
];

export default function SystemLogs() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedLevel, setSelectedLevel] = useState<string>("all");

  const getLevelIcon = (level: string) => {
    switch (level) {
      case "error":
        return <XCircle size={18} className="text-red-500" />;
      case "warning":
        return <AlertTriangle size={18} className="text-yellow-500" />;
      case "info":
        return <Info size={18} className="text-blue-500" />;
      case "success":
        return <CheckCircle size={18} className="text-green-500" />;
      default:
        return null;
    }
  };

  const getLevelBadgeColor = (level: string) => {
    switch (level) {
      case "error":
        return "bg-red-100 text-red-700";
      case "warning":
        return "bg-yellow-100 text-yellow-700";
      case "info":
        return "bg-blue-100 text-blue-700";
      case "success":
        return "bg-green-100 text-green-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const filteredLogs = mockLogs.filter((log) => {
    const matchesSearch =
      log.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.source.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.details.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesLevel = selectedLevel === "all" || log.level === selectedLevel;

    return matchesSearch && matchesLevel;
  });

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">System Logs</h1>
        <p className="text-gray-500">Monitor system activities and events</p>
      </div>

      <div className="bg-white rounded-xl shadow-sm p-6">
        {/* Controls */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div className="flex-1 w-full md:w-auto">
            <div className="relative">
              <Search
                size={18}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                placeholder="Search logs..."
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="flex items-center space-x-4 w-full md:w-auto">
            <div className="relative flex-1 md:flex-none">
              <select
                className="w-full md:w-auto appearance-none pl-4 pr-10 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
              >
                <option value="all">All Levels</option>
                <option value="error">Error</option>
                <option value="warning">Warning</option>
                <option value="info">Info</option>
                <option value="success">Success</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                <Filter size={16} className="text-gray-400" />
              </div>
            </div>

            <button className="flex items-center space-x-2 px-4 py-2 border border-gray-200 rounded-md text-sm text-gray-600 hover:bg-gray-50">
              <Download size={16} />
              <span>Export</span>
            </button>

            <button className="flex items-center space-x-2 px-4 py-2 border border-gray-200 rounded-md text-sm text-gray-600 hover:bg-gray-50">
              <RefreshCw size={16} />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Logs Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Timestamp
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Level
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Message
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Source
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Details
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredLogs.map((log) => (
                <tr
                  key={log.id}
                  className="border-b border-gray-200 hover:bg-gray-50"
                >
                  <td className="px-4 py-4 text-sm text-gray-500">
                    {log.timestamp}
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center space-x-2">
                      {getLevelIcon(log.level)}
                      <span
                        className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getLevelBadgeColor(
                          log.level
                        )}`}
                      >
                        {log.level.charAt(0).toUpperCase() + log.level.slice(1)}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-900">
                    {log.message}
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-500">
                    {log.source}
                  </td>
                  <td className="px-4 py-4 text-sm text-gray-500">
                    {log.details}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredLogs.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">
              No logs found matching your criteria
            </p>
          </div>
        )}

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-500">
            Showing {filteredLogs.length} entries
          </div>

          <div className="flex items-center space-x-1">
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
              &lt;
            </button>
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm bg-[#009639] text-white">
              1
            </button>
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
              &gt;
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
