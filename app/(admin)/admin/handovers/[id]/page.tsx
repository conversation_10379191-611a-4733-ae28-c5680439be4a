"use client";

import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Car,
  User,
  Calendar,
  Clock,
  MapPin,
  CheckCircle,
  XCircle,
  Camera,
  FileText,
  Edit,
  Trash2,
  MessageSquare,
  AlertTriangle,
  Info,
  Upload,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default function HandoverDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const handoverId = params.id;
  const [activeTab, setActiveTab] = useState("details");

  // Mock data - replace with actual API call
  const handover = {
    id: handoverId,
    vehicle: {
      id: 1,
      name: "Toyota Fortuner",
      model: "2.8 GD-6 4x4 VX AT",
      year: "2023",
      registration: "CA 123-456",
      image: "/placeholder.svg?height=200&width=300",
    },
    group: {
      id: 1,
      name: "Family SUV",
    },
    fromUser: {
      id: 1,
      name: "Sipho Nkosi",
      email: "<EMAIL>",
      phone: "+27 71 234 5678",
    },
    toUser: {
      id: 2,
      name: "Thabo Molefe",
      email: "<EMAIL>",
      phone: "+27 82 345 6789",
    },
    scheduledDate: "2024-02-15",
    scheduledTime: "14:00",
    status: "Scheduled",
    location: {
      name: "Cape Town Central",
      address: "123 Long Street, Cape Town",
      coordinates: {
        lat: -33.9249,
        lng: 18.4241,
      },
    },
    condition: {
      beforeHandover: {
        mileage: "15,230 km",
        fuelLevel: "3/4",
        exteriorCondition: "Good",
        interiorCondition: "Excellent",
        notes:
          "Small scratch on rear bumper, otherwise in excellent condition.",
        images: [
          "/placeholder.svg?height=150&width=200",
          "/placeholder.svg?height=150&width=200",
          "/placeholder.svg?height=150&width=200",
        ],
      },
      afterHandover: {
        mileage: "15,230 km",
        fuelLevel: "3/4",
        exteriorCondition: "Good",
        interiorCondition: "Excellent",
        notes: "No new damage or issues noted.",
        images: [
          "/placeholder.svg?height=150&width=200",
          "/placeholder.svg?height=150&width=200",
        ],
      },
    },
    documents: [
      {
        id: 1,
        name: "Handover Agreement",
        type: "PDF",
        uploadDate: "2024-02-15",
        uploadedBy: "Sipho Nkosi",
      },
      {
        id: 2,
        name: "Vehicle Inspection Report",
        type: "PDF",
        uploadDate: "2024-02-15",
        uploadedBy: "System",
      },
    ],
    timeline: [
      {
        id: 1,
        action: "Handover Scheduled",
        date: "2024-02-10",
        time: "09:15",
        by: "Sipho Nkosi",
      },
      {
        id: 2,
        action: "Reminder Sent",
        date: "2024-02-14",
        time: "14:00",
        by: "System",
      },
      {
        id: 3,
        action: "Check-in Confirmed",
        date: "2024-02-15",
        time: "13:45",
        by: "Sipho Nkosi",
      },
    ],
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800";
      case "Scheduled":
        return "bg-blue-100 text-blue-800";
      case "In Progress":
        return "bg-yellow-100 text-yellow-800";
      case "Cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="p-6">
      <Button
        variant="ghost"
        className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
        onClick={() => router.push("/admin/handovers")}
      >
        <ArrowLeft size={16} className="mr-2" />
        Back to Handovers
      </Button>

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Handover Details</h1>
          <p className="text-gray-500">ID: {handover.id}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <Edit size={16} />
            Edit
          </Button>
          <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
            <CheckCircle size={16} />
            Complete Handover
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Vehicle Information */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Car size={18} className="text-[#009639] mr-2" />
              Vehicle Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Vehicle:</span>
                <span className="text-sm font-medium">{handover.vehicle.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">License Plate:</span>
                <span className="text-sm font-medium">{handover.vehicle.registration}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Group:</span>
                <Link href={`/admin/groups/${handover.group.id}`} className="text-sm font-medium text-[#009639] hover:text-[#007A2F]">
                  {handover.group.name}
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* User Information */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <User size={18} className="text-[#009639] mr-2" />
              User Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">User:</span>
                <Link href={`/admin/users/${handover.fromUser.id}`} className="text-sm font-medium text-[#009639] hover:text-[#007A2F]">
                  {handover.fromUser.name}
                </Link>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Email:</span>
                <span className="text-sm font-medium">{handover.fromUser.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Phone:</span>
                <span className="text-sm font-medium">{handover.fromUser.phone}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Handover Status */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Clock size={18} className="text-[#009639] mr-2" />
              Handover Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Status:</span>
                <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadge(handover.status)}`}>
                  {handover.status}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Date:</span>
                <span className="text-sm font-medium">{handover.scheduledDate}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Time:</span>
                <span className="text-sm font-medium">{handover.scheduledTime}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="details" className="mb-6">
        <TabsList className="mb-4">
          <TabsTrigger value="details" className="px-4">
            <FileText size={16} className="mr-2" />
            Details
          </TabsTrigger>
          <TabsTrigger value="before" className="px-4">
            <Camera size={16} className="mr-2" />
            Before Handover
          </TabsTrigger>
          <TabsTrigger value="after" className="px-4">
            <Camera size={16} className="mr-2" />
            After Handover
          </TabsTrigger>
          <TabsTrigger value="documents" className="px-4">
            <FileText size={16} className="mr-2" />
            Documents
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Handover Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-md font-semibold mb-3">Location Information</h3>
                  <div className="space-y-3">
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Address:</span>
                      <span className="text-sm text-gray-600">{handover.location.address}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">City:</span>
                      <span className="text-sm text-gray-600">{handover.location.name}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Postal Code:</span>
                      <span className="text-sm text-gray-600">{handover.location.address}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-md font-semibold mb-3">Notes</h3>
                  <p className="text-sm text-gray-600">{handover.condition.beforeHandover.notes}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="before">
          <Card>
            <CardHeader>
              <CardTitle>Before Handover Condition</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-md font-semibold mb-3">Condition Report</h3>
                  <div className="space-y-3">
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Mileage:</span>
                      <span className="text-sm text-gray-600">{handover.condition.beforeHandover.mileage}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Fuel Level:</span>
                      <span className="text-sm text-gray-600">{handover.condition.beforeHandover.fuelLevel}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Cleanliness:</span>
                      <span className="text-sm text-gray-600">{handover.condition.beforeHandover.exteriorCondition}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Damages:</span>
                      <span className="text-sm text-gray-600">{handover.condition.beforeHandover.interiorCondition}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-md font-semibold mb-3">Images</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {handover.condition.beforeHandover.images.map((image, index) => (
                      <div key={index} className="border border-gray-200 rounded-md overflow-hidden">
                        <img src={image} alt={`Before handover ${index + 1}`} className="w-full h-32 object-cover" />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="after">
          <Card>
            <CardHeader>
              <CardTitle>After Handover Condition</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-md font-semibold mb-3">Condition Report</h3>
                  <div className="space-y-3">
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Mileage:</span>
                      <span className="text-sm text-gray-600">{handover.condition.afterHandover.mileage}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Fuel Level:</span>
                      <span className="text-sm text-gray-600">{handover.condition.afterHandover.fuelLevel}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Cleanliness:</span>
                      <span className="text-sm text-gray-600">{handover.condition.afterHandover.exteriorCondition}</span>
                    </div>
                    <div className="flex">
                      <span className="text-sm font-medium w-32">Damages:</span>
                      <span className="text-sm text-gray-600">{handover.condition.afterHandover.interiorCondition}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-md font-semibold mb-3">Images</h3>
                  {handover.condition.afterHandover.images.length > 0 ? (
                    <div className="grid grid-cols-2 gap-2">
                      {handover.condition.afterHandover.images.map((image, index) => (
                        <div key={index} className="border border-gray-200 rounded-md overflow-hidden">
                          <img src={image} alt={`After handover ${index + 1}`} className="w-full h-32 object-cover" />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No images recorded yet</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Documents</CardTitle>
                <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
                  <FileText size={16} />
                  Upload Document
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {handover.documents.map((doc) => (
                  <div
                    key={doc.id}
                    className="flex items-center justify-between p-4 border border-gray-100 rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-lg bg-[#e6ffe6] flex items-center justify-center mr-3">
                        <FileText size={16} className="text-[#009639]" />
                      </div>
                      <div>
                        <p className="font-medium">{doc.name}</p>
                        <p className="text-xs text-gray-500">
                          Uploaded on {doc.uploadDate} by {doc.uploadedBy}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Handover Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {handover.timeline.map((item) => (
              <div key={item.id} className="flex">
                <div className="mr-4 flex flex-col items-center">
                  <div className="w-3 h-3 rounded-full bg-[#009639]"></div>
                  {item.id !== handover.timeline.length && (
                    <div className="w-0.5 h-full bg-gray-200"></div>
                  )}
                </div>
                <div className="pb-4">
                  <div className="flex items-center">
                    <span className="text-sm font-medium">{item.action}</span>
                    <span className="text-xs text-gray-500 ml-2">
                      {item.date} at {item.time}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">By: {item.by}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
