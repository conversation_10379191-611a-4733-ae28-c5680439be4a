"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Calendar,
  Car,
  User,
  CheckCircle,
  Clock,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function HandoversPage() {
  // Mock data - replace with actual API call
  const handovers = [
    {
      id: 1,
      vehicle: "Toyota Fortuner",
      group: "Family SUV",
      fromUser: "Sipho Nkosi",
      toUser: "Thabo Molefe",
      scheduledDate: "2024-02-15",
      scheduledTime: "14:00",
      status: "Scheduled",
      location: "Cape Town Central",
    },
    {
      id: 2,
      vehicle: "VW Polo",
      group: "Weekend Getaway",
      fromUser: "Lerato Mabaso",
      toUser: "Mandla Zulu",
      scheduledDate: "2024-02-10",
      scheduledTime: "09:30",
      status: "Completed",
      location: "Johannesburg North",
    },
    {
      id: 3,
      vehicle: "Honda Civic",
      group: "Work Commute",
      fromUser: "<PERSON> Both<PERSON>",
      toUser: "Pieter van der Merwe",
      scheduledDate: "2024-02-18",
      scheduledTime: "17:00",
      status: "Cancelled",
      location: "Durban Beachfront",
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Vehicle Handovers</h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search handovers..."
              className="pl-10 w-[300px]"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Schedule Handover
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Upcoming Handovers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold">8</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Completed This Week</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-2xl font-bold text-green-600">12</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Cancelled</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-red-600" />
              <span className="text-2xl font-bold text-red-600">2</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Handovers</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vehicle</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>From</TableHead>
                <TableHead>To</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {handovers.map((handover) => (
                <TableRow key={handover.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4 text-[#009639]" />
                      <Link
                        href={`/admin/vehicles/${handover.id}`}
                        className="text-[#009639] hover:text-[#007A2F]"
                      >
                        {handover.vehicle}
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/groups/${handover.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {handover.group}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <Link
                        href={`/admin/users/${handover.id}`}
                        className="text-[#009639] hover:text-[#007A2F]"
                      >
                        {handover.fromUser}
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <Link
                        href={`/admin/users/${handover.id}`}
                        className="text-[#009639] hover:text-[#007A2F]"
                      >
                        {handover.toUser}
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>{handover.scheduledDate}</TableCell>
                  <TableCell>{handover.scheduledTime}</TableCell>
                  <TableCell>{handover.location}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        handover.status === "Completed"
                          ? "bg-green-100 text-green-800"
                          : handover.status === "Scheduled"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {handover.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/handovers/${handover.id}`}
                      className="text-[#009639] hover:text-[#007A2F] font-medium"
                    >
                      View Details
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
