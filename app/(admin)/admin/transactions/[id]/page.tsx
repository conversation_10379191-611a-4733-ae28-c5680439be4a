"use client";

import React from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import {
  ArrowLeft,
  Calendar,
  CreditCard,
  DollarSign,
  User,
  Car,
  Clock,
  FileText,
  Download,
  Printer,
  Mail,
  AlertCircle,
  CheckCircle2,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";

export default function TransactionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;

  // Mock data - replace with actual API call
  const transaction = {
    id,
    reference: `TRX-00${id}-2024`,
    type:
      id === "1"
        ? "Fraction Purchase"
        : id === "2"
        ? "Fraction Sale"
        : id === "3"
        ? "Maintenance Fee"
        : id === "4"
        ? "Insurance Payment"
        : "Rental Income",
    amount:
      id === "1"
        ? 45000
        : id === "2"
        ? 32000
        : id === "3"
        ? 2500
        : id === "4"
        ? 3800
        : 5500,
    date:
      id === "1"
        ? "2024-01-15"
        : id === "2"
        ? "2024-01-20"
        : id === "3"
        ? "2024-01-25"
        : id === "4"
        ? "2024-01-28"
        : "2024-02-01",
    time:
      id === "1"
        ? "09:45 AM"
        : id === "2"
        ? "02:30 PM"
        : id === "3"
        ? "11:15 AM"
        : id === "4"
        ? "04:20 PM"
        : "10:00 AM",
    status: id === "2" ? "Pending" : "Completed",
    paymentMethod:
      id === "1" || id === "4"
        ? "Credit Card"
        : id === "3"
        ? "Debit Card"
        : "Bank Transfer",
    paymentDetails:
      id === "1" || id === "4"
        ? "**** **** **** 4242"
        : id === "3"
        ? "**** **** **** 5678"
        : "FNB Account ****1234",
    user:
      id === "1"
        ? "Sipho Nkosi"
        : id === "2"
        ? "Lerato Mabaso"
        : id === "3"
        ? "Johan Botha"
        : id === "4"
        ? "Thandi Ndlovu"
        : "Michael van der Merwe",
    userEmail:
      id === "1"
        ? "<EMAIL>"
        : id === "2"
        ? "<EMAIL>"
        : id === "3"
        ? "<EMAIL>"
        : id === "4"
        ? "<EMAIL>"
        : "<EMAIL>",
    vehicle:
      id === "1"
        ? "Toyota Fortuner"
        : id === "2"
        ? "VW Polo"
        : id === "3"
        ? "Honda Civic"
        : id === "4"
        ? "BMW X3"
        : "Mercedes C-Class",
    vehicleId: id,
    description:
      id === "1"
        ? "Purchase of 20% fraction of Toyota Fortuner"
        : id === "2"
        ? "Sale of 15% fraction of VW Polo"
        : id === "3"
        ? "Monthly maintenance fee for Honda Civic"
        : id === "4"
        ? "Quarterly insurance payment for BMW X3"
        : "Monthly rental income from Mercedes C-Class",
    fees:
      id === "1"
        ? 1350
        : id === "2"
        ? 960
        : id === "3"
        ? 75
        : id === "4"
        ? 114
        : 165,
    subtotal:
      id === "1"
        ? 43650
        : id === "2"
        ? 31040
        : id === "3"
        ? 2425
        : id === "4"
        ? 3686
        : 5335,
    relatedTransactions:
      id === "1" || id === "2"
        ? [
            {
              id: parseInt(id) + 10,
              reference: `TRX-0${parseInt(id) + 10}-2024`,
              type: "Platform Fee",
              amount: id === "1" ? 1350 : 960,
              date: id === "1" ? "2024-01-15" : "2024-01-20",
              status: "Completed",
            },
          ]
        : [],
  };

  // Transaction type colors
  const getTypeColor = (type: string) => {
    switch (type) {
      case "Fraction Purchase":
        return "bg-green-100 text-green-800";
      case "Fraction Sale":
        return "bg-blue-100 text-blue-800";
      case "Maintenance Fee":
        return "bg-orange-100 text-orange-800";
      case "Insurance Payment":
        return "bg-purple-100 text-purple-800";
      case "Rental Income":
        return "bg-indigo-100 text-indigo-800";
      case "Platform Fee":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Status colors and icons
  const getStatusInfo = (status: string) => {
    switch (status) {
      case "Completed":
        return {
          color: "bg-green-100 text-green-800",
          icon: <CheckCircle2 className="h-4 w-4 text-green-600 mr-1" />,
        };
      case "Pending":
        return {
          color: "bg-yellow-100 text-yellow-800",
          icon: <Clock className="h-4 w-4 text-yellow-600 mr-1" />,
        };
      case "Failed":
        return {
          color: "bg-red-100 text-red-800",
          icon: <AlertCircle className="h-4 w-4 text-red-600 mr-1" />,
        };
      default:
        return {
          color: "bg-gray-100 text-gray-800",
          icon: <Clock className="h-4 w-4 text-gray-600 mr-1" />,
        };
    }
  };

  const statusInfo = getStatusInfo(transaction.status);

  return (
    <div className="p-6">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="outline"
          size="icon"
          onClick={() => router.back()}
          className="h-8 w-8"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold text-gray-800">
          Transaction Details
        </h1>
      </div>

      <div className="grid grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Transaction Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-500">Reference</p>
              <p className="font-medium">{transaction.reference}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Type</p>
              <span
                className={`px-2 py-1 rounded-full text-xs inline-block mt-1 ${getTypeColor(
                  transaction.type
                )}`}
              >
                {transaction.type}
              </span>
            </div>
            <div>
              <p className="text-sm text-gray-500">Amount</p>
              <p className="font-medium text-lg">
                R {transaction.amount.toLocaleString()}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Status</p>
              <div className="flex items-center mt-1">
                <span
                  className={`px-2 py-1 rounded-full text-xs flex items-center ${statusInfo.color}`}
                >
                  {statusInfo.icon}
                  {transaction.status}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Payment Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-500">Payment Method</p>
              <div className="flex items-center gap-2 mt-1">
                <CreditCard className="h-4 w-4 text-gray-500" />
                <p className="font-medium">{transaction.paymentMethod}</p>
              </div>
            </div>
            <div>
              <p className="text-sm text-gray-500">Payment Details</p>
              <p className="font-medium">{transaction.paymentDetails}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Date & Time</p>
              <div className="flex items-center gap-2 mt-1">
                <Calendar className="h-4 w-4 text-gray-500" />
                <p className="font-medium">
                  {transaction.date} at {transaction.time}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Related Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-gray-500">User</p>
              <div className="flex items-center gap-2 mt-1">
                <User className="h-4 w-4 text-gray-500" />
                <Link
                  href={`/admin/users/${transaction.id}`}
                  className="font-medium text-[#009639] hover:text-[#007A2F]"
                >
                  {transaction.user}
                </Link>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                {transaction.userEmail}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Vehicle</p>
              <div className="flex items-center gap-2 mt-1">
                <Car className="h-4 w-4 text-gray-500" />
                <Link
                  href={`/admin/vehicles/${transaction.vehicleId}`}
                  className="font-medium text-[#009639] hover:text-[#007A2F]"
                >
                  {transaction.vehicle}
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Transaction Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Description</p>
                <p className="font-medium">{transaction.description}</p>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <p className="text-sm">Subtotal</p>
                  <p className="font-medium">
                    R {transaction.subtotal.toLocaleString()}
                  </p>
                </div>
                <div className="flex justify-between">
                  <p className="text-sm">Platform Fee</p>
                  <p className="font-medium">
                    R {transaction.fees.toLocaleString()}
                  </p>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between">
                  <p className="font-medium">Total</p>
                  <p className="font-bold">
                    R {transaction.amount.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {transaction.relatedTransactions.length > 0 && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Related Transactions</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Reference</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transaction.relatedTransactions.map((relatedTx) => (
                  <TableRow key={relatedTx.id}>
                    <TableCell className="font-medium">
                      {relatedTx.reference}
                    </TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${getTypeColor(
                          relatedTx.type
                        )}`}
                      >
                        {relatedTx.type}
                      </span>
                    </TableCell>
                    <TableCell>R {relatedTx.amount.toLocaleString()}</TableCell>
                    <TableCell>{relatedTx.date}</TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          getStatusInfo(relatedTx.status).color
                        }`}
                      >
                        {relatedTx.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Link
                        href={`/admin/transactions/${relatedTx.id}`}
                        className="text-[#009639] hover:text-[#007A2F] font-medium"
                      >
                        View Details
                      </Link>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      <div className="flex gap-4">
        <Button variant="outline" className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Download Receipt
        </Button>
        <Button variant="outline" className="flex items-center gap-2">
          <Printer className="h-4 w-4" />
          Print Receipt
        </Button>
        <Button variant="outline" className="flex items-center gap-2">
          <Mail className="h-4 w-4" />
          Email Receipt
        </Button>
      </div>
    </div>
  );
}
