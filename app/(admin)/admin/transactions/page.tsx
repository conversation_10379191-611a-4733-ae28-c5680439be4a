"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  CreditCard,
  Wallet,
  BarChart,
  Tag,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TransactionsPage() {
  // State for filtering
  const [searchQuery, setSearchQuery] = useState("");

  // Mock data - replace with actual API call
  const transactions = [
    {
      id: 1,
      reference: "TRX-001-2024",
      user: "Sipho Nkosi",
      type: "Fraction Purchase",
      vehicle: "Toyota Fortuner",
      amount: 45000,
      date: "2024-01-15",
      status: "Completed",
      paymentMethod: "Credit Card",
    },
    {
      id: 2,
      reference: "TRX-002-2024",
      user: "Lerato Mabaso",
      type: "Fraction Sale",
      vehicle: "VW Polo",
      amount: 32000,
      date: "2024-01-20",
      status: "Pending",
      paymentMethod: "Bank Transfer",
    },
    {
      id: 3,
      reference: "TRX-003-2024",
      user: "Johan Botha",
      type: "Maintenance Fee",
      vehicle: "Honda Civic",
      amount: 2500,
      date: "2024-01-25",
      status: "Completed",
      paymentMethod: "Debit Card",
    },
    {
      id: 4,
      reference: "TRX-004-2024",
      user: "Thandi Ndlovu",
      type: "Insurance Payment",
      vehicle: "BMW X3",
      amount: 3800,
      date: "2024-01-28",
      status: "Completed",
      paymentMethod: "Credit Card",
    },
    {
      id: 5,
      reference: "TRX-005-2024",
      user: "Michael van der Merwe",
      type: "Rental Income",
      vehicle: "Mercedes C-Class",
      amount: 5500,
      date: "2024-02-01",
      status: "Completed",
      paymentMethod: "Bank Transfer",
    },
  ];

  // Transaction type colors
  const getTypeColor = (type: string) => {
    switch (type) {
      case "Fraction Purchase":
        return "bg-green-100 text-green-800";
      case "Fraction Sale":
        return "bg-blue-100 text-blue-800";
      case "Maintenance Fee":
        return "bg-orange-100 text-orange-800";
      case "Insurance Payment":
        return "bg-purple-100 text-purple-800";
      case "Rental Income":
        return "bg-indigo-100 text-indigo-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Status colors
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800";
      case "Pending":
        return "bg-yellow-100 text-yellow-800";
      case "Failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Payment method icon
  const getPaymentIcon = (method: string) => {
    switch (method) {
      case "Credit Card":
      case "Debit Card":
        return <CreditCard className="h-4 w-4 text-gray-500" />;
      case "Bank Transfer":
        return <Wallet className="h-4 w-4 text-gray-500" />;
      default:
        return <DollarSign className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Transactions</h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search transactions..."
              className="pl-10 w-[300px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Date Range
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Transactions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <BarChart className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold">128</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Income</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <ArrowUpRight className="h-5 w-5 text-green-600" />
              <span className="text-2xl font-bold text-green-600">R 2.4M</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <ArrowDownRight className="h-5 w-5 text-blue-600" />
              <span className="text-2xl font-bold text-blue-600">R 1.1M</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Net Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold text-[#009639]">R 1.3M</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>All Transactions</CardTitle>
          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-500">
              Showing {transactions.length} transactions
            </span>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Reference</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Vehicle</TableHead>
                <TableHead>Payment Method</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell className="font-medium">
                    {transaction.reference}
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/users/${transaction.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {transaction.user}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${getTypeColor(
                        transaction.type
                      )}`}
                    >
                      {transaction.type}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/vehicles/${transaction.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {transaction.vehicle}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {getPaymentIcon(transaction.paymentMethod)}
                      <span>{transaction.paymentMethod}</span>
                    </div>
                  </TableCell>
                  <TableCell>R {transaction.amount.toLocaleString()}</TableCell>
                  <TableCell>{transaction.date}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${getStatusColor(
                        transaction.status
                      )}`}
                    >
                      {transaction.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/transactions/${transaction.id}`}
                      className="text-[#009639] hover:text-[#007A2F] font-medium"
                    >
                      View Details
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
