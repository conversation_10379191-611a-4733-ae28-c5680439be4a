"use client";

import React, { useState } from "react";
import {
  Search,
  Filter,
  ChevronDown,
  MoreHorizontal,
  Plus,
  Users,
  Calendar,
  MapPin,
  Car,
  Shield,
  AlertTriangle,
  CheckCircle,
  FileText,
  DollarSign,
  Handshake,
  Clock,
  Bell,
  Eye,
  Edit,
  MessageSquare,
} from "lucide-react";

// Mock group data
const groupsData = [
  {
    id: 1,
    name: "Family SUV",
    image: "/placeholder.svg?height=60&width=60",
    members: 5,
    vehicles: 1,
    location: "Cape Town",
    status: "Active",
    compliance: "Compliant",
    created: "15/01/2025",
    lastActivity: "2 hours ago",
    formationStatus: "Completed",
    verificationStatus: "Verified",
    legalEntityStatus: "Registered",
    financialSetupStatus: "Completed",
    partnerIntegrationStatus: "Completed",
    applicationProgress: 100,
    communicationStatus: "Up to date",
  },
  {
    id: 2,
    name: "Weekend Getaway",
    image: "/placeholder.svg?height=60&width=60",
    members: 3,
    vehicles: 1,
    location: "Johannesburg",
    status: "Active",
    compliance: "Compliant",
    created: "10/01/2025",
    lastActivity: "1 day ago",
    formationStatus: "Completed",
    verificationStatus: "Verified",
    legalEntityStatus: "Registered",
    financialSetupStatus: "Completed",
    partnerIntegrationStatus: "In Progress",
    applicationProgress: 85,
    communicationStatus: "Pending Update",
  },
  {
    id: 3,
    name: "Work Commute",
    image: "/placeholder.svg?height=60&width=60",
    members: 4,
    vehicles: 1,
    location: "Durban",
    status: "Active",
    compliance: "Compliant",
    created: "05/01/2025",
    lastActivity: "3 hours ago",
    formationStatus: "Completed",
    verificationStatus: "Verified",
    legalEntityStatus: "Registered",
    financialSetupStatus: "In Progress",
    partnerIntegrationStatus: "Pending",
    applicationProgress: 70,
    communicationStatus: "Up to date",
  },
  {
    id: 4,
    name: "City Commuters",
    image: "/placeholder.svg?height=60&width=60",
    members: 8,
    vehicles: 3,
    location: "Cape Town",
    status: "Active",
    compliance: "Warning",
    created: "20/12/2024",
    lastActivity: "5 hours ago",
    formationStatus: "Completed",
    verificationStatus: "Verified",
    legalEntityStatus: "In Progress",
    financialSetupStatus: "Pending",
    partnerIntegrationStatus: "Pending",
    applicationProgress: 50,
    communicationStatus: "Needs Update",
  },
  {
    id: 5,
    name: "Weekend Adventurers",
    image: "/placeholder.svg?height=60&width=60",
    members: 6,
    vehicles: 2,
    location: "Johannesburg",
    status: "Inactive",
    compliance: "Compliant",
    created: "15/12/2024",
    lastActivity: "2 days ago",
    formationStatus: "Completed",
    verificationStatus: "Pending",
    legalEntityStatus: "Pending",
    financialSetupStatus: "Pending",
    partnerIntegrationStatus: "Pending",
    applicationProgress: 30,
    communicationStatus: "Needs Update",
  },
  {
    id: 6,
    name: "Family Vehicles",
    image: "/placeholder.svg?height=60&width=60",
    members: 5,
    vehicles: 2,
    location: "Durban",
    status: "Active",
    compliance: "Non-compliant",
    created: "10/12/2024",
    lastActivity: "1 day ago",
    formationStatus: "Completed",
    verificationStatus: "Rejected",
    legalEntityStatus: "Pending",
    financialSetupStatus: "Pending",
    partnerIntegrationStatus: "Pending",
    applicationProgress: 25,
    communicationStatus: "Needs Update",
  },
  {
    id: 7,
    name: "Electric Car Share",
    image: "/placeholder.svg?height=60&width=60",
    members: 7,
    vehicles: 2,
    location: "Pretoria",
    status: "Active",
    compliance: "Compliant",
    created: "05/12/2024",
    lastActivity: "6 hours ago",
    formationStatus: "In Progress",
    verificationStatus: "Pending",
    legalEntityStatus: "Pending",
    financialSetupStatus: "Pending",
    partnerIntegrationStatus: "Pending",
    applicationProgress: 15,
    communicationStatus: "Up to date",
  },
  {
    id: 8,
    name: "Neighborhood Pool",
    image: "/placeholder.svg?height=60&width=60",
    members: 10,
    vehicles: 3,
    location: "Bloemfontein",
    status: "Active",
    compliance: "Compliant",
    created: "30/11/2024",
    lastActivity: "4 hours ago",
    formationStatus: "New",
    verificationStatus: "Pending",
    legalEntityStatus: "Pending",
    financialSetupStatus: "Pending",
    partnerIntegrationStatus: "Pending",
    applicationProgress: 5,
    communicationStatus: "Up to date",
  },
];



export default function GroupOversight() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedGroups, setSelectedGroups] = useState<number[]>([]);
  const [statusFilter, setStatusFilter] = useState("All");
  const [complianceFilter, setComplianceFilter] = useState("All");
  const [formationFilter, setFormationFilter] = useState("All");
  const [selectedGroup, setSelectedGroup] = useState<any>(null);

  // Handle select all groups
  const handleSelectAll = () => {
    if (selectedGroups.length === groupsData.length) {
      setSelectedGroups([]);
    } else {
      setSelectedGroups(groupsData.map((group) => group.id));
    }
  };

  // Handle select individual group
  const handleSelectGroup = (groupId: number) => {
    if (selectedGroups.includes(groupId)) {
      setSelectedGroups(selectedGroups.filter((id) => id !== groupId));
    } else {
      setSelectedGroups([...selectedGroups, groupId]);
    }
  };

  // Handle view group details
  const handleViewGroup = (group: any) => {
    setSelectedGroup(group);
  };

  // Handle back to list
  const handleBackToList = () => {
    setSelectedGroup(null);
  };

  // Filter groups based on search query and filters
  const filteredGroups = groupsData.filter((group) => {
    const matchesSearch = group.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesStatus =
      statusFilter === "All" || group.status === statusFilter;
    const matchesCompliance =
      complianceFilter === "All" || group.compliance === complianceFilter;
    const matchesFormation =
      formationFilter === "All" || group.formationStatus === formationFilter;

    return (
      matchesSearch && matchesStatus && matchesCompliance && matchesFormation
    );
  });

  // Get compliance badge color
  const getComplianceBadge = (compliance: string) => {
    switch (compliance) {
      case "Compliant":
        return {
          bg: "bg-green-100",
          text: "text-green-600",
          icon: <CheckCircle size={14} className="mr-1" />,
        };
      case "Warning":
        return {
          bg: "bg-yellow-100",
          text: "text-yellow-600",
          icon: <AlertTriangle size={14} className="mr-1" />,
        };
      case "Non-compliant":
        return {
          bg: "bg-red-100",
          text: "text-red-600",
          icon: <AlertTriangle size={14} className="mr-1" />,
        };
      default:
        return { bg: "bg-gray-100", text: "text-gray-600", icon: null };
    }
  };

  // Get status badge for formation stages
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
      case "Registered":
      case "Verified":
        return {
          bg: "bg-green-100",
          text: "text-green-600",
          icon: <CheckCircle size={14} className="mr-1" />,
        };
      case "In Progress":
        return {
          bg: "bg-blue-100",
          text: "text-blue-600",
          icon: <Clock size={14} className="mr-1" />,
        };
      case "Pending":
      case "New":
        return {
          bg: "bg-yellow-100",
          text: "text-yellow-600",
          icon: <Clock size={14} className="mr-1" />,
        };
      case "Rejected":
        return {
          bg: "bg-red-100",
          text: "text-red-600",
          icon: <AlertTriangle size={14} className="mr-1" />,
        };
      default:
        return { bg: "bg-gray-100", text: "text-gray-600", icon: null };
    }
  };

  // Get communication status badge
  const getCommunicationBadge = (status: string) => {
    switch (status) {
      case "Up to date":
        return {
          bg: "bg-green-100",
          text: "text-green-600",
          icon: <CheckCircle size={14} className="mr-1" />,
        };
      case "Pending Update":
        return {
          bg: "bg-yellow-100",
          text: "text-yellow-600",
          icon: <Clock size={14} className="mr-1" />,
        };
      case "Needs Update":
        return {
          bg: "bg-red-100",
          text: "text-red-600",
          icon: <Bell size={14} className="mr-1" />,
        };
      default:
        return { bg: "bg-gray-100", text: "text-gray-600", icon: null };
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Group Oversight</h1>
        <p className="text-gray-500">Monitor and manage all active groups</p>
      </div>

      {selectedGroup ? (
        // Group Detail View
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="p-6 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-semibold">{selectedGroup.name}</h2>
            <button
              onClick={handleBackToList}
              className="text-gray-500 hover:text-gray-700 flex items-center"
            >
              Back to List
            </button>
          </div>

          <div className="p-6">
            {/* Group Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Group Information</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Members</span>
                    <span className="text-sm text-gray-700 flex items-center">
                      <Users size={14} className="mr-1 text-gray-400" />
                      {selectedGroup.members}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Vehicles</span>
                    <span className="text-sm text-gray-700 flex items-center">
                      <Car size={14} className="mr-1 text-gray-400" />
                      {selectedGroup.vehicles}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Location</span>
                    <span className="text-sm text-gray-700 flex items-center">
                      <MapPin size={14} className="mr-1 text-gray-400" />
                      {selectedGroup.location}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Created</span>
                    <span className="text-sm text-gray-700 flex items-center">
                      <Calendar size={14} className="mr-1 text-gray-400" />
                      {selectedGroup.created}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Last Activity</span>
                    <span className="text-sm text-gray-700">
                      {selectedGroup.lastActivity}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Formation Progress</h3>
                <div className="mb-4">
                  <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                    <div
                      className="bg-[#009639] h-2.5 rounded-full"
                      style={{ width: `${selectedGroup.applicationProgress}%` }}
                    ></div>
                  </div>
                  <div className="text-sm text-center">{selectedGroup.applicationProgress}% Complete</div>
                </div>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Formation Status</span>
                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(selectedGroup.formationStatus).bg} ${getStatusBadge(selectedGroup.formationStatus).text} flex items-center w-fit mt-1`}
                    >
                      {getStatusBadge(selectedGroup.formationStatus).icon}
                      {selectedGroup.formationStatus}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Verification Status</span>
                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(selectedGroup.verificationStatus).bg} ${getStatusBadge(selectedGroup.verificationStatus).text} flex items-center w-fit mt-1`}
                    >
                      {getStatusBadge(selectedGroup.verificationStatus).icon}
                      {selectedGroup.verificationStatus}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Legal Entity Status</span>
                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(selectedGroup.legalEntityStatus).bg} ${getStatusBadge(selectedGroup.legalEntityStatus).text} flex items-center w-fit mt-1`}
                    >
                      {getStatusBadge(selectedGroup.legalEntityStatus).icon}
                      {selectedGroup.legalEntityStatus}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Financial Setup Status</span>
                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(selectedGroup.financialSetupStatus).bg} ${getStatusBadge(selectedGroup.financialSetupStatus).text} flex items-center w-fit mt-1`}
                    >
                      {getStatusBadge(selectedGroup.financialSetupStatus).icon}
                      {selectedGroup.financialSetupStatus}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Partner Integration Status</span>
                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(selectedGroup.partnerIntegrationStatus).bg} ${getStatusBadge(selectedGroup.partnerIntegrationStatus).text} flex items-center w-fit mt-1`}
                    >
                      {getStatusBadge(selectedGroup.partnerIntegrationStatus).icon}
                      {selectedGroup.partnerIntegrationStatus}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium mb-4">Communication Status</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">Status</span>
                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full ${getCommunicationBadge(selectedGroup.communicationStatus).bg} ${getCommunicationBadge(selectedGroup.communicationStatus).text} flex items-center w-fit mt-1`}
                    >
                      {getCommunicationBadge(selectedGroup.communicationStatus).icon}
                      {selectedGroup.communicationStatus}
                    </span>
                  </div>
                  <div className="mt-4">
                    <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] w-full mb-2">
                      Send Status Update
                    </button>
                    <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 w-full">
                      View Communication History
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center justify-center">
                <FileText size={16} className="mr-2" />
                Process Legal Entity
              </button>
              <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center justify-center">
                <DollarSign size={16} className="mr-2" />
                Setup Financial Account
              </button>
              <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center justify-center">
                <Handshake size={16} className="mr-2" />
                Connect Partners
              </button>
              <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 flex items-center justify-center">
                <Shield size={16} className="mr-2" />
                Verify Group
              </button>
            </div>

            {/* Tabs for different sections */}
            <div className="border-b border-gray-200 mb-6">
              <div className="flex space-x-4">
                <button className="px-4 py-2 text-sm font-medium text-[#009639] border-b-2 border-[#009639]">
                  Members
                </button>
                <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
                  Vehicles
                </button>
                <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
                  Documents
                </button>
                <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
                  Financial
                </button>
                <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
                  Partners
                </button>
              </div>
            </div>

            {/* Member List (Default Tab) */}
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Name</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Role</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Verification</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-gray-200">
                    <td className="px-4 py-4 text-sm font-medium">Sipho Nkosi</td>
                    <td className="px-4 py-4 text-sm text-gray-600">Group Admin</td>
                    <td className="px-4 py-4">
                      <span className="px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-600">
                        Verified
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex space-x-2">
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Eye size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <MessageSquare size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="px-4 py-4 text-sm font-medium">Thabo Molefe</td>
                    <td className="px-4 py-4 text-sm text-gray-600">Member</td>
                    <td className="px-4 py-4">
                      <span className="px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-600">
                        Verified
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex space-x-2">
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Eye size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <MessageSquare size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      ) : (
        // Groups List View
        <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">All Groups</h2>

          <div className="flex items-center space-x-4">
            <button className="flex items-center px-4 py-2 border border-gray-200 rounded-md text-sm">
              <Filter size={16} className="mr-2" />
              Filters
            </button>

            <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center">
              <Plus size={16} className="mr-2" />
              Add New Group
            </button>
          </div>
        </div>

        <div className="flex flex-wrap gap-4 mb-6">
          <div className="relative">
            <Search
              size={18}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
            <input
              type="text"
              placeholder="Search groups..."
              className="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="relative">
            <select
              className="appearance-none pl-4 pr-10 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="All">All Statuses</option>
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <ChevronDown size={16} className="text-gray-400" />
            </div>
          </div>

          <div className="relative">
            <select
              className="appearance-none pl-4 pr-10 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              value={complianceFilter}
              onChange={(e) => setComplianceFilter(e.target.value)}
            >
              <option value="All">All Compliance</option>
              <option value="Compliant">Compliant</option>
              <option value="Warning">Warning</option>
              <option value="Non-compliant">Non-compliant</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <ChevronDown size={16} className="text-gray-400" />
            </div>
          </div>

          <div className="relative">
            <select
              className="appearance-none pl-4 pr-10 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              value={formationFilter}
              onChange={(e) => setFormationFilter(e.target.value)}
            >
              <option value="All">All Formation Stages</option>
              <option value="New">New</option>
              <option value="In Progress">In Progress</option>
              <option value="Completed">Completed</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <ChevronDown size={16} className="text-gray-400" />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    className="rounded text-[#009639] focus:ring-[#009639]"
                    checked={selectedGroups.length === groupsData.length}
                    onChange={handleSelectAll}
                  />
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Group Name
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Members
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Vehicles
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Location
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Compliance
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Formation Status
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Progress
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Created
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Last Activity
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredGroups.map((group) => {
                const complianceBadge = getComplianceBadge(group.compliance);

                return (
                  <tr
                    key={group.id}
                    className="border-b border-gray-200 hover:bg-gray-50"
                  >
                    <td className="px-4 py-4">
                      <input
                        type="checkbox"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        checked={selectedGroups.includes(group.id)}
                        onChange={() => handleSelectGroup(group.id)}
                      />
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <div className="w-10 h-10 rounded-lg bg-[#e6ffe6] flex items-center justify-center mr-3">
                          <Users size={20} className="text-[#009639]" />
                        </div>
                        <span className="text-sm font-medium">
                          {group.name}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <Users size={16} className="mr-2 text-gray-400" />
                        {group.members}
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <Car size={16} className="mr-2 text-gray-400" />
                        {group.vehicles}
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <MapPin size={16} className="mr-2 text-gray-400" />
                        {group.location}
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${
                          group.status === "Active"
                            ? "bg-green-100 text-green-600"
                            : "bg-gray-100 text-gray-600"
                        }`}
                      >
                        {group.status}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${complianceBadge.bg} ${complianceBadge.text} flex items-center w-fit`}
                      >
                        {complianceBadge.icon}
                        {group.compliance}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${
                          getStatusBadge(group.formationStatus).bg
                        } ${
                          getStatusBadge(group.formationStatus).text
                        } flex items-center w-fit`}
                      >
                        {getStatusBadge(group.formationStatus).icon}
                        {group.formationStatus}
                      </span>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-[#009639] h-2.5 rounded-full"
                          style={{ width: `${group.applicationProgress}%` }}
                        ></div>
                      </div>
                      <div className="text-xs mt-1 text-center">
                        {group.applicationProgress}%
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <Calendar size={16} className="mr-2 text-gray-400" />
                        {group.created}
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-600">
                      {group.lastActivity}
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex space-x-2">
                        <button
                          className="p-1 text-gray-400 hover:text-gray-600"
                          onClick={() => handleViewGroup(group)}
                        >
                          <Eye size={18} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Edit size={18} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Shield size={18} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <MoreHorizontal size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-500">
            Showing 1 to {filteredGroups.length} of {groupsData.length} entries
          </div>

          <div className="flex items-center space-x-1">
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
              &lt;
            </button>
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm bg-[#009639] text-white">
              1
            </button>
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
              &gt;
            </button>
          </div>

          <div className="flex items-center">
            <span className="text-sm text-gray-500 mr-2">Show</span>
            <select className="appearance-none pl-2 pr-6 py-1 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent">
              <option>8</option>
              <option>16</option>
              <option>24</option>
              <option>32</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );
}
