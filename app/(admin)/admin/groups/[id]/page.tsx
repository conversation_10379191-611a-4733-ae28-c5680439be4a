"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Users,
  Car,
  MapPin,
  Calendar,
  Clock,
  Shield,
  FileText,
  DollarSign,
  Handshake,
  Bell,
  MessageSquare,
  CheckCircle,
  AlertTriangle,
  Edit,
  Trash2,
  UserPlus,
  Settings,
  Download,
  Send,
  Plus,
  Eye,
  MoreHorizontal,
  BarChart2,
  Activity,
} from "lucide-react";
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  PieChart,
  Pie,
  Cell,
  CartesianGrid,
} from "recharts";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Link from "next/link";

// Mock group data
const groupsData = [
  {
    id: 1,
    name: "Family SUV",
    image: "/placeholder.svg?height=60&width=60",
    members: 5,
    vehicles: 1,
    location: "Cape Town",
    status: "Active",
    compliance: "Compliant",
    created: "15/01/2025",
    lastActivity: "2 hours ago",
    formationStatus: "Completed",
    verificationStatus: "Verified",
    legalEntityStatus: "Registered",
    financialSetupStatus: "Completed",
    partnerIntegrationStatus: "Completed",
    applicationProgress: 100,
    communicationStatus: "Up to date",
  },
  {
    id: 2,
    name: "Weekend Getaway",
    image: "/placeholder.svg?height=60&width=60",
    members: 3,
    vehicles: 1,
    location: "Johannesburg",
    status: "Active",
    compliance: "Compliant",
    created: "10/01/2025",
    lastActivity: "1 day ago",
    formationStatus: "Completed",
    verificationStatus: "Verified",
    legalEntityStatus: "Registered",
    financialSetupStatus: "Completed",
    partnerIntegrationStatus: "In Progress",
    applicationProgress: 85,
    communicationStatus: "Pending Update",
  },
  {
    id: 3,
    name: "Work Commute",
    image: "/placeholder.svg?height=60&width=60",
    members: 4,
    vehicles: 1,
    location: "Durban",
    status: "Active",
    compliance: "Compliant",
    created: "05/01/2025",
    lastActivity: "3 hours ago",
    formationStatus: "Completed",
    verificationStatus: "Verified",
    legalEntityStatus: "Registered",
    financialSetupStatus: "In Progress",
    partnerIntegrationStatus: "Pending",
    applicationProgress: 70,
    communicationStatus: "Up to date",
  },
];

function GroupDetails({ params }: { params: { id: string } }) {
  const router = useRouter();
  const groupId = parseInt(params.id);

  // Find the group by ID
  const group = groupsData.find((g) => g.id === groupId);

  // If group not found, show error
  if (!group) {
    return (
      <div className="p-8 text-center">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          Group Not Found
        </h1>
        <p className="text-gray-500 mb-6">
          The group you are looking for does not exist.
        </p>
        <Button
          onClick={() => router.push("/admin/groups")}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center mx-auto"
        >
          <ArrowLeft size={16} className="mr-2" />
          Back to Groups
        </Button>
      </div>
    );
  }

  const [activeTab, setActiveTab] = useState("overview");

  // Get compliance badge color
  const getComplianceBadge = (compliance: string) => {
    switch (compliance) {
      case "Compliant":
        return {
          bg: "bg-green-100",
          text: "text-green-600",
          icon: <CheckCircle size={14} className="mr-1" />,
        };
      case "Warning":
        return {
          bg: "bg-yellow-100",
          text: "text-yellow-600",
          icon: <AlertTriangle size={14} className="mr-1" />,
        };
      case "Non-compliant":
        return {
          bg: "bg-red-100",
          text: "text-red-600",
          icon: <AlertTriangle size={14} className="mr-1" />,
        };
      default:
        return { bg: "bg-gray-100", text: "text-gray-600", icon: null };
    }
  };

  const complianceBadge = getComplianceBadge(group.compliance);

  // Get status badge
  const getStatusBadge = (status: string) => {
    return status === "Active"
      ? "bg-green-100 text-green-600"
      : "bg-gray-100 text-gray-600";
  };

  // Get progress status color
  const getProgressStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return {
          bg: "bg-green-100",
          text: "text-green-600",
          icon: <CheckCircle size={14} className="mr-1" />,
        };
      case "In Progress":
        return {
          bg: "bg-blue-100",
          text: "text-blue-600",
          icon: <Clock size={14} className="mr-1" />,
        };
      case "Pending":
        return {
          bg: "bg-yellow-100",
          text: "text-yellow-600",
          icon: <AlertTriangle size={14} className="mr-1" />,
        };
      default:
        return { bg: "bg-gray-100", text: "text-gray-600", icon: null };
    }
  };

  // Mock data for charts and tables
  const memberData = [
    { id: 1, name: "John Doe", role: "Owner", joinDate: "15/01/2025" },
    { id: 2, name: "Jane Smith", role: "Member", joinDate: "16/01/2025" },
    { id: 3, name: "Michael Johnson", role: "Member", joinDate: "17/01/2025" },
    { id: 4, name: "Sarah Williams", role: "Member", joinDate: "18/01/2025" },
    { id: 5, name: "David Brown", role: "Member", joinDate: "19/01/2025" },
  ];

  const vehicleData = [
    {
      id: 1,
      name: "Toyota Fortuner",
      type: "SUV",
      status: "Active",
      utilization: "85%",
    },
  ];

  const bookingData = [
    {
      id: 1,
      user: "John Doe",
      vehicle: "Toyota Fortuner",
      startDate: "20/01/2025",
      endDate: "22/01/2025",
      status: "Completed",
    },
    {
      id: 2,
      user: "Jane Smith",
      vehicle: "Toyota Fortuner",
      startDate: "25/01/2025",
      endDate: "26/01/2025",
      status: "Upcoming",
    },
  ];

  const activityData = [
    {
      id: 1,
      type: "Member Added",
      user: "Sarah Williams",
      date: "18/01/2025",
      time: "14:30",
      description: "New member joined the group",
    },
    {
      id: 2,
      type: "Booking Created",
      user: "Jane Smith",
      date: "17/01/2025",
      time: "09:15",
      description: "Created a new booking for Toyota Fortuner",
    },
    {
      id: 3,
      type: "Vehicle Added",
      user: "John Doe",
      date: "15/01/2025",
      time: "11:45",
      description: "Added Toyota Fortuner to the group",
    },
  ];

  const documentsData = [
    {
      id: 1,
      name: "Group Agreement",
      type: "Legal",
      uploadDate: "15/01/2025",
      size: "1.2 MB",
    },
    {
      id: 2,
      name: "Vehicle Registration",
      type: "Vehicle",
      uploadDate: "15/01/2025",
      size: "0.8 MB",
    },
    {
      id: 3,
      name: "Insurance Policy",
      type: "Insurance",
      uploadDate: "16/01/2025",
      size: "2.1 MB",
    },
  ];

  const utilizationData = [
    { name: "Mon", value: 65 },
    { name: "Tue", value: 75 },
    { name: "Wed", value: 85 },
    { name: "Thu", value: 70 },
    { name: "Fri", value: 90 },
    { name: "Sat", value: 95 },
    { name: "Sun", value: 80 },
  ];

  const costSharingData = [
    { name: "John Doe", value: 30, color: "#009639" },
    { name: "Jane Smith", value: 20, color: "#007A2F" },
    { name: "Michael Johnson", value: 20, color: "#FFD700" },
    { name: "Sarah Williams", value: 15, color: "#20c997" },
    { name: "David Brown", value: 15, color: "#6c757d" },
  ];

  return (
    <div className="p-6">
      <Button
        variant="ghost"
        className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
        onClick={() => router.back()}
      >
        <ArrowLeft size={16} className="mr-2" />
        Back to Groups
      </Button>

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mr-4 overflow-hidden">
            <img
              src={group.image}
              alt={group.name}
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{group.name}</h1>
            <div className="flex items-center mt-1">
              <Badge variant="outline" className={`${complianceBadge.bg} ${complianceBadge.text} mr-2`}>
                <span className="flex items-center">
                  {complianceBadge.icon}
                  {group.compliance}
                </span>
              </Badge>
              <Badge variant="outline" className={`${getStatusBadge(group.status)}`}>
                {group.status}
              </Badge>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <Edit size={16} />
            Edit Group
          </Button>
          <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
            <UserPlus size={16} />
            Add Member
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Users size={18} className="text-[#009639] mr-2" />
              Group Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Members:</span>
                <span className="text-sm font-medium">{group.members}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Vehicles:</span>
                <span className="text-sm font-medium">{group.vehicles}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Location:</span>
                <span className="text-sm font-medium flex items-center">
                  <MapPin size={14} className="mr-1 text-gray-400" />
                  {group.location}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Created:</span>
                <span className="text-sm font-medium">{group.created}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Last Activity:</span>
                <span className="text-sm font-medium">{group.lastActivity}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Shield size={18} className="text-[#009639] mr-2" />
              Compliance Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Formation:</span>
                <Badge variant="outline" className={`${getProgressStatusColor(group.formationStatus).bg} ${getProgressStatusColor(group.formationStatus).text}`}>
                  <span className="flex items-center">
                    {getProgressStatusColor(group.formationStatus).icon}
                    {group.formationStatus}
                  </span>
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Verification:</span>
                <Badge variant="outline" className={`${getProgressStatusColor(group.verificationStatus).bg} ${getProgressStatusColor(group.verificationStatus).text}`}>
                  <span className="flex items-center">
                    {getProgressStatusColor(group.verificationStatus).icon}
                    {group.verificationStatus}
                  </span>
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Legal Entity:</span>
                <Badge variant="outline" className={`${getProgressStatusColor(group.legalEntityStatus).bg} ${getProgressStatusColor(group.legalEntityStatus).text}`}>
                  <span className="flex items-center">
                    {getProgressStatusColor(group.legalEntityStatus).icon}
                    {group.legalEntityStatus}
                  </span>
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Financial Setup:</span>
                <Badge variant="outline" className={`${getProgressStatusColor(group.financialSetupStatus).bg} ${getProgressStatusColor(group.financialSetupStatus).text}`}>
                  <span className="flex items-center">
                    {getProgressStatusColor(group.financialSetupStatus).icon}
                    {group.financialSetupStatus}
                  </span>
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Partner Integration:</span>
                <Badge variant="outline" className={`${getProgressStatusColor(group.partnerIntegrationStatus).bg} ${getProgressStatusColor(group.partnerIntegrationStatus).text}`}>
                  <span className="flex items-center">
                    {getProgressStatusColor(group.partnerIntegrationStatus).icon}
                    {group.partnerIntegrationStatus}
                  </span>
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <BarChart2 size={18} className="text-[#009639] mr-2" />
              Application Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium text-gray-500">Overall Progress:</span>
                  <span className="text-sm font-medium">{group.applicationProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-[#009639] h-2.5 rounded-full"
                    style={{ width: `${group.applicationProgress}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Communication:</span>
                <Badge variant="outline" className={`${getProgressStatusColor(group.communicationStatus).bg} ${getProgressStatusColor(group.communicationStatus).text}`}>
                  <span className="flex items-center">
                    {getProgressStatusColor(group.communicationStatus).icon}
                    {group.communicationStatus}
                  </span>
                </Badge>
              </div>
              
              <Button variant="outline" size="sm" className="w-full mt-2 flex items-center justify-center gap-2">
                <FileText size={14} />
                View Full Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-6">
        <TabsList className="mb-4">
          <TabsTrigger value="overview" className="px-4">
            <Users size={16} className="mr-2" />
            Members
          </TabsTrigger>
          <TabsTrigger value="vehicles" className="px-4">
            <Car size={16} className="mr-2" />
            Vehicles
          </TabsTrigger>
          <TabsTrigger value="bookings" className="px-4">
            <Calendar size={16} className="mr-2" />
            Bookings
          </TabsTrigger>
          <TabsTrigger value="activity" className="px-4">
            <Activity size={16} className="mr-2" />
            Activity
          </TabsTrigger>
          <TabsTrigger value="documents" className="px-4">
            <FileText size={16} className="mr-2" />
            Documents
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg flex items-center">
                  <Users size={18} className="text-[#009639] mr-2" />
                  Group Members
                </CardTitle>
                <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
                  <UserPlus size={16} />
                  Add Member
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Join Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {memberData.map((member) => (
                    <TableRow key={member.id}>
                      <TableCell className="font-medium">{member.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={member.role === "Owner" ? "bg-[#00963920] text-[#009639]" : "bg-blue-100 text-blue-800"}>
                          {member.role}
                        </Badge>
                      </TableCell>
                      <TableCell>{member.joinDate}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm" asChild className="text-[#009639] hover:text-[#007A2F]">
                            <Link href={`/admin/users/${member.id}`}>
                              View
                            </Link>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <BarChart2 size={18} className="text-[#009639] mr-2" />
                  Vehicle Utilization
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={utilizationData}>
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="value" fill="#009639" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <DollarSign size={18} className="text-[#009639] mr-2" />
                  Cost Sharing
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={costSharingData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {costSharingData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="vehicles">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg flex items-center">
                  <Car size={18} className="text-[#009639] mr-2" />
                  Group Vehicles
                </CardTitle>
                <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
                  <Plus size={16} />
                  Add Vehicle
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vehicle</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Utilization</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {vehicleData.map((vehicle) => (
                    <TableRow key={vehicle.id}>
                      <TableCell className="font-medium">{vehicle.name}</TableCell>
                      <TableCell>{vehicle.type}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-[#00963920] text-[#009639]">
                          {vehicle.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{vehicle.utilization}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm" asChild className="text-[#009639] hover:text-[#007A2F]">
                            <Link href={`/admin/vehicles/${vehicle.id}`}>
                              View
                            </Link>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bookings">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg flex items-center">
                  <Calendar size={18} className="text-[#009639] mr-2" />
                  Booking History
                </CardTitle>
                <Button variant="outline" className="flex items-center gap-2">
                  <Download size={16} />
                  Export
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Vehicle</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {bookingData.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell className="font-medium">{booking.user}</TableCell>
                      <TableCell>{booking.vehicle}</TableCell>
                      <TableCell>{booking.startDate}</TableCell>
                      <TableCell>{booking.endDate}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className={booking.status === "Completed" ? "bg-[#00963920] text-[#009639]" : "bg-blue-100 text-blue-800"}>
                          {booking.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm" asChild className="text-[#009639] hover:text-[#007A2F]">
                            <Link href={`/admin/bookings/${booking.id}`}>
                              View
                            </Link>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Activity size={18} className="text-[#009639] mr-2" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activityData.map((activity) => (
                  <div key={activity.id} className="p-3 border border-gray-100 rounded-lg">
                    <div className="flex justify-between mb-1">
                      <span className="font-medium">{activity.type}</span>
                      <span className="text-xs text-gray-500">{activity.date} {activity.time}</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-1">By {activity.user}</p>
                    <p className="text-sm text-gray-600">
                      {activity.description}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg flex items-center">
                  <FileText size={18} className="text-[#009639] mr-2" />
                  Group Documents
                </CardTitle>
                <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
                  <Plus size={16} />
                  Upload Document
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Document Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Upload Date</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {documentsData.map((document) => (
                    <TableRow key={document.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-lg bg-[#e6ffe6] flex items-center justify-center mr-3">
                            <FileText size={16} className="text-[#009639]" />
                          </div>
                          <span className="font-medium">{document.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{document.type}</TableCell>
                      <TableCell>{document.uploadDate}</TableCell>
                      <TableCell>{document.size}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-[#0d6efd] hover:bg-[#0d6efd10] hover:text-[#0a58ca]">
                            <Eye size={16} />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-[#009639] hover:bg-[#00963910] hover:text-[#007A2F]">
                            <Download size={16} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default GroupDetails;
