"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Download,
  Upload,
  Plus,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Shield,
  FileText,
  Calendar,
  User,
  Building,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import {
  getAllComplianceRequirementsDrizzle,
  getComplianceStatsDrizzle,
  getAllComplianceSetsDrizzle,
  getAllComplianceRequirementTypesDrizzle,
  getAllIssuingAuthoritiesDrizzle,
  createComplianceRequirementDrizzle,
  updateComplianceRequirementDrizzle,
  deleteComplianceRequirementDrizzle,
  approveComplianceRequirementDrizzle,
  rejectComplianceRequirementDrizzle,
  type ComplianceRequirementRead,
  type ComplianceRequirementCreate,
  type ComplianceStats,
  type ComplianceSetRead,
  type ComplianceRequirementTypeRead,
  type IssuingAuthorityRead,
} from "@/drizzle-actions/compliance";

export default function CompliancePage() {
  const [complianceRequirements, setComplianceRequirements] = useState<ComplianceRequirementRead[]>([]);
  const [stats, setStats] = useState<ComplianceStats | null>(null);
  const [complianceSets, setComplianceSets] = useState<ComplianceSetRead[]>([]);
  const [requirementTypes, setRequirementTypes] = useState<ComplianceRequirementTypeRead[]>([]);
  const [issuingAuthorities, setIssuingAuthorities] = useState<IssuingAuthorityRead[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [complianceSetFilter, setComplianceSetFilter] = useState<string>("all");
  const [showExpiring, setShowExpiring] = useState(false);
  const [showExpired, setShowExpired] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [selectedRequirement, setSelectedRequirement] = useState<ComplianceRequirementRead | null>(null);

  // Form state
  const [formData, setFormData] = useState<Partial<ComplianceRequirementCreate>>({
    partyId: 0,
    complianceSetId: 0,
    requirementTypeId: 0,
    status: "PENDING",
    referenceType: "URL",
    reference: "",
    notes: "",
    issueDate: "",
    expiryDate: "",
  });

  const [reviewNotes, setReviewNotes] = useState("");

  const loadComplianceData = async () => {
    try {
      setLoading(true);
      
      const [
        requirementsResult,
        statsResult,
        setsResult,
        typesResult,
        authoritiesResult,
      ] = await Promise.all([
        getAllComplianceRequirementsDrizzle({
          page,
          limit: 20,
          status: statusFilter === "all" ? undefined : statusFilter as any,
          complianceSetId: complianceSetFilter === "all" ? undefined : parseInt(complianceSetFilter),
          expiringSoon: showExpiring,
          expired: showExpired,
          sortBy: "submittedAt",
          sortOrder: "desc",
        }),
        getComplianceStatsDrizzle(),
        getAllComplianceSetsDrizzle(),
        getAllComplianceRequirementTypesDrizzle(),
        getAllIssuingAuthoritiesDrizzle(),
      ]);

      setComplianceRequirements(requirementsResult.records);
      setTotalPages(requirementsResult.totalPages);
      setStats(statsResult);
      setComplianceSets(setsResult);
      setRequirementTypes(typesResult);
      setIssuingAuthorities(authoritiesResult);
    } catch (error) {
      console.error("Error loading compliance data:", error);
      toast({
        title: "Error",
        description: "Failed to load compliance data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadComplianceData();
  }, [page, statusFilter, complianceSetFilter, showExpiring, showExpired]);

  const handleCreateRequirement = async () => {
    try {
      if (!formData.partyId || !formData.complianceSetId || !formData.requirementTypeId) {
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields",
          variant: "destructive",
        });
        return;
      }

      await createComplianceRequirementDrizzle(formData as ComplianceRequirementCreate);
      
      toast({
        title: "Success",
        description: "Compliance requirement created successfully",
      });
      
      setShowCreateDialog(false);
      setFormData({
        partyId: 0,
        complianceSetId: 0,
        requirementTypeId: 0,
        status: "PENDING",
        referenceType: "URL",
        reference: "",
        notes: "",
        issueDate: "",
        expiryDate: "",
      });
      loadComplianceData();
    } catch (error) {
      console.error("Error creating compliance requirement:", error);
      toast({
        title: "Error",
        description: "Failed to create compliance requirement",
        variant: "destructive",
      });
    }
  };

  const handleApproveRequirement = async (requirement: ComplianceRequirementRead) => {
    try {
      // For now, use a fixed reviewer ID (would come from auth context)
      const reviewerId = 1;
      
      await approveComplianceRequirementDrizzle(requirement.id, reviewerId, reviewNotes || undefined);
      
      toast({
        title: "Success",
        description: "Compliance requirement approved",
      });
      
      setShowReviewDialog(false);
      setSelectedRequirement(null);
      setReviewNotes("");
      loadComplianceData();
    } catch (error) {
      console.error("Error approving compliance requirement:", error);
      toast({
        title: "Error",
        description: "Failed to approve compliance requirement",
        variant: "destructive",
      });
    }
  };

  const handleRejectRequirement = async (requirement: ComplianceRequirementRead) => {
    try {
      if (!reviewNotes.trim()) {
        toast({
          title: "Validation Error",
          description: "Please provide rejection notes",
          variant: "destructive",
        });
        return;
      }
      
      // For now, use a fixed reviewer ID (would come from auth context)
      const reviewerId = 1;
      
      await rejectComplianceRequirementDrizzle(requirement.id, reviewerId, reviewNotes);
      
      toast({
        title: "Success",
        description: "Compliance requirement rejected",
      });
      
      setShowReviewDialog(false);
      setSelectedRequirement(null);
      setReviewNotes("");
      loadComplianceData();
    } catch (error) {
      console.error("Error rejecting compliance requirement:", error);
      toast({
        title: "Error",
        description: "Failed to reject compliance requirement",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status?: string) => {
    const colors = {
      PENDING: "bg-yellow-100 text-yellow-800",
      ACCEPTED: "bg-green-100 text-green-800",
      REJECTED: "bg-red-100 text-red-800",
      INCOMPLETE: "bg-gray-100 text-gray-800",
    };

    return (
      <Badge className={colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800"}>
        {status || "UNKNOWN"}
      </Badge>
    );
  };

  const isExpiringSoon = (expiryDate?: string) => {
    if (!expiryDate) return false;
    const expiry = new Date(expiryDate);
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    return expiry <= thirtyDaysFromNow && expiry > new Date();
  };

  const isExpired = (expiryDate?: string) => {
    if (!expiryDate) return false;
    return new Date(expiryDate) < new Date();
  };

  const filteredRequirements = complianceRequirements.filter(requirement =>
    requirement.requirementType?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    requirement.party?.individual?.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    requirement.party?.individual?.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    requirement.complianceSet?.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading compliance data...</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Compliance Management
        </h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search requirements..."
              className="pl-10 w-[300px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="ACCEPTED">Accepted</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
              <SelectItem value="INCOMPLETE">Incomplete</SelectItem>
            </SelectContent>
          </Select>

          <Select value={complianceSetFilter} onValueChange={setComplianceSetFilter}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by set" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Sets</SelectItem>
              {complianceSets.map(set => (
                <SelectItem key={set.id} value={set.id.toString()}>
                  {set.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant={showExpiring ? "default" : "outline"}
            onClick={() => setShowExpiring(!showExpiring)}
          >
            <Clock className="h-4 w-4 mr-2" />
            Expiring
          </Button>

          <Button
            variant={showExpired ? "default" : "outline"}
            onClick={() => setShowExpired(!showExpired)}
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Expired
          </Button>

          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="bg-[#009639] hover:bg-[#007A2F] text-white">
                <Plus className="h-4 w-4 mr-2" />
                Add Requirement
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create Compliance Requirement</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="partyId">Party ID</Label>
                  <Input
                    id="partyId"
                    type="number"
                    value={formData.partyId || ""}
                    onChange={(e) => setFormData({ ...formData, partyId: parseInt(e.target.value) || 0 })}
                  />
                </div>
                <div>
                  <Label htmlFor="complianceSetId">Compliance Set</Label>
                  <Select
                    value={formData.complianceSetId?.toString() || ""}
                    onValueChange={(value) => setFormData({ ...formData, complianceSetId: parseInt(value) })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select compliance set" />
                    </SelectTrigger>
                    <SelectContent>
                      {complianceSets.map(set => (
                        <SelectItem key={set.id} value={set.id.toString()}>
                          {set.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="requirementTypeId">Requirement Type</Label>
                  <Select
                    value={formData.requirementTypeId?.toString() || ""}
                    onValueChange={(value) => setFormData({ ...formData, requirementTypeId: parseInt(value) })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select requirement type" />
                    </SelectTrigger>
                    <SelectContent>
                      {requirementTypes.map(type => (
                        <SelectItem key={type.id} value={type.id.toString()}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="reference">Reference</Label>
                  <Input
                    id="reference"
                    value={formData.reference || ""}
                    onChange={(e) => setFormData({ ...formData, reference: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="issueDate">Issue Date</Label>
                  <Input
                    id="issueDate"
                    type="date"
                    value={formData.issueDate || ""}
                    onChange={(e) => setFormData({ ...formData, issueDate: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="expiryDate">Expiry Date</Label>
                  <Input
                    id="expiryDate"
                    type="date"
                    value={formData.expiryDate || ""}
                    onChange={(e) => setFormData({ ...formData, expiryDate: e.target.value })}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleCreateRequirement} className="flex-1">
                    Create Requirement
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowCreateDialog(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Requirements</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-[#009639]" />
                <span className="text-2xl font-bold">{stats.totalRequirements}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-yellow-600" />
                <span className="text-2xl font-bold text-yellow-600">{stats.pending}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Accepted</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-2xl font-bold text-green-600">{stats.accepted}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Rejected</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <XCircle className="h-5 w-5 text-red-600" />
                <span className="text-2xl font-bold text-red-600">{stats.rejected}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Incomplete</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-gray-600" />
                <span className="text-2xl font-bold text-gray-600">{stats.incomplete}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-orange-600" />
                <span className="text-2xl font-bold text-orange-600">{stats.expiringSoon}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Expired</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <span className="text-2xl font-bold text-red-500">{stats.expired}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Parties</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                <span className="text-2xl font-bold text-blue-600">{stats.totalPartiesWithCompliance}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Compliance Requirements Table */}
      <Card>
        <CardHeader>
          <CardTitle>Compliance Requirements</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Party</TableHead>
                <TableHead>Requirement</TableHead>
                <TableHead>Set</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Expiry</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRequirements.map((requirement) => (
                <TableRow 
                  key={requirement.id} 
                  className={
                    isExpired(requirement.expiryDate) ? "bg-red-50" : 
                    isExpiringSoon(requirement.expiryDate) ? "bg-orange-50" : ""
                  }
                >
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-[#009639]" />
                      <div>
                        <div className="font-medium">
                          {requirement.party?.individual?.firstName} {requirement.party?.individual?.lastName}
                        </div>
                        <div className="text-sm text-gray-500">
                          Party ID: {requirement.partyId}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{requirement.requirementType?.name}</div>
                      {requirement.requirementType?.description && (
                        <div className="text-sm text-gray-500">
                          {requirement.requirementType.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-blue-600" />
                      <span>{requirement.complianceSet?.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(requirement.status)}
                  </TableCell>
                  <TableCell>
                    {requirement.submittedAt ? 
                      new Date(requirement.submittedAt).toLocaleDateString() : 
                      "Not submitted"
                    }
                  </TableCell>
                  <TableCell>
                    <div>
                      {requirement.expiryDate ? (
                        <div className={
                          isExpired(requirement.expiryDate) ? "text-red-600 font-medium" :
                          isExpiringSoon(requirement.expiryDate) ? "text-orange-600 font-medium" :
                          "text-gray-600"
                        }>
                          {new Date(requirement.expiryDate).toLocaleDateString()}
                        </div>
                      ) : (
                        <span className="text-gray-400">No expiry</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      {requirement.status === "PENDING" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedRequirement(requirement);
                            setShowReviewDialog(true);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      <Button size="sm" variant="outline">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredRequirements.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No compliance requirements found
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-4">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Previous
              </Button>
              <span className="flex items-center px-4">
                Page {page} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Review Dialog */}
      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Review Compliance Requirement</DialogTitle>
          </DialogHeader>
          {selectedRequirement && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Party</Label>
                  <div className="text-sm">
                    {selectedRequirement.party?.individual?.firstName} {selectedRequirement.party?.individual?.lastName}
                  </div>
                </div>
                <div>
                  <Label>Requirement Type</Label>
                  <div className="text-sm">{selectedRequirement.requirementType?.name}</div>
                </div>
                <div>
                  <Label>Compliance Set</Label>
                  <div className="text-sm">{selectedRequirement.complianceSet?.name}</div>
                </div>
                <div>
                  <Label>Status</Label>
                  <div className="text-sm">{getStatusBadge(selectedRequirement.status)}</div>
                </div>
                <div>
                  <Label>Submitted</Label>
                  <div className="text-sm">
                    {selectedRequirement.submittedAt ? 
                      new Date(selectedRequirement.submittedAt).toLocaleDateString() : 
                      "Not submitted"
                    }
                  </div>
                </div>
                <div>
                  <Label>Expiry Date</Label>
                  <div className="text-sm">
                    {selectedRequirement.expiryDate ? 
                      new Date(selectedRequirement.expiryDate).toLocaleDateString() : 
                      "No expiry"
                    }
                  </div>
                </div>
              </div>
              
              {selectedRequirement.reference && (
                <div>
                  <Label>Reference</Label>
                  <div className="text-sm">{selectedRequirement.reference}</div>
                </div>
              )}
              
              {selectedRequirement.notes && (
                <div>
                  <Label>Notes</Label>
                  <div className="text-sm">{selectedRequirement.notes}</div>
                </div>
              )}
              
              <div>
                <Label htmlFor="reviewNotes">Review Notes</Label>
                <Textarea
                  id="reviewNotes"
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  placeholder="Add your review notes here..."
                />
              </div>
              
              <div className="flex gap-2">
                <Button
                  onClick={() => handleApproveRequirement(selectedRequirement)}
                  className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve
                </Button>
                <Button
                  onClick={() => handleRejectRequirement(selectedRequirement)}
                  variant="outline"
                  className="flex-1 text-red-600 border-red-600 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowReviewDialog(false);
                    setSelectedRequirement(null);
                    setReviewNotes("");
                  }}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
