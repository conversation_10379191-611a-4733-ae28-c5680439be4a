"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Plus,
  Car,
  Tag,
  Eye,
  DollarSign,
  Percent,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function ListingsPage() {
  // Mock data - replace with actual API call
  const listings = [
    {
      id: 1,
      vehicle: "Toyota Fortuner",
      group: "Family SUV",
      price: 225000,
      fractionPrice: 45000,
      fractionSize: "20%",
      availableFractions: 3,
      totalFractions: 5,
      location: "Cape Town",
      status: "Active",
      listedDate: "2024-01-15",
      views: 120,
    },
    {
      id: 2,
      vehicle: "VW Polo",
      group: "Weekend Getaway",
      price: 180000,
      fractionPrice: 36000,
      fractionSize: "20%",
      availableFractions: 2,
      totalFractions: 5,
      location: "Johannesburg",
      status: "Active",
      listedDate: "2024-01-20",
      views: 85,
    },
    {
      id: 3,
      vehicle: "Honda Civic",
      group: "Work Commute",
      price: 220000,
      fractionPrice: 55000,
      fractionSize: "25%",
      availableFractions: 0,
      totalFractions: 4,
      location: "Durban",
      status: "Sold Out",
      listedDate: "2024-01-10",
      views: 150,
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Vehicle Listings</h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search listings..."
              className="pl-10 w-[300px]"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
          <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Listing
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Active Listings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Car className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold">18</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Available Fractions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Percent className="h-5 w-5 text-blue-600" />
              <span className="text-2xl font-bold text-blue-600">42</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold">R 4.2M</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Views</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-gray-600" />
              <span className="text-2xl font-bold">1,245</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Listings</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vehicle</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Fraction Price</TableHead>
                <TableHead>Available</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Views</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {listings.map((listing) => (
                <TableRow key={listing.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4 text-[#009639]" />
                      <Link
                        href={`/admin/vehicles/${listing.id}`}
                        className="text-[#009639] hover:text-[#007A2F]"
                      >
                        {listing.vehicle}
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/groups/${listing.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {listing.group}
                    </Link>
                  </TableCell>
                  <TableCell>R {listing.price.toLocaleString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <span>R {listing.fractionPrice.toLocaleString()}</span>
                      <span className="text-xs text-gray-500">
                        ({listing.fractionSize})
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {listing.availableFractions}/{listing.totalFractions}{" "}
                    fractions
                  </TableCell>
                  <TableCell>{listing.location}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        listing.status === "Active"
                          ? "bg-green-100 text-green-800"
                          : listing.status === "Sold Out"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {listing.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Eye className="h-3 w-3 text-gray-500" />
                      {listing.views}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/listings/${listing.id}`}
                      className="text-[#009639] hover:text-[#007A2F] font-medium"
                    >
                      View Details
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
