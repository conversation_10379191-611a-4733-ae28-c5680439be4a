"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Wrench,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Car,
  Plus,
  Edit,
  Trash2,
  Eye,
  Clock,
  DollarSign,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import {
  getAllVehicleMaintenanceDrizzle,
  getVehicleMaintenanceStatsDrizzle,
  createVehicleMaintenanceDrizzle,
  updateVehicleMaintenanceDrizzle,
  deleteVehicleMaintenanceDrizzle,
  completeVehicleMaintenanceDrizzle,
  type VehicleMaintenanceRead,
  type VehicleMaintenanceCreate,
} from "@/drizzle-actions/vehicle-maintenance";

interface MaintenanceStats {
  totalMaintenance: number;
  scheduled: number;
  pending: number;
  completed: number;
  overdue: number;
  dueSoon: number;
  totalCostEstimated: number;
  totalCostActual: number;
}

export default function MaintenancePage() {
  const [maintenanceRecords, setMaintenanceRecords] = useState<VehicleMaintenanceRead[]>([]);
  const [stats, setStats] = useState<MaintenanceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [showOverdue, setShowOverdue] = useState(false);
  const [showDueSoon, setShowDueSoon] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<VehicleMaintenanceRead | null>(null);

  // Form state
  const [formData, setFormData] = useState<Partial<VehicleMaintenanceCreate>>({
    vehicleId: 0,
    name: "",
    description: "",
    dueDate: "",
    dueOdometer: 0,
    status: "SCHEDULED",
    expectedCost: 0,
    serviceProvider: "",
    isScheduled: true,
  });

  const loadMaintenanceData = async () => {
    try {
      setLoading(true);
      
      const [recordsResult, statsResult] = await Promise.all([
        getAllVehicleMaintenanceDrizzle({
          page,
          limit: 20,
          status: statusFilter === "all" ? undefined : statusFilter as any,
          overdue: showOverdue,
          dueSoon: showDueSoon,
          sortBy: "dueDate",
          sortOrder: "asc",
        }),
        getVehicleMaintenanceStatsDrizzle(),
      ]);

      setMaintenanceRecords(recordsResult.records);
      setTotalPages(recordsResult.totalPages);
      setStats(statsResult);
    } catch (error) {
      console.error("Error loading maintenance data:", error);
      toast({
        title: "Error",
        description: "Failed to load maintenance data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMaintenanceData();
  }, [page, statusFilter, showOverdue, showDueSoon]);

  const handleCreateMaintenance = async () => {
    try {
      if (!formData.vehicleId || !formData.name || !formData.dueDate) {
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields",
          variant: "destructive",
        });
        return;
      }

      await createVehicleMaintenanceDrizzle(formData as VehicleMaintenanceCreate);
      
      toast({
        title: "Success",
        description: "Maintenance record created successfully",
      });
      
      setShowCreateDialog(false);
      setFormData({
        vehicleId: 0,
        name: "",
        description: "",
        dueDate: "",
        dueOdometer: 0,
        status: "SCHEDULED",
        expectedCost: 0,
        serviceProvider: "",
        isScheduled: true,
      });
      loadMaintenanceData();
    } catch (error) {
      console.error("Error creating maintenance:", error);
      toast({
        title: "Error",
        description: "Failed to create maintenance record",
        variant: "destructive",
      });
    }
  };

  const handleUpdateMaintenance = async () => {
    try {
      if (!selectedRecord || !formData.name || !formData.dueDate) {
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields",
          variant: "destructive",
        });
        return;
      }

      await updateVehicleMaintenanceDrizzle(selectedRecord.id, formData);
      
      toast({
        title: "Success",
        description: "Maintenance record updated successfully",
      });
      
      setShowEditDialog(false);
      setSelectedRecord(null);
      loadMaintenanceData();
    } catch (error) {
      console.error("Error updating maintenance:", error);
      toast({
        title: "Error",
        description: "Failed to update maintenance record",
        variant: "destructive",
      });
    }
  };

  const handleDeleteMaintenance = async (id: number) => {
    try {
      if (confirm("Are you sure you want to delete this maintenance record?")) {
        await deleteVehicleMaintenanceDrizzle(id);
        
        toast({
          title: "Success",
          description: "Maintenance record deleted successfully",
        });
        
        loadMaintenanceData();
      }
    } catch (error) {
      console.error("Error deleting maintenance:", error);
      toast({
        title: "Error",
        description: "Failed to delete maintenance record",
        variant: "destructive",
      });
    }
  };

  const handleCompleteMaintenance = async (record: VehicleMaintenanceRead) => {
    try {
      const actualCost = prompt("Enter actual cost:");
      if (actualCost === null) return;

      const technicianNotes = prompt("Enter technician notes (optional):");
      
      await completeVehicleMaintenanceDrizzle(record.id, {
        completedDate: new Date().toISOString(),
        actualCost: actualCost ? parseFloat(actualCost) : undefined,
        technicianNotes: technicianNotes || undefined,
      });
      
      toast({
        title: "Success",
        description: "Maintenance marked as completed",
      });
      
      loadMaintenanceData();
    } catch (error) {
      console.error("Error completing maintenance:", error);
      toast({
        title: "Error",
        description: "Failed to complete maintenance",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      SCHEDULED: "default",
      PENDING: "secondary",
      COMPLETED: "default",
    } as const;

    const colors = {
      SCHEDULED: "bg-blue-100 text-blue-800",
      PENDING: "bg-yellow-100 text-yellow-800",
      COMPLETED: "bg-green-100 text-green-800",
    };

    return (
      <Badge className={colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800"}>
        {status}
      </Badge>
    );
  };

  const isOverdue = (dueDate: string, status: string) => {
    return new Date(dueDate) < new Date() && status === "PENDING";
  };

  const filteredRecords = maintenanceRecords.filter(record =>
    record.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.vehicle?.vinNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.serviceProvider?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading maintenance data...</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Vehicle Maintenance Management
        </h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search maintenance..."
              className="pl-10 w-[300px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="SCHEDULED">Scheduled</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="COMPLETED">Completed</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant={showOverdue ? "default" : "outline"}
            onClick={() => setShowOverdue(!showOverdue)}
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Overdue
          </Button>

          <Button
            variant={showDueSoon ? "default" : "outline"}
            onClick={() => setShowDueSoon(!showDueSoon)}
          >
            <Clock className="h-4 w-4 mr-2" />
            Due Soon
          </Button>

          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="bg-[#009639] hover:bg-[#007A2F] text-white">
                <Plus className="h-4 w-4 mr-2" />
                Schedule Maintenance
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Schedule New Maintenance</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="vehicleId">Vehicle ID</Label>
                  <Input
                    id="vehicleId"
                    type="number"
                    value={formData.vehicleId || ""}
                    onChange={(e) => setFormData({ ...formData, vehicleId: parseInt(e.target.value) || 0 })}
                  />
                </div>
                <div>
                  <Label htmlFor="name">Maintenance Name</Label>
                  <Input
                    id="name"
                    value={formData.name || ""}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description || ""}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="dueDate">Due Date</Label>
                  <Input
                    id="dueDate"
                    type="datetime-local"
                    value={formData.dueDate || ""}
                    onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="dueOdometer">Due Odometer</Label>
                  <Input
                    id="dueOdometer"
                    type="number"
                    value={formData.dueOdometer || ""}
                    onChange={(e) => setFormData({ ...formData, dueOdometer: parseInt(e.target.value) || 0 })}
                  />
                </div>
                <div>
                  <Label htmlFor="expectedCost">Expected Cost</Label>
                  <Input
                    id="expectedCost"
                    type="number"
                    step="0.01"
                    value={formData.expectedCost || ""}
                    onChange={(e) => setFormData({ ...formData, expectedCost: parseFloat(e.target.value) || 0 })}
                  />
                </div>
                <div>
                  <Label htmlFor="serviceProvider">Service Provider</Label>
                  <Input
                    id="serviceProvider"
                    value={formData.serviceProvider || ""}
                    onChange={(e) => setFormData({ ...formData, serviceProvider: e.target.value })}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleCreateMaintenance} className="flex-1">
                    Create Maintenance
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowCreateDialog(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Maintenance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Wrench className="h-5 w-5 text-[#009639]" />
                <span className="text-2xl font-bold">{stats.totalMaintenance}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-2xl font-bold text-green-600">{stats.completed}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Due Soon</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-blue-600" />
                <span className="text-2xl font-bold text-blue-600">{stats.dueSoon}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <span className="text-2xl font-bold text-red-600">{stats.overdue}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-yellow-600" />
                <span className="text-2xl font-bold text-yellow-600">{stats.pending}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-500" />
                <span className="text-2xl font-bold text-blue-500">{stats.scheduled}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Estimated Cost</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-purple-600" />
                <span className="text-2xl font-bold text-purple-600">
                  R{stats.totalCostEstimated.toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Actual Cost</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-500" />
                <span className="text-2xl font-bold text-green-500">
                  R{stats.totalCostActual.toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Maintenance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Maintenance Records</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vehicle</TableHead>
                <TableHead>Maintenance</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Provider</TableHead>
                <TableHead>Cost</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRecords.map((record) => (
                <TableRow key={record.id} className={isOverdue(record.dueDate, record.status) ? "bg-red-50" : ""}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4 text-[#009639]" />
                      <div>
                        <div className="font-medium">
                          {record.vehicle?.model?.make?.name} {record.vehicle?.model?.model}
                        </div>
                        <div className="text-sm text-gray-500">
                          {record.vehicle?.vinNumber}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{record.name}</div>
                      {record.description && (
                        <div className="text-sm text-gray-500">{record.description}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div>{new Date(record.dueDate).toLocaleDateString()}</div>
                      <div className="text-sm text-gray-500">
                        Odometer: {record.dueOdometer.toLocaleString()} km
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(record.status)}
                  </TableCell>
                  <TableCell>{record.serviceProvider || "Not assigned"}</TableCell>
                  <TableCell>
                    <div>
                      <div>Est: R{record.expectedCost.toLocaleString()}</div>
                      {record.actualCost && (
                        <div className="text-sm text-green-600">
                          Actual: R{record.actualCost.toLocaleString()}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      {record.status === "PENDING" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCompleteMaintenance(record)}
                        >
                          <CheckCircle className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setSelectedRecord(record);
                          setFormData({
                            vehicleId: record.vehicleId,
                            name: record.name,
                            description: record.description,
                            dueDate: record.dueDate,
                            dueOdometer: record.dueOdometer,
                            status: record.status,
                            expectedCost: record.expectedCost,
                            serviceProvider: record.serviceProvider,
                            isScheduled: record.isScheduled,
                          });
                          setShowEditDialog(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteMaintenance(record.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredRecords.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No maintenance records found
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2 mt-4">
              <Button
                variant="outline"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Previous
              </Button>
              <span className="flex items-center px-4">
                Page {page} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Maintenance Record</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Maintenance Name</Label>
              <Input
                id="edit-name"
                value={formData.name || ""}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description || ""}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="edit-dueDate">Due Date</Label>
              <Input
                id="edit-dueDate"
                type="datetime-local"
                value={formData.dueDate || ""}
                onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="edit-status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value as any })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="edit-expectedCost">Expected Cost</Label>
              <Input
                id="edit-expectedCost"
                type="number"
                step="0.01"
                value={formData.expectedCost || ""}
                onChange={(e) => setFormData({ ...formData, expectedCost: parseFloat(e.target.value) || 0 })}
              />
            </div>
            <div>
              <Label htmlFor="edit-serviceProvider">Service Provider</Label>
              <Input
                id="edit-serviceProvider"
                value={formData.serviceProvider || ""}
                onChange={(e) => setFormData({ ...formData, serviceProvider: e.target.value })}
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={handleUpdateMaintenance} className="flex-1">
                Update Maintenance
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditDialog(false);
                  setSelectedRecord(null);
                }}
                className="flex-1"
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
