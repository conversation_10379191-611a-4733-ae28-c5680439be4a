"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  Search,
  Filter,
  ChevronDown,
  MoreHorizontal,
  Trash2,
  Edit,
  UserPlus,
  Users,
  User,
  UserCheck,
  UserX,
  Calendar,
  Download,
  ArrowRight,
  Mail,
  Phone,
  MapPin,
  Clock,
  Activity,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  BarChart2,
  PieChart,
  TrendingUp,
  TrendingDown,
  Globe,
  Smartphone,
  Laptop,
  RefreshCw,
  FileText,
  Eye,
  Plus,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import {
  Bar<PERSON>hart,
  Bar,
  LineChart,
  Line,
  <PERSON>hart as RechartsPie<PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  AreaChart,
  Area,
  LabelList,
} from "recharts";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

// Mock user data
const usersData = [
  {
    id: 1,
    name: "Sipho Nkosi",
    email: "<EMAIL>",
    phone: "+27 71 234 5678",
    location: "Johannesburg, GP",
    joinDate: "20/01/2025",
    lastActive: "2 hours ago",
    status: "active",
    role: "Individual User",
    verified: true,
    groups: 2,
    vehicles: 1,
    bookings: 8,
    score: 16.7,
    scoreReasoning: "50% (1/2)",
    time: "00:53",
    scoreAnalysis: 100,
    scoreGeneric: "0% (0/2)",
    dateJoined: "2023-05-12",
  },
  {
    id: 2,
    name: "Thabo Molefe",
    email: "<EMAIL>",
    phone: "+27 82 345 6789",
    location: "Cape Town, WC",
    joinDate: "15/01/2025",
    lastActive: "5 minutes ago",
    status: "active",
    role: "Group Admin",
    verified: true,
    groups: 1,
    vehicles: 3,
    bookings: 12,
    score: 19.7,
    scoreReasoning: "100% (2/2)",
    time: "01:00",
    scoreAnalysis: 0,
    scoreGeneric: "0% (0/2)",
    dateJoined: "2023-06-24",
  },
  {
    id: 3,
    name: "Mandla Zulu",
    email: "<EMAIL>",
    phone: "+27 63 456 7890",
    location: "Durban, KZN",
    joinDate: "05/02/2025",
    lastActive: "1 day ago",
    status: "inactive",
    role: "Individual User",
    verified: false,
    groups: 0,
    vehicles: 1,
    bookings: 3,
    score: 13.7,
    scoreReasoning: "0% (0/2)",
    time: "01:01",
    scoreAnalysis: 50,
    scoreGeneric: "0% (0/2)",
    dateJoined: "2023-07-15",
  },
  {
    id: 4,
    name: "Lerato Mabaso",
    email: "<EMAIL>",
    phone: "+27 84 567 8901",
    location: "Pretoria, GP",
    joinDate: "10/02/2025",
    lastActive: "3 hours ago",
    status: "active",
    role: "Individual User",
    verified: true,
    groups: 1,
    vehicles: 0,
    bookings: 5,
    score: 19.7,
    scoreReasoning: "25% (0/2)",
    time: "01:01",
    scoreAnalysis: 75,
    scoreGeneric: "0% (0/2)",
    dateJoined: "2023-08-01",
  },
  {
    id: 5,
    name: "Nomsa Ndlovu",
    email: "<EMAIL>",
    phone: "+27 76 678 9012",
    location: "Bloemfontein, FS",
    joinDate: "01/03/2025",
    lastActive: "Just now",
    status: "active",
    role: "Partner",
    verified: true,
    groups: 3,
    vehicles: 5,
    bookings: 20,
    score: 11.2,
    scoreReasoning: "100% (2/2)",
    time: "01:01",
    scoreAnalysis: 0,
    scoreGeneric: "0% (0/2)",
    dateJoined: "2023-09-10",
  },
  {
    id: 6,
    name: "Pieter van der Merwe",
    email: "<EMAIL>",
    phone: "+27 83 789 0123",
    location: "Stellenbosch, WC",
    joinDate: "15/03/2025",
    lastActive: "4 days ago",
    status: "suspended",
    role: "Individual User",
    verified: true,
    groups: 0,
    vehicles: 1,
    bookings: 1,
    score: 6.2,
    scoreReasoning: "0% (0/2)",
    time: "01:01",
    scoreAnalysis: 100,
    scoreGeneric: "0% (0/2)",
    dateJoined: "2023-10-15",
  },
  {
    id: 7,
    name: "Johan Botha",
    email: "<EMAIL>",
    phone: "+27 72 890 1234",
    location: "Port Elizabeth, EC",
    joinDate: "20/03/2025",
    lastActive: "1 hour ago",
    status: "active",
    role: "Group Admin",
    verified: true,
    groups: 2,
    vehicles: 4,
    bookings: 15,
    score: 28.3,
    scoreReasoning: "100% (2/2)",
    time: "01:01",
    scoreAnalysis: 25,
    scoreGeneric: "0% (0/2)",
    dateJoined: "2023-11-01",
  },
  {
    id: 8,
    name: "Themba Khumalo",
    email: "<EMAIL>",
    phone: "+27 78 901 2345",
    location: "Nelspruit, MP",
    joinDate: "05/04/2025",
    lastActive: "2 days ago",
    status: "active",
    role: "Individual User",
    verified: true,
    groups: 1,
    vehicles: 1,
    bookings: 7,
    score: 16.6,
    scoreReasoning: "50% (1/2)",
    time: "01:01",
    scoreAnalysis: 50,
    scoreGeneric: "0% (0/2)",
    dateJoined: "2023-12-01",
  },
  {
    id: 9,
    name: "Andile Ngcobo",
    email: "<EMAIL>",
    phone: "+27 79 012 3456",
    location: "Kimberley, NC",
    joinDate: "10/04/2025",
    lastActive: "6 hours ago",
    status: "active",
    role: "Individual User",
    verified: false,
    groups: 0,
    vehicles: 0,
    bookings: 0,
    score: 19.6,
    scoreReasoning: "25% (0/2)",
    time: "01:01",
    scoreAnalysis: 75,
    scoreGeneric: "0% (0/2)",
    dateJoined: "2024-01-01",
  },
  {
    id: 10,
    name: "Francois du Plessis",
    email: "<EMAIL>",
    phone: "+27 74 123 4567",
    location: "George, WC",
    joinDate: "15/04/2025",
    lastActive: "3 days ago",
    status: "active",
    role: "Partner",
    verified: true,
    groups: 1,
    vehicles: 2,
    bookings: 9,
    score: 10.3,
    scoreReasoning: "100% (2/2)",
    time: "01:01",
    scoreAnalysis: 100,
    scoreGeneric: "0% (0/2)",
    dateJoined: "2024-02-01",
  },
];

// User growth data
const userGrowthData = [
  { month: "Jan", newUsers: 120, activeUsers: 980, churnRate: 2.1 },
  { month: "Feb", newUsers: 145, activeUsers: 1050, churnRate: 1.8 },
  { month: "Mar", newUsers: 165, activeUsers: 1120, churnRate: 2.3 },
  { month: "Apr", newUsers: 190, activeUsers: 1180, churnRate: 2.5 },
  { month: "May", newUsers: 210, activeUsers: 1220, churnRate: 2.8 },
  { month: "Jun", newUsers: 180, activeUsers: 1240, churnRate: 3.0 },
  { month: "Jul", newUsers: 160, activeUsers: 1230, churnRate: 2.7 },
];

// User activity data
const userActivityData = [
  { day: "Mon", logins: 450, bookings: 120, groupActivities: 85 },
  { day: "Tue", logins: 520, bookings: 150, groupActivities: 95 },
  { day: "Wed", logins: 580, bookings: 170, groupActivities: 110 },
  { day: "Thu", logins: 620, bookings: 190, groupActivities: 130 },
  { day: "Fri", logins: 750, bookings: 210, groupActivities: 150 },
  { day: "Sat", logins: 400, bookings: 100, groupActivities: 70 },
  { day: "Sun", logins: 350, bookings: 80, groupActivities: 60 },
];

// User by role data
const usersByRoleData = [
  { name: "Individual Users", value: 65, color: "#009639" },
  { name: "Group Admins", value: 20, color: "#007A2F" },
  { name: "Partners", value: 15, color: "#FFD700" },
];

// User by status data
const usersByStatusData = [
  { name: "Active", value: 80, color: "#009639" },
  { name: "Inactive", value: 15, color: "#6c757d" },
  { name: "Suspended", value: 5, color: "#dc3545" },
];

// User by verification data
const usersByVerificationData = [
  { name: "Verified", value: 75, color: "#009639" },
  { name: "Pending", value: 20, color: "#ffc107" },
  { name: "Rejected", value: 5, color: "#dc3545" },
];

// Geographic distribution data
const geoDistributionData = [
  { province: "Gauteng", users: 35, color: "#009639" },
  { province: "Western Cape", users: 25, color: "#007A2F" },
  { province: "KwaZulu-Natal", users: 15, color: "#FFD700" },
  { province: "Eastern Cape", users: 10, color: "#20c997" },
  { province: "Free State", users: 5, color: "#6c757d" },
  { province: "Other", users: 10, color: "#adb5bd" },
];

// Device usage data
const deviceUsageData = [
  { name: "Mobile", value: 65, color: "#009639" },
  { name: "Desktop", value: 25, color: "#007A2F" },
  { name: "Tablet", value: 10, color: "#FFD700" },
];

// User retention data
const retentionData = [
  { month: "1 Month", rate: 85 },
  { month: "3 Months", rate: 70 },
  { month: "6 Months", rate: 60 },
  { month: "1 Year", rate: 45 },
];

// User at risk data
const usersAtRiskData = [
  {
    id: 3,
    name: "Mandla Zulu",
    avatar: "/placeholder.svg?height=40&width=40",
    reason: "Inactive for 30+ days",
    riskLevel: "high",
  },
  {
    id: 6,
    name: "Pieter van der Merwe",
    avatar: "/placeholder.svg?height=40&width=40",
    reason: "Multiple failed bookings",
    riskLevel: "medium",
  },
  {
    id: 9,
    name: "Andile Ngcobo",
    avatar: "/placeholder.svg?height=40&width=40",
    reason: "Incomplete profile",
    riskLevel: "low",
  },
];

// Key metrics data
const keyMetricsData = [
  {
    name: "Total Users",
    value: 1247,
    change: "****%",
    icon: <Users size={18} />,
    color: "#009639",
  },
  {
    name: "Active Users",
    value: 980,
    change: "+5.2%",
    icon: <UserCheck size={18} />,
    color: "#007A2F",
  },
  {
    name: "New This Week",
    value: 45,
    change: "+12.5%",
    icon: <UserPlus size={18} />,
    color: "#20c997",
  },
  {
    name: "Verification Rate",
    value: "75%",
    change: "+3.1%",
    icon: <Shield size={18} />,
    color: "#FFD700",
  },
];

export default function UserManagement() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [statusFilter, setStatusFilter] = useState("All");
  const [roleFilter, setRoleFilter] = useState("All");
  const [activeTab, setActiveTab] = useState("all");

  // Handle select all users
  const handleSelectAll = () => {
    if (selectedUsers.length === usersData.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(usersData.map((user) => user.id));
    }
  };

  // Handle select individual user
  const handleSelectUser = (userId: number) => {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter((id) => id !== userId));
    } else {
      setSelectedUsers([...selectedUsers, userId]);
    }
  };

  // Filter users based on search query and filters
  const filteredUsers = usersData.filter((user) => {
    const matchesSearch = user.name
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesStatus =
      statusFilter === "All" ||
      user.status.toLowerCase() === statusFilter.toLowerCase();
    const matchesRole =
      roleFilter === "All" || user.role === roleFilter;

    return matchesSearch && matchesStatus && matchesRole;
  });

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-[#00963920] text-[#009639]";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "suspended":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Statistics data
  const statsData = {
    totalUsers: usersData.length,
    activeUsers: usersData.filter(user => user.status.toLowerCase() === "active").length,
    verificationRate: Math.round(usersData.filter(user => user.verified).length / usersData.length * 100),
    newUsers: 45,
  };

  // Handle view user details
  const handleViewUser = (user: any) => {
    router.push(`/admin/users/${user.id}`);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">User Management</h1>
        <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
          <UserPlus size={16} />
          Add User
        </Button>
      </div>

      {/* Statistics Section */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{statsData.totalUsers}</div>
              <div className="p-2 bg-[#00963915] rounded-full">
                <Users size={20} className="text-[#009639]" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500">Active Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{statsData.activeUsers}</div>
              <div className="p-2 bg-[#00963915] rounded-full">
                <UserCheck size={20} className="text-[#009639]" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500">Verification Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{statsData.verificationRate}%</div>
              <div className="p-2 bg-[#00963915] rounded-full">
                <Shield size={20} className="text-[#009639]" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500">New Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{statsData.newUsers}</div>
              <div className="p-2 bg-[#00963915] rounded-full">
                <UserPlus size={20} className="text-[#009639]" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <BarChart2 size={18} className="text-[#009639] mr-2" />
              User Growth
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[250px]">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={userGrowthData}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="newUsers"
                    stackId="1"
                    stroke="#009639"
                    fill="#00963950"
                  />
                  <Area
                    type="monotone"
                    dataKey="activeUsers"
                    stackId="2"
                    stroke="#007A2F"
                    fill="#007A2F50"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <PieChart size={18} className="text-[#009639] mr-2" />
              User Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[250px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie
                    data={usersByRoleData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {usersByRoleData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters Section */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                type="text"
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex flex-col md:flex-row gap-4">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <div className="flex items-center">
                    <Filter size={16} className="mr-2 text-gray-400" />
                    <SelectValue placeholder="Status" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                </SelectContent>
              </Select>

              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-[180px]">
                  <div className="flex items-center">
                    <UserCheck size={16} className="mr-2 text-gray-400" />
                    <SelectValue placeholder="Role" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All Roles</SelectItem>
                  <SelectItem value="Individual User">Individual User</SelectItem>
                  <SelectItem value="Group Admin">Group Admin</SelectItem>
                  <SelectItem value="Partner">Partner</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg flex items-center">
              <Users size={18} className="text-[#009639] mr-2" />
              Users
            </CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="text-gray-600">
                <Download size={16} className="mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <input
                    type="checkbox"
                    className="rounded text-[#009639] focus:ring-[#009639]"
                    checked={selectedUsers.length === usersData.length}
                    onChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date Joined</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow 
                  key={user.id}
                  className="cursor-pointer hover:bg-gray-50"
                  onClick={() => handleViewUser(user)}
                >
                  <TableCell onClick={(e) => e.stopPropagation()}>
                    <input
                      type="checkbox"
                      className="rounded text-[#009639] focus:ring-[#009639]"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => handleSelectUser(user.id)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-lg bg-[#e6ffe6] flex items-center justify-center mr-3">
                        <User size={20} className="text-[#009639]" />
                      </div>
                      <span className="font-medium">{user.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={
                      user.role === "Admin" 
                        ? "bg-purple-100 text-purple-800" 
                        : user.role === "Moderator"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                    }>
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getStatusColor(user.status)}>
                      {user.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {user.dateJoined}
                  </TableCell>
                  <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                    <div className="flex justify-end space-x-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8 text-gray-500 hover:text-gray-700"
                        onClick={() => router.push(`/admin/users/${user.id}`)}
                      >
                        <Eye size={16} />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8 text-gray-500 hover:text-gray-700"
                      >
                        <Edit size={16} />
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-8 w-8 text-gray-500 hover:text-gray-700"
                          >
                            <MoreHorizontal size={16} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Mail size={16} className="mr-2" />
                            Send Email
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Shield size={16} className="mr-2" />
                            Verify User
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <AlertTriangle size={16} className="mr-2" />
                            Suspend User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Additional User Analytics Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <Activity size={18} className="text-[#009639] mr-2" />
              User Retention
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[250px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={retentionData}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="rate" fill="#009639" radius={[4, 4, 0, 0]}>
                    <LabelList dataKey="rate" position="top" formatter={(value: number) => `${value}%`} />
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <AlertTriangle size={18} className="text-[#009639] mr-2" />
              Users at Risk
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {usersAtRiskData.map((user) => (
                <div key={user.id} className="flex items-center p-3 border border-gray-100 rounded-lg hover:border-gray-200 transition-colors">
                  <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden mr-3">
                    <Image
                      src={user.avatar}
                      alt={user.name}
                      width={40}
                      height={40}
                    />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{user.name}</div>
                    <div className="text-xs text-gray-500">{user.reason}</div>
                  </div>
                  <Badge variant="outline" className={user.riskLevel === "high" ? "bg-red-100 text-red-800" : "bg-yellow-100 text-yellow-800"}>
                    {user.riskLevel}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
