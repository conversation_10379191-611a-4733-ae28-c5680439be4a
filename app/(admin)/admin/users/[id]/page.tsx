"use client";

import React from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Car, Users, Calendar, Activity, UserCog, Mail, Phone } from "lucide-react";
import Link from "next/link";

export default function UserDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const userId = params.id;

  // Mock data - replace with actual API call
  const user = {
    id: userId,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+**************",
    role: "Individual",
    status: "Active",
    joinedDate: "2024-01-15",
    groups: [
      { id: 1, name: "Family Car Share", role: "Owner", members: 4 },
      { id: 2, name: "Business Fleet", role: "Member", members: 6 },
    ],
    vehicles: [
      {
        id: 1,
        name: "Toyota Fortuner",
        ownership: "30%",
        status: "Active",
      },
      {
        id: 2,
        name: "VW Polo",
        ownership: "50%",
        status: "Pending Handover",
      },
    ],
    bookings: [
      {
        id: 1,
        vehicle: "Toyota Fortuner",
        date: "2024-02-01",
        status: "Completed",
      },
      {
        id: 2,
        vehicle: "VW Polo",
        date: "2024-02-15",
        status: "Upcoming",
      },
    ],
  };

  return (
    <div className="p-6">
      <Button
        variant="ghost"
        className="flex items-center text-gray-600 hover:text-gray-800 mb-4"
        onClick={() => router.back()}
      >
        <ArrowLeft size={16} className="mr-2" />
        Back to Users
      </Button>

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">{user.name}</h1>
          <p className="text-gray-500">User ID: {userId}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <UserCog size={16} />
            Edit User
          </Button>
          <Button variant="outline" className="text-[#dc3545] border-[#dc3545] hover:bg-[#dc354510] hover:text-[#b02a37]">
            Suspend Account
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Users size={18} className="text-[#009639] mr-2" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Email:</span>
                <span className="text-sm font-medium flex items-center">
                  <Mail size={14} className="mr-1 text-gray-400" />
                  {user.email}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Phone:</span>
                <span className="text-sm font-medium flex items-center">
                  <Phone size={14} className="mr-1 text-gray-400" />
                  {user.phone}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Role:</span>
                <span className="text-sm font-medium">{user.role}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Status:</span>
                <Badge variant={user.status === "Active" ? "default" : "secondary"} className={user.status === "Active" ? "bg-[#00963920] text-[#009639] hover:bg-[#00963930] border-[#00963950]" : "bg-gray-100 text-gray-800"}>
                  {user.status}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Joined Date:</span>
                <span className="text-sm font-medium">{user.joinedDate}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Users size={18} className="text-[#009639] mr-2" />
              Groups
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {user.groups.map((group) => (
                <div
                  key={group.id}
                  className="p-3 border border-gray-100 rounded-lg hover:border-gray-200 transition-colors"
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{group.name}</p>
                      <p className="text-sm text-gray-500">
                        {group.role} • {group.members} members
                      </p>
                    </div>
                    <Button variant="ghost" size="sm" asChild className="text-[#009639] hover:text-[#007A2F]">
                      <Link href={`/admin/groups/${group.id}`}>
                        View
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Car size={18} className="text-[#009639] mr-2" />
              Vehicles
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {user.vehicles.map((vehicle) => (
                <div
                  key={vehicle.id}
                  className="p-3 border border-gray-100 rounded-lg hover:border-gray-200 transition-colors"
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{vehicle.name}</p>
                      <p className="text-sm text-gray-500">
                        {vehicle.ownership} ownership • 
                        <Badge variant="outline" className={vehicle.status === "Active" ? "ml-1 bg-[#00963920] text-[#009639] border-[#00963950]" : "ml-1 bg-yellow-100 text-yellow-800"}>
                          {vehicle.status}
                        </Badge>
                      </p>
                    </div>
                    <Button variant="ghost" size="sm" asChild className="text-[#009639] hover:text-[#007A2F]">
                      <Link href={`/admin/vehicles/${vehicle.id}`}>
                        View
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="bookings" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="bookings" className="px-4">
            <Calendar size={16} className="mr-2" />
            Bookings
          </TabsTrigger>
          <TabsTrigger value="activity" className="px-4">
            <Activity size={16} className="mr-2" />
            Activity
          </TabsTrigger>
        </TabsList>

        <TabsContent value="bookings">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Calendar size={18} className="text-[#009639] mr-2" />
                Booking History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {user.bookings.map((booking) => (
                  <div
                    key={booking.id}
                    className="p-3 border border-gray-100 rounded-lg hover:border-gray-200 transition-colors"
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">{booking.vehicle}</p>
                        <p className="text-sm text-gray-500">
                          {booking.date} • 
                          <Badge variant="outline" className={booking.status === "Completed" ? "ml-1 bg-[#00963920] text-[#009639] border-[#00963950]" : "ml-1 bg-blue-100 text-blue-800"}>
                            {booking.status}
                          </Badge>
                        </p>
                      </div>
                      <Button variant="ghost" size="sm" asChild className="text-[#009639] hover:text-[#007A2F]">
                        <Link href={`/admin/bookings/${booking.id}`}>
                          View
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Activity size={18} className="text-[#009639] mr-2" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 border border-gray-100 rounded-lg">
                  <p className="font-medium">Group Joined</p>
                  <p className="text-sm text-gray-500">
                    Joined "Family Car Share" group • 2024-01-20
                  </p>
                </div>
                <div className="p-3 border border-gray-100 rounded-lg">
                  <p className="font-medium">Vehicle Purchase</p>
                  <p className="text-sm text-gray-500">
                    Purchased 30% share in Toyota Fortuner • 2024-01-15
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
