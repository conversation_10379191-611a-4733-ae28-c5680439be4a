"use client";

import React, { useState } from "react";
import {
  Search,
  Plus,
  Filter,
  ChevronDown,
  MoreHorizontal,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  Clock,
  User,
  Calendar,
  FileText,
  Eye,
  Edit,
  Trash2,
} from "lucide-react";

// Mock dispute data
const disputesData = [
  {
    id: 1,
    title: "Vehicle Damage Dispute",
    description: "Dispute regarding damage to vehicle during shared use",
    status: "Open",
    priority: "High",
    createdDate: "15/01/2025",
    lastUpdated: "16/01/2025",
    createdBy: "Sipho Nkosi",
    assignedTo: "Admin",
    involvedParties: ["Sipho Nkosi", "Thabo Molefe"],
    category: "Vehicle Damage",
    groupId: "GRP-001",
    vehicleId: "VEH-001",
  },
  {
    id: 2,
    title: "Payment Dispute",
    description: "Dispute regarding monthly payment contribution",
    status: "In Progress",
    priority: "Medium",
    createdDate: "10/01/2025",
    lastUpdated: "12/01/2025",
    createdBy: "Lerato Mabaso",
    assignedTo: "Admin",
    involvedParties: ["Lerato Mabaso", "Mandla Zulu", "Nomsa Ndlovu"],
    category: "Payment",
    groupId: "GRP-002",
    vehicleId: "VEH-002",
  },
  {
    id: 3,
    title: "Scheduling Conflict",
    description: "Dispute regarding vehicle usage schedule",
    status: "Resolved",
    priority: "Low",
    createdDate: "05/01/2025",
    lastUpdated: "08/01/2025",
    createdBy: "Johan Botha",
    assignedTo: "Group Admin",
    involvedParties: ["Johan Botha", "Pieter van der Merwe"],
    category: "Scheduling",
    groupId: "GRP-003",
    vehicleId: "VEH-003",
  },
  {
    id: 4,
    title: "Maintenance Cost Dispute",
    description: "Dispute regarding shared maintenance costs",
    status: "Open",
    priority: "Medium",
    createdDate: "28/12/2024",
    lastUpdated: "30/12/2024",
    createdBy: "Themba Khumalo",
    assignedTo: "Admin",
    involvedParties: ["Themba Khumalo", "Andile Ngcobo", "Francois du Plessis"],
    category: "Maintenance",
    groupId: "GRP-004",
    vehicleId: "VEH-004",
  },
  {
    id: 5,
    title: "Insurance Claim Dispute",
    description: "Dispute regarding insurance claim process",
    status: "In Progress",
    priority: "High",
    createdDate: "20/12/2024",
    lastUpdated: "22/12/2024",
    createdBy: "Nomsa Ndlovu",
    assignedTo: "Insurance Partner",
    involvedParties: ["Nomsa Ndlovu", "Sipho Nkosi"],
    category: "Insurance",
    groupId: "GRP-001",
    vehicleId: "VEH-001",
  },
  {
    id: 6,
    title: "Fuel Cost Sharing Dispute",
    description: "Dispute regarding fuel cost sharing arrangement",
    status: "Resolved",
    priority: "Low",
    createdDate: "15/12/2024",
    lastUpdated: "18/12/2024",
    createdBy: "Mandla Zulu",
    assignedTo: "Group Admin",
    involvedParties: ["Mandla Zulu", "Lerato Mabaso", "Thabo Molefe"],
    category: "Fuel Costs",
    groupId: "GRP-002",
    vehicleId: "VEH-002",
  },
  {
    id: 7,
    title: "Vehicle Return Condition Dispute",
    description: "Dispute regarding vehicle condition upon return",
    status: "Closed",
    priority: "Medium",
    createdDate: "10/12/2024",
    lastUpdated: "15/12/2024",
    createdBy: "Pieter van der Merwe",
    assignedTo: "Admin",
    involvedParties: ["Pieter van der Merwe", "Johan Botha"],
    category: "Vehicle Condition",
    groupId: "GRP-003",
    vehicleId: "VEH-003",
  },
  {
    id: 8,
    title: "Unauthorized Usage Dispute",
    description: "Dispute regarding unauthorized vehicle usage",
    status: "In Progress",
    priority: "High",
    createdDate: "05/12/2024",
    lastUpdated: "08/12/2024",
    createdBy: "Andile Ngcobo",
    assignedTo: "Admin",
    involvedParties: ["Andile Ngcobo", "Themba Khumalo"],
    category: "Usage Violation",
    groupId: "GRP-004",
    vehicleId: "VEH-004",
  },
];

export default function DisputeResolution() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDisputes, setSelectedDisputes] = useState<number[]>([]);
  const [statusFilter, setStatusFilter] = useState("All");
  const [priorityFilter, setPriorityFilter] = useState("All");
  const [categoryFilter, setCategoryFilter] = useState("All");
  const [selectedDispute, setSelectedDispute] = useState<any>(null);

  // Handle select all disputes
  const handleSelectAll = () => {
    if (selectedDisputes.length === disputesData.length) {
      setSelectedDisputes([]);
    } else {
      setSelectedDisputes(disputesData.map((dispute) => dispute.id));
    }
  };

  // Handle select individual dispute
  const handleSelectDispute = (disputeId: number) => {
    if (selectedDisputes.includes(disputeId)) {
      setSelectedDisputes(selectedDisputes.filter((id) => id !== disputeId));
    } else {
      setSelectedDisputes([...selectedDisputes, disputeId]);
    }
  };

  // Filter disputes based on search query and filters
  const filteredDisputes = disputesData.filter((dispute) => {
    const matchesSearch =
      dispute.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      dispute.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      dispute.createdBy.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus =
      statusFilter === "All" || dispute.status === statusFilter;
    
    const matchesPriority =
      priorityFilter === "All" || dispute.priority === priorityFilter;
    
    const matchesCategory =
      categoryFilter === "All" || dispute.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
  });

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Open":
        return "bg-yellow-100 text-yellow-600";
      case "In Progress":
        return "bg-blue-100 text-blue-600";
      case "Resolved":
        return "bg-green-100 text-green-600";
      case "Closed":
        return "bg-gray-100 text-gray-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  // Get priority badge color
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-600";
      case "Medium":
        return "bg-orange-100 text-orange-600";
      case "Low":
        return "bg-green-100 text-green-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  // View dispute details
  const viewDisputeDetails = (dispute: any) => {
    setSelectedDispute(dispute);
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Dispute Resolution</h1>
        <p className="text-gray-500">
          Manage and resolve disputes between members
        </p>
      </div>

      {selectedDispute ? (
        // Dispute Details View
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="p-6 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-xl font-semibold">{selectedDispute.title}</h2>
            <button
              onClick={() => setSelectedDispute(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              Back to List
            </button>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="text-lg font-medium mb-4">Dispute Information</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">
                      Status
                    </span>
                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full inline-block mt-1 ${getStatusBadge(
                        selectedDispute.status
                      )}`}
                    >
                      {selectedDispute.status}
                    </span>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-500 block">
                      Priority
                    </span>
                    <span
                      className={`px-3 py-1 text-xs font-medium rounded-full inline-block mt-1 ${getPriorityBadge(
                        selectedDispute.priority
                      )}`}
                    >
                      {selectedDispute.priority}
                    </span>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-500 block">
                      Category
                    </span>
                    <span className="text-sm text-gray-700">
                      {selectedDispute.category}
                    </span>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-500 block">
                      Created Date
                    </span>
                    <span className="text-sm text-gray-700 flex items-center">
                      <Calendar size={14} className="mr-1 text-gray-400" />
                      {selectedDispute.createdDate}
                    </span>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-500 block">
                      Last Updated
                    </span>
                    <span className="text-sm text-gray-700 flex items-center">
                      <Calendar size={14} className="mr-1 text-gray-400" />
                      {selectedDispute.lastUpdated}
                    </span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Parties Involved</h3>
                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-gray-500 block">
                      Created By
                    </span>
                    <span className="text-sm text-gray-700 flex items-center">
                      <User size={14} className="mr-1 text-gray-400" />
                      {selectedDispute.createdBy}
                    </span>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-500 block">
                      Assigned To
                    </span>
                    <span className="text-sm text-gray-700 flex items-center">
                      <User size={14} className="mr-1 text-gray-400" />
                      {selectedDispute.assignedTo}
                    </span>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-500 block">
                      Involved Parties
                    </span>
                    <div className="mt-1 space-y-1">
                      {selectedDispute.involvedParties.map(
                        (party: string, index: number) => (
                          <span
                            key={index}
                            className="text-sm text-gray-700 flex items-center"
                          >
                            <User size={14} className="mr-1 text-gray-400" />
                            {party}
                          </span>
                        )
                      )}
                    </div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-500 block">
                      Group ID
                    </span>
                    <span className="text-sm text-gray-700">
                      {selectedDispute.groupId}
                    </span>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-500 block">
                      Vehicle ID
                    </span>
                    <span className="text-sm text-gray-700">
                      {selectedDispute.vehicleId}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Description</h3>
              <p className="text-sm text-gray-700 bg-gray-50 p-4 rounded-md">
                {selectedDispute.description}
              </p>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Resolution Actions</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Update Status
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent">
                    <option>Open</option>
                    <option>In Progress</option>
                    <option>Resolved</option>
                    <option>Closed</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Assign To
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent">
                    <option>Admin</option>
                    <option>Group Admin</option>
                    <option>Insurance Partner</option>
                    <option>Legal Team</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Resolution Notes
                  </label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                    rows={4}
                    placeholder="Enter resolution notes..."
                  ></textarea>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F]">
                Save Resolution
              </button>
              <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50">
                Contact Parties
              </button>
              <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50">
                Request More Information
              </button>
            </div>
          </div>
        </div>
      ) : (
        // Disputes List View
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0">
              <div className="relative">
                <Search
                  size={18}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Search disputes..."
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="flex flex-wrap items-center gap-3">
                <div className="relative">
                  <select
                    className="appearance-none pl-4 pr-10 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                  >
                    <option value="All">All Statuses</option>
                    <option value="Open">Open</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Resolved">Resolved</option>
                    <option value="Closed">Closed</option>
                  </select>
                  <ChevronDown
                    size={14}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
                  />
                </div>

                <div className="relative">
                  <select
                    className="appearance-none pl-4 pr-10 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                    value={priorityFilter}
                    onChange={(e) => setPriorityFilter(e.target.value)}
                  >
                    <option value="All">All Priorities</option>
                    <option value="High">High</option>
                    <option value="Medium">Medium</option>
                    <option value="Low">Low</option>
                  </select>
                  <ChevronDown
                    size={14}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
                  />
                </div>

                <div className="relative">
                  <select
                    className="appearance-none pl-4 pr-10 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                  >
                    <option value="All">All Categories</option>
                    <option value="Vehicle Damage">Vehicle Damage</option>
                    <option value="Payment">Payment</option>
                    <option value="Scheduling">Scheduling</option>
                    <option value="Maintenance">Maintenance</option>
                    <option value="Insurance">Insurance</option>
                    <option value="Fuel Costs">Fuel Costs</option>
                    <option value="Vehicle Condition">Vehicle Condition</option>
                    <option value="Usage Violation">Usage Violation</option>
                  </select>
                  <ChevronDown
                    size={14}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"
                  />
                </div>

                <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center">
                  <Plus size={16} className="mr-2" />
                  New Dispute
                </button>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      className="rounded text-[#009639] focus:ring-[#009639]"
                      checked={
                        selectedDisputes.length === disputesData.length &&
                        disputesData.length > 0
                      }
                      onChange={handleSelectAll}
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created By
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredDisputes.map((dispute) => (
                  <tr
                    key={dispute.id}
                    className="border-b border-gray-200 hover:bg-gray-50"
                  >
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        checked={selectedDisputes.includes(dispute.id)}
                        onChange={() => handleSelectDispute(dispute.id)}
                      />
                    </td>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">
                      {dispute.title}
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(
                          dispute.status
                        )}`}
                      >
                        {dispute.status}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${getPriorityBadge(
                          dispute.priority
                        )}`}
                      >
                        {dispute.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {dispute.category}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {dispute.createdBy}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1 text-gray-400" />
                        {dispute.createdDate}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      <div className="flex space-x-2">
                        <button
                          className="p-1 text-gray-400 hover:text-gray-600"
                          onClick={() => viewDisputeDetails(dispute)}
                        >
                          <Eye size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Edit size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <MessageSquare size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="px-6 py-4 flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Showing 1 to {filteredDisputes.length} of {filteredDisputes.length}{" "}
              entries
            </div>

            <div className="flex items-center space-x-1">
              <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
                &lt;
              </button>
              <button className="px-3 py-1 border border-gray-200 rounded-md text-sm bg-[#009639] text-white">
                1
              </button>
              <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
                &gt;
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
