"use client";

import React, { useState } from "react";
import {
  Search,
  Plus,
  Bell,
  FileText,
  HelpCircle,
  Edit,
  Trash2,
  Eye,
  Calendar,
  User,
  Tag,
  MapPin,
  File,
  Download,
} from "lucide-react";

// Mock content data

// Mock document data
const documentsData = [
  {
    id: 1,
    title: "Vehicle Registration Certificate",
    category: "Vehicle Documents",
    uploadDate: "15/01/2025",
    expiryDate: "15/01/2026",
    status: "Valid",
    entityType: "Vehicle",
    entityId: "VEH-001",
    fileType: "PDF",
    fileSize: "1.2 MB",
    uploadedBy: "Admin",
  },
  {
    id: 2,
    title: "Insurance Policy",
    category: "Insurance",
    uploadDate: "10/01/2025",
    expiryDate: "10/01/2026",
    status: "Valid",
    entityType: "Group",
    entityId: "GRP-002",
    fileType: "PDF",
    fileSize: "2.5 MB",
    uploadedBy: "Group Admin",
  },
  {
    id: 3,
    title: "Driver's License",
    category: "Personal Documents",
    uploadDate: "05/01/2025",
    expiryDate: "05/01/2030",
    status: "Valid",
    entityType: "User",
    entityId: "USR-003",
    fileType: "JPG",
    fileSize: "0.8 MB",
    uploadedBy: "User",
  },
  {
    id: 4,
    title: "Vehicle Service Record",
    category: "Maintenance",
    uploadDate: "28/12/2024",
    expiryDate: "N/A",
    status: "Valid",
    entityType: "Vehicle",
    entityId: "VEH-001",
    fileType: "PDF",
    fileSize: "3.1 MB",
    uploadedBy: "Service Partner",
  },
  {
    id: 5,
    title: "Group Agreement",
    category: "Legal",
    uploadDate: "20/12/2024",
    expiryDate: "20/12/2025",
    status: "Valid",
    entityType: "Group",
    entityId: "GRP-002",
    fileType: "PDF",
    fileSize: "1.5 MB",
    uploadedBy: "Legal Team",
  },
  {
    id: 6,
    title: "Vehicle Inspection Report",
    category: "Compliance",
    uploadDate: "15/12/2024",
    expiryDate: "15/06/2025",
    status: "Valid",
    entityType: "Vehicle",
    entityId: "VEH-003",
    fileType: "PDF",
    fileSize: "2.2 MB",
    uploadedBy: "Compliance Officer",
  },
  {
    id: 7,
    title: "Roadworthy Certificate",
    category: "Compliance",
    uploadDate: "10/12/2024",
    expiryDate: "10/12/2025",
    status: "Valid",
    entityType: "Vehicle",
    entityId: "VEH-004",
    fileType: "PDF",
    fileSize: "1.0 MB",
    uploadedBy: "Admin",
  },
  {
    id: 8,
    title: "Partner Agreement",
    category: "Legal",
    uploadDate: "05/12/2024",
    expiryDate: "05/12/2026",
    status: "Valid",
    entityType: "Partner",
    entityId: "PTR-001",
    fileType: "PDF",
    fileSize: "2.8 MB",
    uploadedBy: "Legal Team",
  },
];

// Mock content data
const notificationsData = [
  {
    id: 1,
    title: "New Feature Announcement",
    type: "App Notification",
    status: "Published",
    audience: "All Users",
    publishDate: "15/01/2025",
    createdBy: "Admin",
  },
  {
    id: 2,
    title: "Maintenance Scheduled",
    type: "App Notification",
    status: "Scheduled",
    audience: "All Users",
    publishDate: "25/01/2025",
    createdBy: "Admin",
  },
  {
    id: 3,
    title: "Freedom Day Special Offer",
    type: "Marketing",
    status: "Draft",
    audience: "Active Users",
    publishDate: "Pending",
    createdBy: "Marketing Team",
  },
  {
    id: 4,
    title: "POPIA Compliance Update",
    type: "Policy",
    status: "Published",
    audience: "All Users",
    publishDate: "10/01/2025",
    createdBy: "Legal Team",
  },
  {
    id: 5,
    title: "New Cape Town Partner Announcement",
    type: "Marketing",
    status: "Published",
    audience: "All Users",
    publishDate: "05/01/2025",
    createdBy: "Marketing Team",
  },
  {
    id: 6,
    title: "Fuel Price Update Alert",
    type: "App Notification",
    status: "Published",
    audience: "All Users",
    publishDate: "01/01/2025",
    createdBy: "Admin",
  },
  {
    id: 7,
    title: "Johannesburg Service Expansion",
    type: "Marketing",
    status: "Scheduled",
    audience: "Gauteng Users",
    publishDate: "20/01/2025",
    createdBy: "Marketing Team",
  },
];

const helpContentData = [
  {
    id: 1,
    title: "Getting Started Guide",
    category: "Onboarding",
    lastUpdated: "15/01/2025",
    status: "Published",
  },
  {
    id: 2,
    title: "How to Create a Group",
    category: "Groups",
    lastUpdated: "10/01/2025",
    status: "Published",
  },
  {
    id: 3,
    title: "Vehicle Sharing Guidelines",
    category: "Vehicles",
    lastUpdated: "05/01/2025",
    status: "Published",
  },
  {
    id: 4,
    title: "Payment and Billing FAQ",
    category: "Billing",
    lastUpdated: "28/12/2024",
    status: "Published",
  },
  {
    id: 5,
    title: "Troubleshooting Common Issues",
    category: "Support",
    lastUpdated: "20/12/2024",
    status: "Draft",
  },
  {
    id: 6,
    title: "South African Driving Laws and Regulations",
    category: "Legal",
    lastUpdated: "18/12/2024",
    status: "Published",
  },
  {
    id: 7,
    title: "Cape Town Parking Guide",
    category: "Local",
    lastUpdated: "15/12/2024",
    status: "Published",
  },
  {
    id: 8,
    title: "Johannesburg Traffic Tips",
    category: "Local",
    lastUpdated: "12/12/2024",
    status: "Published",
  },
  {
    id: 9,
    title: "E-Toll Information for Gauteng Users",
    category: "Local",
    lastUpdated: "10/12/2024",
    status: "Published",
  },
];

const policiesData = [
  {
    id: 1,
    title: "Terms of Service",
    lastUpdated: "15/01/2025",
    status: "Published",
    region: "Global",
  },
  {
    id: 2,
    title: "Privacy Policy",
    lastUpdated: "10/01/2025",
    status: "Published",
    region: "Global",
  },
  {
    id: 3,
    title: "Data Protection Policy (POPIA Compliance)",
    lastUpdated: "05/01/2025",
    status: "Published",
    region: "South Africa",
  },
  {
    id: 4,
    title: "Cookie Policy",
    lastUpdated: "28/12/2024",
    status: "Published",
    region: "Global",
  },
  {
    id: 5,
    title: "Acceptable Use Policy",
    lastUpdated: "20/12/2024",
    status: "Draft",
    region: "Global",
  },
  {
    id: 6,
    title: "South African Consumer Protection Act Compliance",
    lastUpdated: "15/12/2024",
    status: "Published",
    region: "South Africa",
  },
  {
    id: 7,
    title: "National Road Traffic Act Guidelines",
    lastUpdated: "10/12/2024",
    status: "Published",
    region: "South Africa",
  },
  {
    id: 8,
    title: "Electronic Communications Act Compliance",
    lastUpdated: "05/12/2024",
    status: "Published",
    region: "South Africa",
  },
];

export default function ContentManagement() {
  const [activeTab, setActiveTab] = useState("notifications");
  const [searchQuery, setSearchQuery] = useState("");

  // Filter content based on search query and active tab
  const getFilteredContent = () => {
    const query = searchQuery.toLowerCase();

    switch (activeTab) {
      case "notifications":
        return notificationsData.filter(
          (item) =>
            item.title.toLowerCase().includes(query) ||
            item.type.toLowerCase().includes(query)
        );
      case "help":
        return helpContentData.filter(
          (item) =>
            item.title.toLowerCase().includes(query) ||
            item.category.toLowerCase().includes(query)
        );
      case "policies":
        return policiesData.filter(
          (item) =>
            item.title.toLowerCase().includes(query) ||
            item.region.toLowerCase().includes(query)
        );
      case "documents":
        return documentsData.filter(
          (item) =>
            item.title.toLowerCase().includes(query) ||
            item.category.toLowerCase().includes(query) ||
            item.entityType.toLowerCase().includes(query) ||
            item.status.toLowerCase().includes(query)
        );
      default:
        return [];
    }
  };

  const filteredContent = getFilteredContent();

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Published":
        return "bg-green-100 text-green-600";
      case "Draft":
        return "bg-gray-100 text-gray-600";
      case "Scheduled":
        return "bg-blue-100 text-blue-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Content Management</h1>
        <p className="text-gray-500">
          Manage app notifications, help content, and policies
        </p>
      </div>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            className={`px-6 py-4 text-sm font-medium ${
              activeTab === "notifications"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("notifications")}
          >
            <Bell size={16} className="inline-block mr-2" />
            Notifications
          </button>
          <button
            className={`px-6 py-4 text-sm font-medium ${
              activeTab === "help"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("help")}
          >
            <HelpCircle size={16} className="inline-block mr-2" />
            Help Content
          </button>
          <button
            className={`px-6 py-4 text-sm font-medium ${
              activeTab === "policies"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("policies")}
          >
            <FileText size={16} className="inline-block mr-2" />
            Policies
          </button>
          <button
            className={`px-6 py-4 text-sm font-medium ${
              activeTab === "documents"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("documents")}
          >
            <File size={16} className="inline-block mr-2" />
            Documents
          </button>
        </div>

        {/* Content Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div className="relative">
              <Search
                size={18}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                placeholder={`Search ${activeTab}...`}
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center">
              <Plus size={16} className="mr-2" />
              {activeTab === "notifications"
                ? "Create Notification"
                : activeTab === "help"
                ? "Add Help Article"
                : activeTab === "policies"
                ? "Add Policy"
                : "Upload Document"}
            </button>
          </div>
        </div>

        {/* Content Table */}
        <div className="overflow-x-auto">
          {activeTab === "notifications" && (
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Audience
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Publish Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created By
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredContent.map((item: any) => (
                  <tr
                    key={item.id}
                    className="border-b border-gray-200 hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.title}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${
                          item.type === "App Notification"
                            ? "bg-purple-100 text-purple-600"
                            : item.type === "Marketing"
                            ? "bg-blue-100 text-blue-600"
                            : "bg-yellow-100 text-yellow-600"
                        }`}
                      >
                        {item.type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(
                          item.status
                        )}`}
                      >
                        {item.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.audience}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1 text-gray-400" />
                        {item.publishDate}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <User size={14} className="mr-1 text-gray-400" />
                        {item.createdBy}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex space-x-2">
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Eye size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Edit size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}

          {activeTab === "help" && (
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Updated
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredContent.map((item: any) => (
                  <tr
                    key={item.id}
                    className="border-b border-gray-200 hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.title}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <Tag size={14} className="mr-1 text-gray-400" />
                        {item.category}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1 text-gray-400" />
                        {item.lastUpdated}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(
                          item.status
                        )}`}
                      >
                        {item.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex space-x-2">
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Eye size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Edit size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}

          {activeTab === "policies" && (
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Updated
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Region
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredContent.map((item: any) => (
                  <tr
                    key={item.id}
                    className="border-b border-gray-200 hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.title}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1 text-gray-400" />
                        {item.lastUpdated}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(
                          item.status
                        )}`}
                      >
                        {item.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <MapPin size={14} className="mr-1 text-gray-400" />
                        {item.region}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex space-x-2">
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Eye size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Edit size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}

          {activeTab === "documents" && (
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Entity Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Upload Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expiry Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredContent.map((item: any) => (
                  <tr
                    key={item.id}
                    className="border-b border-gray-200 hover:bg-gray-50"
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.title}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.category}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.entityType}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1 text-gray-400" />
                        {item.uploadDate}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1 text-gray-400" />
                        {item.expiryDate}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span
                        className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusBadge(
                          item.status
                        )}`}
                      >
                        {item.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex space-x-2">
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Eye size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Download size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Edit size={16} />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-gray-600">
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing 1 to {filteredContent.length} of {filteredContent.length}{" "}
            entries
          </div>

          <div className="flex items-center space-x-1">
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
              &lt;
            </button>
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm bg-[#009639] text-white">
              1
            </button>
            <button className="px-3 py-1 border border-gray-200 rounded-md text-sm text-gray-500 hover:bg-gray-50">
              &gt;
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
