"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  FileText,
  CheckCircle,
  Alert<PERSON>riangle,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function LegalEntitiesPage() {
  // Mock data - replace with actual API call
  const legalEntities = [
    {
      id: 1,
      name: "Family SUV Trust",
      group: "Family SUV",
      type: "Trust",
      status: "Registered",
      submissionDate: "2024-01-10",
      approvalDate: "2024-01-15",
      documents: 5,
    },
    {
      id: 2,
      name: "Weekend Getaway PTY",
      group: "Weekend Getaway",
      type: "Company",
      status: "Pending",
      submissionDate: "2024-01-20",
      approvalDate: "-",
      documents: 3,
    },
    {
      id: 3,
      name: "Work Commute CC",
      group: "Work Commute",
      type: "Close Corporation",
      status: "Under Review",
      submissionDate: "2024-01-18",
      approvalDate: "-",
      documents: 4,
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Legal Entity Submissions
        </h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search legal entities..."
              className="pl-10 w-[300px]"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Submissions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold">15</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Registered</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-2xl font-bold text-green-600">8</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Under Review</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              <span className="text-2xl font-bold text-yellow-600">4</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Pending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <span className="text-2xl font-bold text-blue-600">3</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Legal Entity Submissions</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Entity Name</TableHead>
                <TableHead>Group</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submission Date</TableHead>
                <TableHead>Approval Date</TableHead>
                <TableHead>Documents</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {legalEntities.map((entity) => (
                <TableRow key={entity.id}>
                  <TableCell className="font-medium">{entity.name}</TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/groups/${entity.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {entity.group}
                    </Link>
                  </TableCell>
                  <TableCell>{entity.type}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        entity.status === "Registered"
                          ? "bg-green-100 text-green-800"
                          : entity.status === "Under Review"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {entity.status}
                    </span>
                  </TableCell>
                  <TableCell>{entity.submissionDate}</TableCell>
                  <TableCell>{entity.approvalDate}</TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/legal-entities/${entity.id}/documents`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {entity.documents} Documents
                    </Link>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/legal-entities/${entity.id}`}
                      className="text-[#009639] hover:text-[#007A2F] font-medium"
                    >
                      View Details
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
