"use client";

import React, { useState } from "react";
import {
  Save,
  Bell,
  Mail,
  Shield,
  Globe,
  Database,
  Lock,
  CreditCard,
  Users,
  FileText,
  AlertTriangle,
} from "lucide-react";

export default function AdminSettings() {
  const [activeTab, setActiveTab] = useState("general");

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Settings</h1>
        <p className="text-gray-500">
          Configure system settings and preferences
        </p>
      </div>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden">
        <div className="flex border-b border-gray-200">
          <button
            className={`px-6 py-4 text-sm font-medium ${
              activeTab === "general"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("general")}
          >
            General
          </button>
          <button
            className={`px-6 py-4 text-sm font-medium ${
              activeTab === "notifications"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("notifications")}
          >
            Notifications
          </button>
          <button
            className={`px-6 py-4 text-sm font-medium ${
              activeTab === "security"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("security")}
          >
            Security
          </button>
          <button
            className={`px-6 py-4 text-sm font-medium ${
              activeTab === "billing"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("billing")}
          >
            Billing
          </button>
          <button
            className={`px-6 py-4 text-sm font-medium ${
              activeTab === "integrations"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("integrations")}
          >
            Integrations
          </button>
        </div>

        <div className="p-6">
          {activeTab === "general" && (
            <div>
              <h2 className="text-lg font-semibold mb-6">General Settings</h2>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Platform Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                    defaultValue="Poolly Admin"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Email
                  </label>
                  <input
                    type="email"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                    defaultValue="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Default Language
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent">
                    <option>English (South Africa)</option>
                    <option>Afrikaans</option>
                    <option>isiZulu</option>
                    <option>isiXhosa</option>
                    <option>Sesotho</option>
                    <option>Setswana</option>
                    <option>Sepedi</option>
                    <option>Tshivenda</option>
                    <option>Xitsonga</option>
                    <option>siSwati</option>
                    <option>isiNdebele</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Time Zone
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent">
                    <option>
                      Africa/Johannesburg (SAST - South Africa Standard Time)
                    </option>
                  </select>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="maintenance-mode"
                    className="rounded text-[#009639] focus:ring-[#009639]"
                  />
                  <label
                    htmlFor="maintenance-mode"
                    className="ml-2 text-sm text-gray-700"
                  >
                    Enable Maintenance Mode
                  </label>
                </div>

                <div>
                  <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center">
                    <Save size={16} className="mr-2" />
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === "notifications" && (
            <div>
              <h2 className="text-lg font-semibold mb-6">
                Notification Settings
              </h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-md font-medium mb-3">
                    Email Notifications
                  </h3>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Mail size={18} className="text-gray-400 mr-2" />
                        <span className="text-sm text-gray-700">
                          New User Registration
                        </span>
                      </div>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          id="email-new-user"
                          className="sr-only"
                          defaultChecked
                        />
                        <label
                          htmlFor="email-new-user"
                          className="block h-6 rounded-full cursor-pointer bg-gray-300 before:absolute before:h-4 before:w-4 before:left-1 before:top-1 before:rounded-full before:bg-white before:transition-all checked:before:left-5 peer-checked:bg-[#009639]"
                        ></label>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Mail size={18} className="text-gray-400 mr-2" />
                        <span className="text-sm text-gray-700">
                          New Group Creation
                        </span>
                      </div>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          id="email-new-group"
                          className="sr-only"
                          defaultChecked
                        />
                        <label
                          htmlFor="email-new-group"
                          className="block h-6 rounded-full cursor-pointer bg-gray-300 before:absolute before:h-4 before:w-4 before:left-1 before:top-1 before:rounded-full before:bg-white before:transition-all checked:before:left-5 peer-checked:bg-[#009639]"
                        ></label>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Mail size={18} className="text-gray-400 mr-2" />
                        <span className="text-sm text-gray-700">
                          Compliance Alerts
                        </span>
                      </div>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          id="email-compliance"
                          className="sr-only"
                          defaultChecked
                        />
                        <label
                          htmlFor="email-compliance"
                          className="block h-6 rounded-full cursor-pointer bg-gray-300 before:absolute before:h-4 before:w-4 before:left-1 before:top-1 before:rounded-full before:bg-white before:transition-all checked:before:left-5 peer-checked:bg-[#009639]"
                        ></label>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-md font-medium mb-3">
                    System Notifications
                  </h3>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Bell size={18} className="text-gray-400 mr-2" />
                        <span className="text-sm text-gray-700">
                          New User Registration
                        </span>
                      </div>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          id="system-new-user"
                          className="sr-only"
                          defaultChecked
                        />
                        <label
                          htmlFor="system-new-user"
                          className="block h-6 rounded-full cursor-pointer bg-gray-300 before:absolute before:h-4 before:w-4 before:left-1 before:top-1 before:rounded-full before:bg-white before:transition-all checked:before:left-5 peer-checked:bg-[#009639]"
                        ></label>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Bell size={18} className="text-gray-400 mr-2" />
                        <span className="text-sm text-gray-700">
                          System Alerts
                        </span>
                      </div>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          id="system-alerts"
                          className="sr-only"
                          defaultChecked
                        />
                        <label
                          htmlFor="system-alerts"
                          className="block h-6 rounded-full cursor-pointer bg-gray-300 before:absolute before:h-4 before:w-4 before:left-1 before:top-1 before:rounded-full before:bg-white before:transition-all checked:before:left-5 peer-checked:bg-[#009639]"
                        ></label>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Bell size={18} className="text-gray-400 mr-2" />
                        <span className="text-sm text-gray-700">
                          Partner Updates
                        </span>
                      </div>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          id="system-partner"
                          className="sr-only"
                          defaultChecked
                        />
                        <label
                          htmlFor="system-partner"
                          className="block h-6 rounded-full cursor-pointer bg-gray-300 before:absolute before:h-4 before:w-4 before:left-1 before:top-1 before:rounded-full before:bg-white before:transition-all checked:before:left-5 peer-checked:bg-[#009639]"
                        ></label>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center">
                    <Save size={16} className="mr-2" />
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === "security" && (
            <div>
              <h2 className="text-lg font-semibold mb-6">Security Settings</h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-md font-medium mb-3">Authentication</h3>

                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="two-factor"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        defaultChecked
                      />
                      <label
                        htmlFor="two-factor"
                        className="ml-2 text-sm text-gray-700"
                      >
                        Require Two-Factor Authentication for Admin Users
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="password-complexity"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        defaultChecked
                      />
                      <label
                        htmlFor="password-complexity"
                        className="ml-2 text-sm text-gray-700"
                      >
                        Enforce Password Complexity Requirements
                      </label>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Session Timeout (minutes)
                      </label>
                      <input
                        type="number"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                        defaultValue="30"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-md font-medium mb-3">Data Protection</h3>

                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="data-encryption"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        defaultChecked
                      />
                      <label
                        htmlFor="data-encryption"
                        className="ml-2 text-sm text-gray-700"
                      >
                        Enable Data Encryption
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="audit-logging"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        defaultChecked
                      />
                      <label
                        htmlFor="audit-logging"
                        className="ml-2 text-sm text-gray-700"
                      >
                        Enable Audit Logging
                      </label>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Data Retention Period (days)
                      </label>
                      <input
                        type="number"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                        defaultValue="90"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center">
                    <Save size={16} className="mr-2" />
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === "billing" && (
            <div>
              <h2 className="text-lg font-semibold mb-6">Billing Settings</h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-md font-medium mb-3">Payment Gateways</h3>

                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="gateway-stripe"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        defaultChecked
                      />
                      <label
                        htmlFor="gateway-stripe"
                        className="ml-2 text-sm text-gray-700"
                      >
                        Stripe
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="gateway-payfast"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        defaultChecked
                      />
                      <label
                        htmlFor="gateway-payfast"
                        className="ml-2 text-sm text-gray-700"
                      >
                        PayFast
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="gateway-ozow"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        defaultChecked
                      />
                      <label
                        htmlFor="gateway-ozow"
                        className="ml-2 text-sm text-gray-700"
                      >
                        Ozow
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="gateway-snapscan"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                      />
                      <label
                        htmlFor="gateway-snapscan"
                        className="ml-2 text-sm text-gray-700"
                      >
                        SnapScan
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="gateway-zapper"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                      />
                      <label
                        htmlFor="gateway-zapper"
                        className="ml-2 text-sm text-gray-700"
                      >
                        Zapper
                      </label>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-md font-medium mb-3">Invoice Settings</h3>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Invoice Prefix
                      </label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                        defaultValue="POOLLY-ZA-"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Default Payment Terms (days)
                      </label>
                      <input
                        type="number"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                        defaultValue="30"
                      />
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="auto-reminders"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        defaultChecked
                      />
                      <label
                        htmlFor="auto-reminders"
                        className="ml-2 text-sm text-gray-700"
                      >
                        Send Automatic Payment Reminders
                      </label>
                    </div>
                  </div>
                </div>

                <div>
                  <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center">
                    <Save size={16} className="mr-2" />
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === "integrations" && (
            <div>
              <h2 className="text-lg font-semibold mb-6">
                Integration Settings
              </h2>

              <div className="space-y-6">
                <div>
                  <h3 className="text-md font-medium mb-3">
                    API Configuration
                  </h3>

                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="enable-api"
                        className="rounded text-[#009639] focus:ring-[#009639]"
                        defaultChecked
                      />
                      <label
                        htmlFor="enable-api"
                        className="ml-2 text-sm text-gray-700"
                      >
                        Enable API Access
                      </label>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        API Rate Limit (requests per minute)
                      </label>
                      <input
                        type="number"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                        defaultValue="60"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-md font-medium mb-3">
                    Third-Party Services
                  </h3>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center mr-3">
                          <Globe size={18} className="text-blue-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">Google Maps API</p>
                          <p className="text-xs text-gray-500">
                            Location services integration
                          </p>
                        </div>
                      </div>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          id="google-maps"
                          className="sr-only"
                          defaultChecked
                        />
                        <label
                          htmlFor="google-maps"
                          className="block h-6 rounded-full cursor-pointer bg-gray-300 before:absolute before:h-4 before:w-4 before:left-1 before:top-1 before:rounded-full before:bg-white before:transition-all checked:before:left-5 peer-checked:bg-[#009639]"
                        ></label>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center mr-3">
                          <Mail size={18} className="text-green-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">SendGrid</p>
                          <p className="text-xs text-gray-500">
                            Email service provider
                          </p>
                        </div>
                      </div>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          id="sendgrid"
                          className="sr-only"
                          defaultChecked
                        />
                        <label
                          htmlFor="sendgrid"
                          className="block h-6 rounded-full cursor-pointer bg-gray-300 before:absolute before:h-4 before:w-4 before:left-1 before:top-1 before:rounded-full before:bg-white before:transition-all checked:before:left-5 peer-checked:bg-[#009639]"
                        ></label>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center mr-3">
                          <Bell size={18} className="text-purple-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">Twilio</p>
                          <p className="text-xs text-gray-500">
                            SMS notifications
                          </p>
                        </div>
                      </div>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          id="twilio"
                          className="sr-only"
                          defaultChecked
                        />
                        <label
                          htmlFor="twilio"
                          className="block h-6 rounded-full cursor-pointer bg-gray-300 before:absolute before:h-4 before:w-4 before:left-1 before:top-1 before:rounded-full before:bg-white before:transition-all checked:before:left-5 peer-checked:bg-[#009639]"
                        ></label>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <button className="bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F] flex items-center">
                    <Save size={16} className="mr-2" />
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
