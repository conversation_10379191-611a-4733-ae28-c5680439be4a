"use client";

import React, { useState } from "react";
import {
  Search,
  HelpCircle,
  Book,
  FileText,
  MessageSquare,
  Video,
  ExternalLink,
  ChevronRight,
  ChevronDown,
  Mail,
  Phone,
} from "lucide-react";

// Mock help data
const faqData = [
  {
    id: 1,
    question: "How do I add a new user to the system?",
    answer:
      "To add a new user, navigate to the User Management screen and click on the 'Add New User' button. Fill in the required information and click 'Save'.",
  },
  {
    id: 2,
    question: "How do I manage group compliance?",
    answer:
      "You can manage group compliance from the Group Oversight screen. Each group has a compliance status indicator. Click on a group to view detailed compliance information and take necessary actions.",
  },
  {
    id: 3,
    question: "How do I create a new notification?",
    answer:
      "Navigate to the Content Management screen, select the Notifications tab, and click on 'Create Notification'. Fill in the notification details, select the target audience, and set the publication date.",
  },
  {
    id: 4,
    question: "How do I add a new partner?",
    answer:
      "Go to the Partner Management screen and click on 'Add New Partner'. Complete the partner information form including contact details, service type, and contract information.",
  },
  {
    id: 5,
    question: "How do I view system analytics?",
    answer:
      "The Analytics Dashboard provides comprehensive system analytics. You can view user growth, group activities, financial metrics, and more. Use the filters to customize the data view.",
  },
];

const resourcesData = [
  {
    id: 1,
    title: "Admin User Guide",
    description: "Comprehensive guide for Poolly administrators",
    type: "document",
    icon: <FileText size={20} className="text-[#009639]" />,
  },
  {
    id: 2,
    title: "Video Tutorials",
    description: "Step-by-step video guides for common tasks",
    type: "video",
    icon: <Video size={20} className="text-[#007A2F]" />,
  },
  {
    id: 3,
    title: "API Documentation",
    description: "Technical documentation for developers",
    type: "document",
    icon: <FileText size={20} className="text-[#009639]" />,
  },
  {
    id: 4,
    title: "Best Practices Guide",
    description: "Recommendations for optimal system usage",
    type: "document",
    icon: <Book size={20} className="text-[#007A2F]" />,
  },
];

export default function AdminHelp() {
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  // Filter FAQs based on search query
  const filteredFaqs = faqData.filter(
    (faq) =>
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Toggle FAQ expansion
  const toggleFaq = (id: number) => {
    if (expandedFaq === id) {
      setExpandedFaq(null);
    } else {
      setExpandedFaq(id);
    }
  };

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Help Center</h1>
        <p className="text-gray-500">
          Find answers and resources to help you use the admin system
        </p>
      </div>

      {/* Search */}
      <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-lg font-semibold text-center mb-4">
            How can we help you today?
          </h2>
          <div className="relative">
            <Search
              size={20}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
            <input
              type="text"
              placeholder="Search for help..."
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Quick Links */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <HelpCircle size={20} className="text-[#009639] mr-2" />
            Quick Links
          </h2>

          <ul className="space-y-3">
            <li>
              <a
                href="#"
                className="flex items-center text-sm text-gray-700 hover:text-[#009639]"
              >
                <ChevronRight size={16} className="mr-2" />
                User Management Guide
              </a>
            </li>
            <li>
              <a
                href="#"
                className="flex items-center text-sm text-gray-700 hover:text-[#009639]"
              >
                <ChevronRight size={16} className="mr-2" />
                Group Oversight Procedures
              </a>
            </li>
            <li>
              <a
                href="#"
                className="flex items-center text-sm text-gray-700 hover:text-[#009639]"
              >
                <ChevronRight size={16} className="mr-2" />
                Content Management Tutorial
              </a>
            </li>
            <li>
              <a
                href="#"
                className="flex items-center text-sm text-gray-700 hover:text-[#009639]"
              >
                <ChevronRight size={16} className="mr-2" />
                Partner Management Best Practices
              </a>
            </li>
            <li>
              <a
                href="#"
                className="flex items-center text-sm text-gray-700 hover:text-[#009639]"
              >
                <ChevronRight size={16} className="mr-2" />
                Analytics Dashboard Overview
              </a>
            </li>
          </ul>
        </div>

        {/* Contact Support */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <MessageSquare size={20} className="text-[#009639] mr-2" />
            Contact Support
          </h2>

          <p className="text-sm text-gray-600 mb-4">
            Need personalized help? Our support team is ready to assist you.
          </p>

          <div className="space-y-3">
            <div className="flex items-center text-sm text-gray-700">
              <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <MessageSquare size={16} className="text-[#009639]" />
              </div>
              <div>
                <p className="font-medium">Live Chat</p>
                <p className="text-xs text-gray-500">Available 24/7</p>
              </div>
            </div>

            <div className="flex items-center text-sm text-gray-700">
              <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <Mail size={16} className="text-[#009639]" />
              </div>
              <div>
                <p className="font-medium">Email Support</p>
                <p className="text-xs text-gray-500"><EMAIL></p>
              </div>
            </div>

            <div className="flex items-center text-sm text-gray-700">
              <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                <Phone size={16} className="text-[#009639]" />
              </div>
              <div>
                <p className="font-medium">Phone Support</p>
                <p className="text-xs text-gray-500">+27 123 456 789</p>
              </div>
            </div>
          </div>

          <button className="w-full mt-4 bg-[#009639] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#007A2F]">
            Contact Support
          </button>
        </div>

        {/* Resources */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <Book size={20} className="text-[#009639] mr-2" />
            Resources
          </h2>

          <div className="space-y-3">
            {resourcesData.map((resource) => (
              <a
                key={resource.id}
                href="#"
                className="flex items-center p-3 border border-gray-200 rounded-md hover:border-[#009639] transition-colors"
              >
                <div className="mr-3">{resource.icon}</div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-800">
                    {resource.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {resource.description}
                  </p>
                </div>
                <ExternalLink size={16} className="text-gray-400" />
              </a>
            ))}
          </div>
        </div>
      </div>

      {/* FAQs */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center">
          <HelpCircle size={20} className="text-[#009639] mr-2" />
          Frequently Asked Questions
        </h2>

        <div className="space-y-3">
          {filteredFaqs.length > 0 ? (
            filteredFaqs.map((faq) => (
              <div
                key={faq.id}
                className="border border-gray-200 rounded-md overflow-hidden"
              >
                <button
                  className="w-full flex items-center justify-between p-4 text-left focus:outline-none"
                  onClick={() => toggleFaq(faq.id)}
                >
                  <span className="text-sm font-medium text-gray-800">
                    {faq.question}
                  </span>
                  {expandedFaq === faq.id ? (
                    <ChevronDown size={18} className="text-gray-500" />
                  ) : (
                    <ChevronRight size={18} className="text-gray-500" />
                  )}
                </button>

                {expandedFaq === faq.id && (
                  <div className="p-4 bg-gray-50 border-t border-gray-200">
                    <p className="text-sm text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="text-center py-6">
              <HelpCircle size={40} className="mx-auto text-gray-300 mb-2" />
              <p className="text-gray-500">
                No FAQs found matching your search.
              </p>
              <p className="text-sm text-gray-400">
                Try a different search term or browse all FAQs.
              </p>
            </div>
          )}
        </div>

        {filteredFaqs.length > 0 && filteredFaqs.length < faqData.length && (
          <div className="mt-4 text-center">
            <button
              className="text-[#009639] text-sm font-medium hover:underline"
              onClick={() => setSearchQuery("")}
            >
              View all FAQs
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
