"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function IncomeTrackingPage() {
  // Mock data - replace with actual API call
  const transactions = [
    {
      id: 1,
      group: "Family Car Share",
      vehicle: "Toyota Fortuner",
      type: "Income",
      amount: 2500,
      date: "2024-01-15",
      description: "Rental Income",
    },
    {
      id: 2,
      group: "Business Fleet",
      vehicle: "VW Polo",
      type: "Expense",
      amount: 1200,
      date: "2024-01-16",
      description: "Maintenance Cost",
    },
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Income Tracking</h1>
        <div className="flex gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search transactions..."
              className="pl-10 w-[300px]"
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-[#009639]" />
              <span className="text-2xl font-bold">R 45,000</span>
              <span className="text-sm text-green-600 flex items-center">
                <ArrowUpRight className="h-4 w-4" />
                12%
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Total Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold">R 15,000</span>
              <span className="text-sm text-red-600 flex items-center">
                <ArrowDownRight className="h-4 w-4" />
                8%
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Net Income</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-[#009639]">
                R 30,000
              </span>
              <span className="text-sm text-green-600 flex items-center">
                <ArrowUpRight className="h-4 w-4" />
                15%
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Group</TableHead>
                <TableHead>Vehicle</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>
                    <Link
                      href={`/admin/groups/${transaction.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {transaction.group}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/vehicles/${transaction.id}`}
                      className="text-[#009639] hover:text-[#007A2F]"
                    >
                      {transaction.vehicle}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        transaction.type === "Income"
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {transaction.type}
                    </span>
                  </TableCell>
                  <TableCell
                    className={
                      transaction.type === "Income"
                        ? "text-green-600"
                        : "text-red-600"
                    }
                  >
                    R {transaction.amount}
                  </TableCell>
                  <TableCell>{transaction.date}</TableCell>
                  <TableCell>{transaction.description}</TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/transactions/${transaction.id}`}
                      className="text-[#009639] hover:text-[#007A2F] font-medium"
                    >
                      View Details
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
