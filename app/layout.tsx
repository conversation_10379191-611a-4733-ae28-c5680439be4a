import outputs from "@/amplify_outputs.json";
import { Amplify } from "aws-amplify";
import { cognitoUserPoolsTokenProvider } from "aws-amplify/auth/cognito";
import { CookieStorage } from "aws-amplify/utils";
import type { Metadata, Viewport } from "next";
import { Poppins } from "next/font/google";
import type React from "react";
import "./globals.css";

Amplify.configure(outputs);

cognitoUserPoolsTokenProvider.setKeyValueStorage(new CookieStorage());

export const metadata: Metadata = {
  title: "Poolly",
  description: "Making it possible for everyone",
  generator: "v0.dev",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  viewportFit: "cover",
};

const poppins = Poppins({
  weight: ["100", "200", "300", "400", "500", "700"],
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${poppins.className} bg-white`}>{children}</body>
    </html>
  );
}
