"use client";

import useS<PERSON> from "swr";
import { getIndividualData, getStaticFormData, getContactPoints } from "@/app/(main)/home/<USER>";

// Global profile data type
export interface GlobalProfileData {
  individual: {
    id: number;
    party_id: number;
    first_name: string;
    last_name: string;
    birth_date: string;
    middle_name: string | null;
    created_at: string;
    username: string;
    email: string;
  } | null;
  profilePic: string | null;
  contactPoints: any[];
  contactPointTypes: any[];
  addressTypes: any[];
  loaded: boolean;
}

// Single global profile hook - all components use this
export function useGlobalProfile(partyId: number) {
  const { data, error, isLoading, mutate } = useSWR(
    partyId ? 'current-profile' : null, // Single global cache key
    async () => {
      console.log("🌍 Global Profile: Fetching data for partyId:", partyId);

      try {
        // Load individual and static data in parallel
        const [individual, staticData] = await Promise.all([
          getIndividualData(partyId),
          getStaticFormData(),
        ]);

        if (!individual) {
          throw new Error("Individual not found");
        }

        // Load contact points after we have the individual
        const contactPoints = await getContactPoints(individual.party_id);
        const { contactPointTypes, addressTypes } = staticData;

        // Find the email contact point to get the real email
        const emailTypeId = contactPointTypes.find((ct: any) => ct.name === "email")?.id;
        const emailContact = contactPoints.find(
          (cp: any) => cp.contact_point_type_id === emailTypeId
        );

        // Update individual with real email if found
        const individualWithEmail = {
          ...individual,
          email: emailContact?.value || individual.email || "",
        };

        const profileData: GlobalProfileData = {
          individual: individualWithEmail,
          profilePic: null, // Will be implemented later
          contactPoints,
          contactPointTypes,
          addressTypes,
          loaded: true,
        };

        console.log("✅ Global Profile: Data loaded successfully");
        return profileData;
      } catch (error) {
        console.error("❌ Global Profile: Failed to load data:", error);
        throw error;
      }
    },
    {
      revalidateOnMount: true,
      dedupingInterval: 60000, // 1 minute cache
      onSuccess: (data) => {
        console.log("🎉 Global Profile: Cache updated with fresh data");
      },
    }
  );

  // Optimistic update function
  const updateProfile = async (newData: Partial<GlobalProfileData['individual']>) => {
    if (!data?.individual) return;

    console.log("⚡ Global Profile: Applying optimistic update");
    
    // Create optimistic data
    const optimisticData: GlobalProfileData = {
      ...data,
      individual: {
        ...data.individual,
        ...newData,
      },
    };

    // Apply optimistic update immediately
    await mutate(optimisticData, false);
    console.log("✅ Global Profile: Optimistic update applied");
  };

  // Refresh function
  const refreshProfile = () => {
    console.log("🔄 Global Profile: Refreshing data");
    return mutate();
  };

  return {
    profile: data,
    isLoading,
    error,
    updateProfile,
    refreshProfile,
    mutate, // Raw mutate for advanced use cases
  };
}

// Helper hook for components that only need profile data
export function useProfile(partyId: number) {
  const { profile, isLoading, error } = useGlobalProfile(partyId);
  return {
    individual: profile?.individual || null,
    profilePic: profile?.profilePic || null,
    contactPoints: profile?.contactPoints || [],
    contactPointTypes: profile?.contactPointTypes || [],
    addressTypes: profile?.addressTypes || [],
    isLoading,
    error,
  };
}
