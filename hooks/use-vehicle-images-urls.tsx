import { useEffect, useState } from "react";
import { getUrl } from "aws-amplify/storage";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";

export const useVehicleMediaUrls = (
  vehicles: VehicleReadWithModelAndParty[]
) => {
  const [mediaUrls, setMediaUrls] = useState<{ [vehicleId: string]: string[] }>(
    {}
  );

  useEffect(() => {
    const loadMediaUrls = async () => {
      const urlMap: { [vehicleId: string]: string[] } = {};

      await Promise.all(
        vehicles.map(async (vehicle) => {
          const mediaPaths = vehicle.media?.map((m) => m.media_path) || [];
          const urls = await Promise.all(
            mediaPaths.map(async (path) => {
              const result = await getUrl({ path });
              return result.url.toString();
            })
          );
          urlMap[vehicle.id] = urls;
        })
      );

      setMediaUrls(urlMap);
    };

    if (vehicles.length > 0) {
      loadMediaUrls();
    }
  }, [vehicles]);

  return mediaUrls;
};
