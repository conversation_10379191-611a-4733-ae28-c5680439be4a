import { useEffect, useState } from "react";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import type { VehicleMaintenanceRead } from "@/types/maintanance";

interface VehicleMaintenanceReadWithVehicleName
  extends VehicleMaintenanceRead {
  vehicleName: string;
}

export function useVehicleMaintenance(
  vehicles: VehicleReadWithModelAndParty[]
) {
  const [maintenanceData, setMaintenanceData] = useState<
    VehicleMaintenanceReadWithVehicleName[]
  >([]);

  useEffect(() => {
    const allMaintenance: VehicleMaintenanceReadWithVehicleName[] = [];

    vehicles.forEach((vehicle) => {
      const vehicleName = vehicle.model?.model || "Unknown vehicle";
      vehicle.maintenance_items?.forEach((item) => {
        allMaintenance.push({
          ...item,
          vehicleName,
        });
      });
    });

    setMaintenanceData(allMaintenance);
  }, [vehicles]);

  return maintenanceData;
}
