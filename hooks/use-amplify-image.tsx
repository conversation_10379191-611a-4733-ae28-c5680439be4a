import { useState, useEffect } from "react";
import { getUrl } from "aws-amplify/storage";

interface UseAmplifyImageOptions {
  validateObjectExistence?: boolean;
  expiresIn?: number;
}

export function useAmplifyImage(
  mediaPath?: string,
  fallbackSrc: string = "/placeholder.svg?height=120&width=200",
  options: UseAmplifyImageOptions = {}
) {
  const [imageUrl, setImageUrl] = useState<string>(fallbackSrc);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    validateObjectExistence = true,
    expiresIn = 900, // 15 minutes
  } = options;

  useEffect(() => {
    const loadImage = async () => {
      if (!mediaPath) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        const result = await getUrl({ 
          path: mediaPath,
          options: {
            validateObjectExistence,
            expiresIn,
          }
        });
        
        setImageUrl(result.url.toString());
      } catch (err) {
        console.error("Error loading image from Amplify Storage:", err);
        setError("Failed to load image");
        setImageUrl(fallbackSrc);
      } finally {
        setIsLoading(false);
      }
    };

    loadImage();
  }, [mediaPath, fallbackSrc, validateObjectExistence, expiresIn]);

  return {
    imageUrl,
    isLoading,
    error,
    retry: () => {
      if (mediaPath) {
        setIsLoading(true);
        setError(null);
      }
    }
  };
} 