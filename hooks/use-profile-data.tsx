"use client";

import use<PERSON><PERSON> from "swr";
import {
  getIndividualData,
  getProfileImageData,
  getStaticFormData,
  getContactPoints,
} from "@/app/(main)/home/<USER>";
import { IndividualRead } from "@/types/individuals";

// Type definition for the profile data
export interface ProfileData {
  individual: IndividualRead | null;
  profilePic: string | null;
  contactPoints: any[];
  contactPointTypes: any[];
  addressTypes: any[];
  loaded: boolean;
}

/**
 * Custom hook for fetching and caching profile data
 * This provides a consistent data source for all components that need profile data
 */
export function useProfileData(partyId: number) {
  // Use consistent key structure for SWR cache
  const { data, error, isLoading, mutate } = useSWR(
    partyId ? [`profile-data`, partyId] : null,
    async () => {
      console.log("SWR: Fetching profile data for partyId:", partyId);

      try {
        // Load individual and profile pic in parallel
        const [individual, profilePic] = await Promise.all([
          getIndividualData(partyId),
          getProfileImageData(partyId),
        ]);

        if (!individual) {
          throw new Error("Individual not found");
        }

        // Load additional data after we have the individual
        const [staticData, contactPoints] = await Promise.all([
          getStaticFormData(),
          getContactPoints(individual.party_id),
        ]);

        const { contactPointTypes, addressTypes } = staticData;

        // Return structured data
        return {
          individual,
          profilePic,
          contactPoints,
          contactPointTypes,
          addressTypes,
          loaded: true,
        };
      } catch (error) {
        console.error("Failed to load profile data:", error);
        throw error;
      }
    },
    {
      // These options will be merged with the global config
      revalidateOnMount: true,
      dedupingInterval: 60000, // 1 minute cache for profile data
      onSuccess: (data) => {
        console.log("🎉 SWR: Profile data loaded successfully");
        console.log("📊 New data:", data);
        console.log(
          "👤 Individual name:",
          data?.individual?.first_name,
          data?.individual?.last_name
        );
      },
    }
  );

  // Helper function to invalidate the cache
  const refreshProfileData = () => {
    console.log("🔄 useProfileData: refreshProfileData() called");
    console.log("🔑 Cache key being invalidated:", [`profile-data`, partyId]);
    console.log("📊 Current data before refresh:", data);
    const result = mutate();
    console.log("✅ useProfileData: mutate() called, returning promise");
    return result;
  };

  // Return structured data with loading and error states
  return {
    data: data as ProfileData,
    isLoading,
    error,
    mutate: refreshProfileData,
  };
}
