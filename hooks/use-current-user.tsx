"use client";

import { useState, useEffect } from "react";
import { getCurrentUser, fetchUserAttributes } from "aws-amplify/auth";

interface CurrentUser {
  userId: string;
  partyId?: number;
  email?: string;
  firstName?: string;
  lastName?: string;
  isLoading: boolean;
  error?: string;
}

export function useCurrentUser(): CurrentUser {
  const [user, setUser] = useState<CurrentUser>({
    userId: "",
    isLoading: true,
  });

  useEffect(() => {
    const loadUser = async () => {
      try {
        setUser(prev => ({ ...prev, isLoading: true, error: undefined }));
        
        const currentUser = await getCurrentUser();
        const attributes = await fetchUserAttributes();
        
        const partyId = attributes["custom:db_id"] 
          ? parseInt(attributes["custom:db_id"]) 
          : undefined;

        setUser({
          userId: currentUser.userId,
          partyId: isNaN(partyId!) ? undefined : partyId,
          email: attributes.email,
          firstName: attributes.given_name,
          lastName: attributes.family_name,
          isLoading: false,
        });
      } catch (error) {
        console.error("Error loading user:", error);
        setUser({
          userId: "",
          isLoading: false,
          error: error instanceof Error ? error.message : "Failed to load user",
        });
      }
    };

    loadUser();
  }, []);

  return user;
} 