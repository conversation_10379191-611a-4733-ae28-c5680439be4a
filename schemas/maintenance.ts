import { z } from "zod";
import { VehicleServiceStatus } from "@/types/maintanance";

export const MaintenanceSchema = z.object({
  vehicle_id: z.number({
    required_error: "Vehicle ID is required",
    invalid_type_error: "Vehicle ID must be a number",
  }),
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name must be a string",
    })
    .min(1, "Name cannot be empty"),
  description: z.string().optional(),
  due_date: z
    .string({
      required_error: "Due date is required",
      invalid_type_error: "Due date must be a string",
    })
    .refine((date) => !isNaN(Date.parse(date)), {
      message: "Invalid date format for due_date",
    }),
  due_odometer: z.number({
    required_error: "Due odometer is required",
    invalid_type_error: "Due odometer must be a number",
  }),
  status: z.nativeEnum(VehicleServiceStatus, {
    required_error: "Status is required",
    invalid_type_error: "Invalid status value",
  }),
  expected_cost: z.number({
    required_error: "Expected cost is required",
    invalid_type_error: "Expected cost must be a number",
  }),
  completed_date: z
    .string()
    .optional()
    .refine((date) => !date || !isNaN(Date.parse(date)), {
      message: "Invalid date format for completed_date",
    }),
  completed_odometer: z.number().optional(),
  actual_cost: z.number().optional(),
  technician_notes: z.string().optional(),
  service_provider: z.string().optional(),
  is_scheduled: z.coerce.boolean().optional(),
});

export const MaintenanceUpdateSchema = MaintenanceSchema.partial().extend({
  maintenance_id: z.number({
    required_error: "Maintenance ID is required",
    invalid_type_error: "Maintenance ID must be a number",
  }),
});

export type MaintenanceSchemaType = z.infer<typeof MaintenanceSchema>;
export type MaintenanceUpdateSchemaType = z.infer<
  typeof MaintenanceUpdateSchema
>;
