import { z } from "zod";

export const possessionSchema = z.object({
  vehicleId: z
    .string({
      required_error: "Vehicle ID is required",
      invalid_type_error: "Vehicle ID must be a string",
    })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val), {
      message: "Vehicle ID must be a number",
    }),

  scratches: z.enum(["none", "minor", "major"], {
    errorMap: () => ({
      message: "Scratches must be 'none', 'minor' or 'major'",
    }),
  }),
  dents: z.enum(["none", "minor", "major"], {
    errorMap: () => ({ message: "Dents must be 'none', 'minor' or 'major'" }),
  }),
  tires: z.string().min(1, "Tire condition is required"),
  lights: z.string().min(1, "Lights condition is required"),

  // Interior
  cleanliness: z.string().min(1, "Cleanliness information is required"),
  seats: z.string().min(1, "Seat condition is required"),
  dashboard_controls: z
    .string()
    .min(1, "Dashboard controls status is required"),
  odors: z.string().min(1, "Odor status is required"),

  // Other
  odometer: z
    .string({
      required_error: "Odometer is required",
      invalid_type_error: "Odometer must be a string",
    })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val), {
      message: "Odometer must be a number",
    }),

  known_issues: z.string().optional(),

  // Photos
  front_view: z.string().min(1, "Front view is required"),
  rear_view: z.string().min(1, "Rear view is required"),
  left_view: z.string().min(1, "Left view is required"),
  right_view: z.string().min(1, "Right view is required"),
  dashboard: z.string().min(1, "Dashboard photo is required"),
  seats_view: z.string().min(1, "Seats photo is required"),
  additional: z
    .array(z.string().min(1, "Additional photo cannot be empty"))
    .optional(),

  // Parties
  from_party_id: z
    .string({
      required_error: "From party ID is required",
      invalid_type_error: "From party ID must be a string",
    })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val), {
      message: "From party ID must be a number",
    }),

  to_party_id: z
    .string({
      required_error: "To party ID is required",
      invalid_type_error: "To party ID must be a string",
    })
    .transform((val) => Number(val))
    .refine((val) => !isNaN(val), {
      message: "To party ID must be a number",
    }),
});
