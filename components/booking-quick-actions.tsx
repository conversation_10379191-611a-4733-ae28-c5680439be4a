"use client";

import { useRouter } from "next/navigation";
import { Calendar, Car } from "lucide-react";

interface BookingQuickActionsProps {
  vehicleId: number;
  vehicleStatus: string;
  showBookButton?: boolean;
  showViewStatusButton?: boolean;
  className?: string;
}

export default function BookingQuickActions({
  vehicleId,
  vehicleStatus,
  showBookButton = true,
  showViewStatusButton = true,
  className = "",
}: BookingQuickActionsProps) {
  const router = useRouter();

  const isAvailable = vehicleStatus === "available";

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {showBookButton && isAvailable && (
        <button
          className="flex items-center px-3 py-1.5 bg-[#009639] text-white text-xs font-medium rounded-full shadow-sm hover:bg-[#007A2F] transition-colors"
          onClick={() => router.push(`/booking-calendar/${vehicleId}`)}
        >
          <Calendar size={12} className="mr-1" />
          Book
        </button>
      )}
      
      {showViewStatusButton && (
        <button
          className="flex items-center px-3 py-1.5 border border-[#009639] text-[#009639] text-xs font-medium rounded-full hover:bg-[#009639] hover:text-white transition-colors"
          onClick={() => router.push(`/vehicle-status/${vehicleId}`)}
        >
          <Car size={12} className="mr-1" />
          Status
        </button>
      )}
    </div>
  );
} 