"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Car, Users, Plus, Calendar, Wrench, FileText } from "lucide-react";
import { getVehiclesInGroup } from "@/drizzle-actions/vehicle-groups";
import type { VehicleRead } from "@/types/vehicles";
import Link from "next/link";

interface VehicleGroupListProps {
  companyId: number;
  companyName: string;
  showAddButton?: boolean;
}

export default function VehicleGroupList({
  companyId,
  companyName,
  showAddButton = true
}: VehicleGroupListProps) {
  const [vehicles, setVehicles] = useState<VehicleRead[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadVehicles = async () => {
      try {
        setLoading(true);
        const vehiclesData = await getVehiclesInGroup(companyId);
        setVehicles(vehiclesData);
      } catch (error) {
        console.error("Error loading group vehicles:", error);
        setError("Failed to load group vehicles");
      } finally {
        setLoading(false);
      }
    };

    loadVehicles();
  }, [companyId]);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading vehicles...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert className="border-red-500">
        <AlertDescription className="text-red-700">
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Group Vehicles - {companyName}
            </CardTitle>
            {showAddButton && (
              <Link href={`/add-vehicle-to-group/${companyId}`}>
                <Button className="bg-[#009639] hover:bg-[#007A2F]">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Vehicle
                </Button>
              </Link>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {vehicles.length === 0 ? (
            <div className="text-center py-8">
              <Car className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">
                No vehicles in this group yet
              </h3>
              <p className="text-gray-500 mb-4">
                Add your first vehicle to start co-ownership
              </p>
              {showAddButton && (
                <Link href={`/add-vehicle-to-group/${companyId}`}>
                  <Button className="bg-[#009639] hover:bg-[#007A2F]">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Vehicle
                  </Button>
                </Link>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {vehicles.map((vehicle) => (
                <VehicleCard key={vehicle.id} vehicle={vehicle} />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

interface VehicleCardProps {
  vehicle: VehicleRead;
}

function VehicleCard({ vehicle }: VehicleCardProps) {
  const vehicleName = `${vehicle.model?.year_model || ''} ${vehicle.model?.make?.name || ''} ${vehicle.model?.model || ''}`.trim();
  const hasMaintenanceItems = vehicle.maintenance_items && vehicle.maintenance_items.length > 0;
  const hasBookings = vehicle.bookings && vehicle.bookings.length > 0;
  const hasDocuments = vehicle.vehicle_documents && vehicle.vehicle_documents.length > 0;

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Vehicle Image */}
          <div className="h-32 bg-gray-100 rounded-lg flex items-center justify-center">
            {vehicle.media && vehicle.media.length > 0 ? (
              <img
                src={vehicle.media[0].media_path}
                alt={vehicleName}
                className="h-full w-full object-cover rounded-lg"
              />
            ) : (
              <Car className="h-12 w-12 text-gray-400" />
            )}
          </div>

          {/* Vehicle Info */}
          <div>
            <h3 className="font-semibold text-gray-800 truncate">
              {vehicleName || 'Unknown Vehicle'}
            </h3>
            <p className="text-sm text-gray-600">
              VIN: {vehicle.vin_number}
            </p>
            {vehicle.vehicle_registration && (
              <p className="text-sm text-gray-600">
                Reg: {vehicle.vehicle_registration}
              </p>
            )}
          </div>

          {/* Status Indicators */}
          <div className="flex justify-between text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{hasBookings ? `${vehicle.bookings.length} bookings` : 'No bookings'}</span>
            </div>
            <div className="flex items-center gap-1">
              <Wrench className="h-3 w-3" />
              <span>{hasMaintenanceItems ? `${vehicle.maintenance_items.length} tasks` : 'No tasks'}</span>
            </div>
          </div>

          {/* Vehicle Documents */}
          {hasDocuments && (
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <FileText className="h-3 w-3" />
              <span>{vehicle.vehicle_documents.length} documents</span>
            </div>
          )}

          {/* Action Button */}
          <Link href={`/vehicle-details/${vehicle.id}`}>
            <Button variant="outline" size="sm" className="w-full">
              View Details
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
} 