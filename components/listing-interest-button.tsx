"use client";

import { useState } from "react";
import { Heart, Users } from "lucide-react";
import { expressInterestInListing, removeInterestFromListing } from "@/actions/listing-interest";

interface ListingInterestButtonProps {
  listingId: number;
  listingAuthorPartyId: number;
  currentUserPartyId?: number;
  initialInterestCount?: number;
  initialUserHasInterest?: boolean;
  variant?: "button" | "compact";
  className?: string;
}

export default function ListingInterestButton({
  listingId,
  listingAuthorPartyId,
  currentUserPartyId,
  initialInterestCount = 0,
  initialUserHasInterest = false,
  variant = "button",
  className = "",
}: ListingInterestButtonProps) {
  const [isExpressing, setIsExpressing] = useState(false);
  const [interestCount, setInterestCount] = useState(initialInterestCount);
  const [userHasInterest, setUserHasInterest] = useState(initialUserHasInterest);

  const isAuthor = currentUserPartyId === listingAuthorPartyId;
  const isLoggedIn = currentUserPartyId !== undefined;

  const handleInterestToggle = async () => {
    if (!isLoggedIn || isAuthor || isExpressing) return;

    setIsExpressing(true);
    try {
      if (userHasInterest) {
        await removeInterestFromListing(listingId);
        setUserHasInterest(false);
        setInterestCount(prev => Math.max(0, prev - 1));
      } else {
        await expressInterestInListing(listingId);
        setUserHasInterest(true);
        setInterestCount(prev => prev + 1);
      }
    } catch (error) {
      console.error("Error toggling interest:", error);
      // Could show a toast notification here
    } finally {
      setIsExpressing(false);
    }
  };

  // For listing authors - show interest count
  if (isAuthor) {
    if (variant === "compact") {
      return (
        <div className={`flex items-center text-sm text-[#797879] ${className}`}>
          <Users size={14} className="mr-1" />
          <span>{interestCount} interested</span>
        </div>
      );
    }

    return (
      <div className={`flex items-center justify-center p-2 bg-[#f0f9f0] rounded-full ${className}`}>
        <Users size={16} className="text-[#009639] mr-2" />
        <span className="text-sm text-[#009639] font-medium">
          {interestCount} {interestCount === 1 ? "person" : "people"} interested
        </span>
      </div>
    );
  }

  // For viewers - show interest button
  if (!isLoggedIn) {
    return (
      <button
        className={`text-sm text-[#797879] ${className}`}
        disabled
      >
        Login to express interest
      </button>
    );
  }

  if (variant === "compact") {
    return (
      <button
        onClick={handleInterestToggle}
        disabled={isExpressing}
        className={`flex items-center text-sm ${
          userHasInterest 
            ? "text-red-500" 
            : "text-[#797879] hover:text-[#009639]"
        } transition-colors ${className}`}
      >
        <Heart 
          size={14} 
          className={`mr-1 ${userHasInterest ? "fill-current" : ""}`} 
        />
        <span>
          {isExpressing 
            ? "..." 
            : userHasInterest 
              ? "Interested" 
              : "Express Interest"
          }
        </span>
      </button>
    );
  }

  return (
    <button
      onClick={handleInterestToggle}
      disabled={isExpressing}
      className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
        userHasInterest
          ? "bg-red-50 text-red-600 border border-red-200 hover:bg-red-100"
          : "bg-gradient-to-r from-[#009639] to-[#007A2F] text-white hover:shadow-lg"
      } disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      {isExpressing ? (
        <div className="flex items-center">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
          Processing...
        </div>
      ) : (
        <div className="flex items-center">
          <Heart 
            size={16} 
            className={`mr-2 ${userHasInterest ? "fill-current" : ""}`} 
          />
          {userHasInterest ? "Remove Interest" : "Express Interest"}
        </div>
      )}
    </button>
  );
} 