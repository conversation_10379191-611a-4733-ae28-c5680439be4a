"use client";

import outputs from "@/amplify_outputs.json";
import { Amplify } from "aws-amplify";
import { cognitoUserPoolsTokenProvider } from "aws-amplify/auth/cognito";
import { CookieStorage } from "aws-amplify/utils";
import { type ReactNode } from "react";

// Configure Amplify
Amplify.configure(outputs);

// Configure storage for auth tokens using cookies
cognitoUserPoolsTokenProvider.setKeyValueStorage(
  new CookieStorage({
    domain: ".poollysa.com",
    sameSite: "strict",
    secure: process.env.NODE_ENV === 'production', // Only secure in production
  })
);

interface AmplifyConfigProps {
  children: ReactNode;
}

export function AmplifyConfig({ children }: AmplifyConfigProps) {
  return <>{children}</>;
} 