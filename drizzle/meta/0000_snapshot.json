{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.cities": {"name": "cities", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "province": {"name": "province", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {"ix_cities_id": {"name": "ix_cities_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"uq_city_name_province": {"columns": ["name", "province"], "nullsNotDistinct": false, "name": "uq_city_name_province"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.account_type": {"name": "account_type", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"account_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "account_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.alembic_version": {"name": "alembic_version", "schema": "", "columns": {"version_num": {"name": "version_num", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": true, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.company_ownership": {"name": "company_ownership", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "fraction": {"name": "fraction", "type": "numeric", "primaryKey": false, "notNull": true}, "effective_from": {"name": "effective_from", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "effective_to": {"name": "effective_to", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"company_ownership_company_id_fkey": {"name": "company_ownership_company_id_fkey", "tableFrom": "company_ownership", "tableTo": "company", "schemaTo": "public", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "company_ownership_party_id_fkey": {"name": "company_ownership_party_id_fkey", "tableFrom": "company_ownership", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.compliance_set": {"name": "compliance_set", "schema": "", "columns": {"name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.compliance_requirement_type": {"name": "compliance_requirement_type", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "default_validity_in_days": {"name": "default_validity_in_days", "type": "integer", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"compliance_requirement_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "compliance_requirement_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.address_type": {"name": "address_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"address_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "address_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.bookings": {"name": "bookings", "schema": "", "columns": {"vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "reference": {"name": "reference", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "start_datetime": {"name": "start_datetime", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_datetime": {"name": "end_datetime", "type": "timestamp", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "bookingstatus", "typeSchema": "public", "primaryKey": false, "notNull": false}, "total_price": {"name": "total_price", "type": "double precision", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_bookings_id": {"name": "ix_bookings_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bookings_party_id_fkey": {"name": "bookings_party_id_fkey", "tableFrom": "bookings", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "bookings_vehicle_id_fkey": {"name": "bookings_vehicle_id_fkey", "tableFrom": "bookings", "tableTo": "vehicles", "schemaTo": "public", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"bookings_reference_key": {"columns": ["reference"], "nullsNotDistinct": false, "name": "bookings_reference_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.compliance_requirement": {"name": "compliance_requirement", "schema": "", "columns": {"party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "compliance_set_id": {"name": "compliance_set_id", "type": "integer", "primaryKey": false, "notNull": true}, "requirement_type_id": {"name": "requirement_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "reviewed_by_id": {"name": "reviewed_by_id", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "compliancerequirementstatusenum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "reference_type": {"name": "reference_type", "type": "referencetypeenum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "reference": {"name": "reference", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "issue_date": {"name": "issue_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "issuing_authority_id": {"name": "issuing_authority_id", "type": "integer", "primaryKey": false, "notNull": false}, "uploaded_individual_id": {"name": "uploaded_individual_id", "type": "integer", "primaryKey": false, "notNull": false}, "verification_id": {"name": "verification_id", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"compliance_requirement_compliance_set_id_fkey": {"name": "compliance_requirement_compliance_set_id_fkey", "tableFrom": "compliance_requirement", "tableTo": "compliance_set", "schemaTo": "public", "columnsFrom": ["compliance_set_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "compliance_requirement_issuing_authority_id_fkey": {"name": "compliance_requirement_issuing_authority_id_fkey", "tableFrom": "compliance_requirement", "tableTo": "issuing_authority", "schemaTo": "public", "columnsFrom": ["issuing_authority_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "compliance_requirement_party_id_fkey": {"name": "compliance_requirement_party_id_fkey", "tableFrom": "compliance_requirement", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "compliance_requirement_requirement_type_id_fkey": {"name": "compliance_requirement_requirement_type_id_fkey", "tableFrom": "compliance_requirement", "tableTo": "compliance_requirement_type", "schemaTo": "public", "columnsFrom": ["requirement_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "compliance_requirement_reviewed_by_id_fkey": {"name": "compliance_requirement_reviewed_by_id_fkey", "tableFrom": "compliance_requirement", "tableTo": "individual", "schemaTo": "public", "columnsFrom": ["reviewed_by_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "compliance_requirement_uploaded_individual_id_fkey": {"name": "compliance_requirement_uploaded_individual_id_fkey", "tableFrom": "compliance_requirement", "tableTo": "individual", "schemaTo": "public", "columnsFrom": ["uploaded_individual_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "compliance_requirement_verification_id_fkey": {"name": "compliance_requirement_verification_id_fkey", "tableFrom": "compliance_requirement", "tableTo": "verification", "schemaTo": "public", "columnsFrom": ["verification_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.contact_type": {"name": "contact_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"contact_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "contact_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.contract_status": {"name": "contract_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"contract_status_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "contract_status_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.contact_point": {"name": "contact_point", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "contact_point_type_id": {"name": "contact_point_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "address_type_id": {"name": "address_type_id", "type": "integer", "primaryKey": false, "notNull": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "verification_date": {"name": "verification_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "verification_method": {"name": "verification_method", "type": "text", "primaryKey": false, "notNull": false}, "mtadata": {"name": "mtadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"contact_point_address_type_id_fkey": {"name": "contact_point_address_type_id_fkey", "tableFrom": "contact_point", "tableTo": "address_type", "schemaTo": "public", "columnsFrom": ["address_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "contact_point_contact_point_type_id_fkey": {"name": "contact_point_contact_point_type_id_fkey", "tableFrom": "contact_point", "tableTo": "contact_point_type", "schemaTo": "public", "columnsFrom": ["contact_point_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "contact_point_party_id_fkey": {"name": "contact_point_party_id_fkey", "tableFrom": "contact_point", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.contact_point_type": {"name": "contact_point_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "validation_pattern": {"name": "validation_pattern", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"contact_point_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "contact_point_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.contract_type": {"name": "contract_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"contract_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "contract_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.identification_type": {"name": "identification_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "validation_pattern": {"name": "validation_pattern", "type": "text", "primaryKey": false, "notNull": false}, "expiration_required": {"name": "expiration_required", "type": "boolean", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"identification_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "identification_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.dispute_media": {"name": "dispute_media", "schema": "", "columns": {"dispute_id": {"name": "dispute_id", "type": "integer", "primaryKey": false, "notNull": true}, "media_path": {"name": "media_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_dispute_media_id": {"name": "ix_dispute_media_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"dispute_media_dispute_id_fkey": {"name": "dispute_media_dispute_id_fkey", "tableFrom": "dispute_media", "tableTo": "disputes", "schemaTo": "public", "columnsFrom": ["dispute_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.document_type": {"name": "document_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"document_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "document_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.emergency_contacts": {"name": "emergency_contacts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "contact_type": {"name": "contact_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"ix_emergency_contacts_id": {"name": "ix_emergency_contacts_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.individual": {"name": "individual", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "middle_name": {"name": "middle_name", "type": "text", "primaryKey": false, "notNull": false}, "salutation": {"name": "salutation", "type": "text", "primaryKey": false, "notNull": false}, "suffix": {"name": "suffix", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "birth_date": {"name": "birth_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "marital_status": {"name": "marital_status", "type": "text", "primaryKey": false, "notNull": false}, "nationality": {"name": "nationality", "type": "text", "primaryKey": false, "notNull": false}, "preferred_language": {"name": "preferred_language", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"individual_party_id_fkey": {"name": "individual_party_id_fkey", "tableFrom": "individual", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.disputes": {"name": "disputes", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "dispute_type": {"name": "dispute_type", "type": "disputetype", "typeSchema": "public", "primaryKey": false, "notNull": true}, "party_offending": {"name": "party_offending", "type": "integer", "primaryKey": false, "notNull": true}, "party_logging": {"name": "party_logging", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "priority", "typeSchema": "public", "primaryKey": false, "notNull": false}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "dispute_status": {"name": "dispute_status", "type": "disputestatus", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {"ix_disputes_id": {"name": "ix_disputes_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"disputes_party_offending_fkey": {"name": "disputes_party_offending_fkey", "tableFrom": "disputes", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_offending"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "disputes_party_logging_fkey": {"name": "disputes_party_logging_fkey", "tableFrom": "disputes", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_logging"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "disputes_company_id_fkey": {"name": "disputes_company_id_fkey", "tableFrom": "disputes", "tableTo": "company", "schemaTo": "public", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.industry": {"name": "industry", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"industry_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "industry_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.lead": {"name": "lead", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "status_id": {"name": "status_id", "type": "integer", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "integer", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "text", "primaryKey": false, "notNull": false}, "annual_revenue": {"name": "annual_revenue", "type": "integer", "primaryKey": false, "notNull": false}, "number_of_employees": {"name": "number_of_employees", "type": "integer", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_converted": {"name": "is_converted", "type": "boolean", "primaryKey": false, "notNull": true}, "converted_date": {"name": "converted_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "converted_account_id": {"name": "converted_account_id", "type": "integer", "primaryKey": false, "notNull": false}, "converted_contact_id": {"name": "converted_contact_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"lead_party_id_fkey": {"name": "lead_party_id_fkey", "tableFrom": "lead", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "lead_source_id_fkey": {"name": "lead_source_id_fkey", "tableFrom": "lead", "tableTo": "lead_source", "schemaTo": "public", "columnsFrom": ["source_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "lead_status_id_fkey": {"name": "lead_status_id_fkey", "tableFrom": "lead", "tableTo": "lead_status", "schemaTo": "public", "columnsFrom": ["status_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.lead_source": {"name": "lead_source", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"lead_source_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "lead_source_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.lead_status": {"name": "lead_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"lead_status_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "lead_status_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.match_rule_type": {"name": "match_rule_type", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "confidence": {"name": "confidence", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.opportunity_stage": {"name": "opportunity_stage", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "probability": {"name": "probability", "type": "integer", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"opportunity_stage_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "opportunity_stage_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.opportunity_type": {"name": "opportunity_type", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"opportunity_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "opportunity_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.issuing_authority": {"name": "issuing_authority", "schema": "", "columns": {"party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"issuing_authority_party_id_fkey": {"name": "issuing_authority_party_id_fkey", "tableFrom": "issuing_authority", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"issuing_authority_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "issuing_authority_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.party_status": {"name": "party_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"party_status_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "party_status_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.party": {"name": "party", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_type_id": {"name": "party_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "status_id": {"name": "status_id", "type": "integer", "primaryKey": false, "notNull": true}, "external_id": {"name": "external_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"party_party_type_id_fkey": {"name": "party_party_type_id_fkey", "tableFrom": "party", "tableTo": "party_type", "schemaTo": "public", "columnsFrom": ["party_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "party_status_id_fkey": {"name": "party_status_id_fkey", "tableFrom": "party", "tableTo": "party_status", "schemaTo": "public", "columnsFrom": ["status_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"party_external_id_key": {"columns": ["external_id"], "nullsNotDistinct": false, "name": "party_external_id_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.party_identification": {"name": "party_identification", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "identification_type_id": {"name": "identification_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "document_number": {"name": "document_number", "type": "text", "primaryKey": false, "notNull": true}, "issuing_authority": {"name": "issuing_authority", "type": "text", "primaryKey": false, "notNull": false}, "issue_date": {"name": "issue_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "expiry_date": {"name": "expiry_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "verification_date": {"name": "verification_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "verification_method": {"name": "verification_method", "type": "text", "primaryKey": false, "notNull": false}, "document_image_url": {"name": "document_image_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"party_identification_identification_type_id_fkey": {"name": "party_identification_identification_type_id_fkey", "tableFrom": "party_identification", "tableTo": "identification_type", "schemaTo": "public", "columnsFrom": ["identification_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "party_identification_party_id_fkey": {"name": "party_identification_party_id_fkey", "tableFrom": "party_identification", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.product_type": {"name": "product_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"product_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "product_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.record_type": {"name": "record_type", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"record_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "record_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.relationship_type": {"name": "relationship_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"relationship_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "relationship_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vehicle_inspections": {"name": "vehicle_inspections", "schema": "", "columns": {"scratches": {"name": "scratches", "type": "conditionlevel", "typeSchema": "public", "primaryKey": false, "notNull": true}, "dents": {"name": "dents", "type": "conditionlevel", "typeSchema": "public", "primaryKey": false, "notNull": true}, "tires": {"name": "tires", "type": "generalcondition", "typeSchema": "public", "primaryKey": false, "notNull": true}, "lights": {"name": "lights", "type": "lightscondition", "typeSchema": "public", "primaryKey": false, "notNull": true}, "cleanliness": {"name": "cleanliness", "type": "cleanlinesslevel", "typeSchema": "public", "primaryKey": false, "notNull": true}, "seats": {"name": "seats", "type": "generalcondition", "typeSchema": "public", "primaryKey": false, "notNull": true}, "dashboard_controls": {"name": "dashboard_controls", "type": "dashboardcondition", "typeSchema": "public", "primaryKey": false, "notNull": true}, "odors": {"name": "odors", "type": "odorlevel", "typeSchema": "public", "primaryKey": false, "notNull": true}, "odometer": {"name": "odometer", "type": "integer", "primaryKey": false, "notNull": true}, "known_issues": {"name": "known_issues", "type": "text", "primaryKey": false, "notNull": false}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "possession_id": {"name": "possession_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_inspections_id": {"name": "ix_vehicle_inspections_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_inspections_possession_id_fkey": {"name": "vehicle_inspections_possession_id_fkey", "tableFrom": "vehicle_inspections", "tableTo": "vehicle_possessions", "schemaTo": "public", "columnsFrom": ["possession_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "vehicle_inspections_vehicle_id_fkey": {"name": "vehicle_inspections_vehicle_id_fkey", "tableFrom": "vehicle_inspections", "tableTo": "vehicles", "schemaTo": "public", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.social_media_type": {"name": "social_media_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "base_url": {"name": "base_url", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"social_media_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "social_media_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.subscription_status": {"name": "subscription_status", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "display_order": {"name": "display_order", "type": "integer", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"subscription_status_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "subscription_status_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "family_name": {"name": "family_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "given_name": {"name": "given_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "org_id": {"name": "org_id", "type": "<PERSON><PERSON><PERSON>(16)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_key": {"columns": ["username"], "nullsNotDistinct": false, "name": "users_username_key"}, "users_email_key": {"columns": ["email"], "nullsNotDistinct": false, "name": "users_email_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vehicle_maintenance": {"name": "vehicle_maintenance", "schema": "", "columns": {"vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "due_odometer": {"name": "due_odometer", "type": "double precision", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "vehicleservicestatus", "typeSchema": "public", "primaryKey": false, "notNull": true}, "expected_cost": {"name": "expected_cost", "type": "double precision", "primaryKey": false, "notNull": true}, "completed_date": {"name": "completed_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_odometer": {"name": "completed_odometer", "type": "double precision", "primaryKey": false, "notNull": false}, "actual_cost": {"name": "actual_cost", "type": "double precision", "primaryKey": false, "notNull": false}, "technician_notes": {"name": "technician_notes", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "service_provider": {"name": "service_provider", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_scheduled": {"name": "is_scheduled", "type": "boolean", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": false, "notNull": true}}, "indexes": {"ix_vehicle_maintenance_id": {"name": "ix_vehicle_maintenance_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_maintenance_vehicle_id_fkey": {"name": "vehicle_maintenance_vehicle_id_fkey", "tableFrom": "vehicle_maintenance", "tableTo": "vehicles", "schemaTo": "public", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vehicle_make": {"name": "vehicle_make", "schema": "", "columns": {"name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_make_id": {"name": "ix_vehicle_make_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.party_type": {"name": "party_type", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"party_type_name_key": {"columns": ["name"], "nullsNotDistinct": false, "name": "party_type_name_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.social_profile": {"name": "social_profile", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "social_media_type_id": {"name": "social_media_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"social_profile_party_id_fkey": {"name": "social_profile_party_id_fkey", "tableFrom": "social_profile", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "social_profile_social_media_type_id_fkey": {"name": "social_profile_social_media_type_id_fkey", "tableFrom": "social_profile", "tableTo": "social_media_type", "schemaTo": "public", "columnsFrom": ["social_media_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vehicle_model_media": {"name": "vehicle_model_media", "schema": "", "columns": {"vehicle_model_id": {"name": "vehicle_model_id", "type": "integer", "primaryKey": false, "notNull": true}, "media_path": {"name": "media_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_model_media_id": {"name": "ix_vehicle_model_media_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_model_media_vehicle_model_id_fkey": {"name": "vehicle_model_media_vehicle_model_id_fkey", "tableFrom": "vehicle_model_media", "tableTo": "vehicle_model", "schemaTo": "public", "columnsFrom": ["vehicle_model_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vehicle_photos": {"name": "vehicle_photos", "schema": "", "columns": {"inspection_id": {"name": "inspection_id", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "phototype", "typeSchema": "public", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_photos_id": {"name": "ix_vehicle_photos_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_photos_inspection_id_fkey": {"name": "vehicle_photos_inspection_id_fkey", "tableFrom": "vehicle_photos", "tableTo": "vehicle_inspections", "schemaTo": "public", "columnsFrom": ["inspection_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"verifying_party_id": {"name": "verifying_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "verification_outcome": {"name": "verification_outcome", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "cost": {"name": "cost", "type": "double precision", "primaryKey": false, "notNull": false}, "verification_type": {"name": "verification_type", "type": "verificationtypeenum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"verification_verifying_party_id_fkey": {"name": "verification_verifying_party_id_fkey", "tableFrom": "verification", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["verifying_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vehicle_possessions": {"name": "vehicle_possessions", "schema": "", "columns": {"from_party_id": {"name": "from_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "to_party_id": {"name": "to_party_id", "type": "integer", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "handover_expected_datetime": {"name": "handover_expected_datetime", "type": "timestamp", "primaryKey": false, "notNull": false}, "handover_actual_datetime": {"name": "handover_actual_datetime", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "possessionstatus", "typeSchema": "public", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_possessions_id": {"name": "ix_vehicle_possessions_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_possessions_from_party_id_fkey": {"name": "vehicle_possessions_from_party_id_fkey", "tableFrom": "vehicle_possessions", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["from_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_possessions_to_party_id_fkey": {"name": "vehicle_possessions_to_party_id_fkey", "tableFrom": "vehicle_possessions", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["to_party_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "vehicle_possessions_vehicle_id_fkey": {"name": "vehicle_possessions_vehicle_id_fkey", "tableFrom": "vehicle_possessions", "tableTo": "vehicles", "schemaTo": "public", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vehicle_media": {"name": "vehicle_media", "schema": "", "columns": {"vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "media_path": {"name": "media_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_media_id": {"name": "ix_vehicle_media_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_media_vehicle_id_fkey": {"name": "vehicle_media_vehicle_id_fkey", "tableFrom": "vehicle_media", "tableTo": "vehicles", "schemaTo": "public", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vehicle_model": {"name": "vehicle_model", "schema": "", "columns": {"make_id": {"name": "make_id", "type": "integer", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "year_model": {"name": "year_model", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "transmission": {"name": "transmission", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_model_id": {"name": "ix_vehicle_model_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_model_make_id_fkey": {"name": "vehicle_model_make_id_fkey", "tableFrom": "vehicle_model", "tableTo": "vehicle_make", "schemaTo": "public", "columnsFrom": ["make_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vehicles": {"name": "vehicles", "schema": "", "columns": {"party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "model_id": {"name": "model_id", "type": "integer", "primaryKey": false, "notNull": true}, "vin_number": {"name": "vin_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "vehicle_registration": {"name": "vehicle_registration", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "country_of_registration": {"name": "country_of_registration", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "manufacturing_year": {"name": "manufacturing_year", "type": "integer", "primaryKey": false, "notNull": false}, "purchase_date": {"name": "purchase_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicles_id": {"name": "ix_vehicles_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicles_model_id_fkey": {"name": "vehicles_model_id_fkey", "tableFrom": "vehicles", "tableTo": "vehicle_model", "schemaTo": "public", "columnsFrom": ["model_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vehicles_party_id_fkey": {"name": "vehicles_party_id_fkey", "tableFrom": "vehicles", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"vehicles_vin_number_key": {"columns": ["vin_number"], "nullsNotDistinct": false, "name": "vehicles_vin_number_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.company": {"name": "company", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "registration_number": {"name": "registration_number", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "registration_country": {"name": "registration_country", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "registration_date": {"name": "registration_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "alias": {"name": "alias", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "city_id": {"name": "city_id", "type": "integer", "primaryKey": false, "notNull": false}, "purpose": {"name": "purpose", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"company_city_id_fkey": {"name": "company_city_id_fkey", "tableFrom": "company", "tableTo": "cities", "schemaTo": "public", "columnsFrom": ["city_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "company_party_id_fkey": {"name": "company_party_id_fkey", "tableFrom": "company", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.dispute_comments": {"name": "dispute_comments", "schema": "", "columns": {"comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true}, "dispute_id": {"name": "dispute_id", "type": "integer", "primaryKey": false, "notNull": true}, "reply_to_comment_id": {"name": "reply_to_comment_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_dispute_comments_id": {"name": "ix_dispute_comments_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"dispute_comments_dispute_id_fkey": {"name": "dispute_comments_dispute_id_fkey", "tableFrom": "dispute_comments", "tableTo": "disputes", "schemaTo": "public", "columnsFrom": ["dispute_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "dispute_comments_reply_to_comment_id_fkey": {"name": "dispute_comments_reply_to_comment_id_fkey", "tableFrom": "dispute_comments", "tableTo": "dispute_comments", "schemaTo": "public", "columnsFrom": ["reply_to_comment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.organization": {"name": "organization", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "party_id": {"name": "party_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": false}, "number_of_employees": {"name": "number_of_employees", "type": "integer", "primaryKey": false, "notNull": false}, "annual_revenue": {"name": "annual_revenue", "type": "integer", "primaryKey": false, "notNull": false}, "website_url": {"name": "website_url", "type": "text", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"organization_party_id_fkey": {"name": "organization_party_id_fkey", "tableFrom": "organization", "tableTo": "party", "schemaTo": "public", "columnsFrom": ["party_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.vehicle_documents": {"name": "vehicle_documents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "integer", "primaryKey": false, "notNull": true}, "media_path": {"name": "media_path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "document_type": {"name": "document_type", "type": "documenttype", "typeSchema": "public", "primaryKey": false, "notNull": true}, "expiration_date": {"name": "expiration_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_vehicle_documents_id": {"name": "ix_vehicle_documents_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_documents_vehicle_id_fkey": {"name": "vehicle_documents_vehicle_id_fkey", "tableFrom": "vehicle_documents", "tableTo": "vehicles", "schemaTo": "public", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.company_ownership_invite": {"name": "company_ownership_invite", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "fraction": {"name": "fraction", "type": "numeric", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "companyownershipinviteenum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"company_ownership_invite_company_id_fkey": {"name": "company_ownership_invite_company_id_fkey", "tableFrom": "company_ownership_invite", "tableTo": "company", "schemaTo": "public", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.compliance_set_requirement_type_mapping": {"name": "compliance_set_requirement_type_mapping", "schema": "", "columns": {"compliance_set_id": {"name": "compliance_set_id", "type": "integer", "primaryKey": false, "notNull": true}, "compliance_requirement_type_id": {"name": "compliance_requirement_type_id", "type": "integer", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"compliance_set_requirement_ty_compliance_requirement_type__fkey": {"name": "compliance_set_requirement_ty_compliance_requirement_type__fkey", "tableFrom": "compliance_set_requirement_type_mapping", "tableTo": "compliance_requirement_type", "schemaTo": "public", "columnsFrom": ["compliance_requirement_type_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "compliance_set_requirement_type_mapping_compliance_set_id_fkey": {"name": "compliance_set_requirement_type_mapping_compliance_set_id_fkey", "tableFrom": "compliance_set_requirement_type_mapping", "tableTo": "compliance_set", "schemaTo": "public", "columnsFrom": ["compliance_set_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.voting_threshold": {"name": "voting_threshold", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": false, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "unanimous": {"name": "unanimous", "type": "boolean", "primaryKey": false, "notNull": false}, "simple_majority": {"name": "simple_majority", "type": "boolean", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_voting_threshold_id": {"name": "ix_voting_threshold_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {"voting_threshold_pkey": {"name": "voting_threshold_pkey", "columns": ["id", "company_id"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.company_notification_preferences": {"name": "company_notification_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": false, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "booking_notifications": {"name": "booking_notifications", "type": "boolean", "primaryKey": false, "notNull": false}, "payment_notifications": {"name": "payment_notifications", "type": "boolean", "primaryKey": false, "notNull": false}, "maintenance_alerts": {"name": "maintenance_alerts", "type": "boolean", "primaryKey": false, "notNull": false}, "member_activity": {"name": "member_activity", "type": "boolean", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"ix_company_notification_preferences_id": {"name": "ix_company_notification_preferences_id", "columns": [{"expression": "id", "asc": true, "nulls": "last", "opclass": "int4_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"company_notification_preferences_company_id_fkey": {"name": "company_notification_preferences_company_id_fkey", "tableFrom": "company_notification_preferences", "tableTo": "company", "schemaTo": "public", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"company_notification_preferences_pkey": {"name": "company_notification_preferences_pkey", "columns": ["id", "company_id"]}}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {"public.bookingstatus": {"name": "bookingstatus", "values": ["PENDING", "CONFIRMED", "CANCELLED", "COMPLETED"], "schema": "public"}, "public.cleanlinesslevel": {"name": "cleanlinesslevel", "values": ["clean", "acceptable", "dirty"], "schema": "public"}, "public.commentpriority": {"name": "commentpriority", "values": ["LOW", "MEDIUM", "HIGH"], "schema": "public"}, "public.commentstatus": {"name": "commentstatus", "values": ["OPEN", "RESOLVED", "DELETED"], "schema": "public"}, "public.companyownershipinviteenum": {"name": "companyownershipinviteenum", "values": ["SENT", "DECLINED", "ACCEPTED"], "schema": "public"}, "public.companytypeenum": {"name": "companytypeenum", "values": ["PRIVATE_COMPANY", "NON_PROFIT", "PARTNERSHIP", "COOPERATIVE"], "schema": "public"}, "public.compliancerequirementstatusenum": {"name": "compliancerequirementstatusenum", "values": ["ACCEPTED", "REJECTED", "INCOMPLETE", "PENDING"], "schema": "public"}, "public.conditionlevel": {"name": "conditionlevel", "values": ["none", "minor", "major"], "schema": "public"}, "public.dashboardcondition": {"name": "dashboardcondition", "values": ["working", "partial", "issues"], "schema": "public"}, "public.disputestatus": {"name": "disputestatus", "values": ["OPEN", "RESOLVED", "DELETED"], "schema": "public"}, "public.disputetype": {"name": "disputetype", "values": ["BOOKING", "vEEHICLE_DAMAGE", "VEHICLE_MAINTENANCE", "MAINTENANCE_COST_DISPUTE", "OTHER"], "schema": "public"}, "public.documenttype": {"name": "documenttype", "values": ["registration", "insurance", "inspection", "other"], "schema": "public"}, "public.generalcondition": {"name": "generalcondition", "values": ["good", "fair", "poor"], "schema": "public"}, "public.lightscondition": {"name": "lightscondition", "values": ["working", "partial", "broken"], "schema": "public"}, "public.odorlevel": {"name": "odorlevel", "values": ["none", "mild", "strong"], "schema": "public"}, "public.phototype": {"name": "phototype", "values": ["left_view", "right_view", "rear_view", "front_view", "dashboard", "seats_view", "interior", "additional", "tires", "signature"], "schema": "public"}, "public.possessionstatus": {"name": "possessionstatus", "values": ["PENDING", "COMPLETED", "CANCELLED"], "schema": "public"}, "public.priority": {"name": "priority", "values": ["LOW", "MEDIUM", "HIGH"], "schema": "public"}, "public.referencetypeenum": {"name": "referencetypeenum", "values": ["TAX_PIN", "URL"], "schema": "public"}, "public.servicestatus": {"name": "servicestatus", "values": ["SCHEDULED", "PENDING", "COMPLETED"], "schema": "public"}, "public.vehicleservicestatus": {"name": "vehicleservicestatus", "values": ["SCHEDULED", "PENDING", "COMPLETED"], "schema": "public"}, "public.verificationtypeenum": {"name": "verificationtypeenum", "values": ["AI", "MANUAL", "API"], "schema": "public"}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}}}