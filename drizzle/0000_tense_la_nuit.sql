-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TYPE "public"."bookingstatus" AS ENUM('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."cleanlinesslevel" AS ENUM('clean', 'acceptable', 'dirty');--> statement-breakpoint
CREATE TYPE "public"."commentpriority" AS ENUM('LOW', 'MEDIUM', 'HIGH');--> statement-breakpoint
CREATE TYPE "public"."commentstatus" AS ENUM('OPEN', 'RESOLVED', 'DELETED');--> statement-breakpoint
CREATE TYPE "public"."companyownershipinviteenum" AS ENUM('SENT', 'DECLINED', 'ACCEPTED');--> statement-breakpoint
CREATE TYPE "public"."companytypeenum" AS ENUM('PRIVATE_COMPANY', 'NON_PROFIT', 'PARTNERSHIP', 'COOPERATIVE');--> statement-breakpoint
CREATE TYPE "public"."compliancerequirementstatusenum" AS ENUM('ACCEPTED', 'REJECTED', 'INCOMPLETE', 'PENDING');--> statement-breakpoint
CREATE TYPE "public"."conditionlevel" AS ENUM('none', 'minor', 'major');--> statement-breakpoint
CREATE TYPE "public"."dashboardcondition" AS ENUM('working', 'partial', 'issues');--> statement-breakpoint
CREATE TYPE "public"."disputestatus" AS ENUM('OPEN', 'RESOLVED', 'DELETED');--> statement-breakpoint
CREATE TYPE "public"."disputetype" AS ENUM('BOOKING', 'vEEHICLE_DAMAGE', 'VEHICLE_MAINTENANCE', 'MAINTENANCE_COST_DISPUTE', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."documenttype" AS ENUM('registration', 'insurance', 'inspection', 'other');--> statement-breakpoint
CREATE TYPE "public"."generalcondition" AS ENUM('good', 'fair', 'poor');--> statement-breakpoint
CREATE TYPE "public"."lightscondition" AS ENUM('working', 'partial', 'broken');--> statement-breakpoint
CREATE TYPE "public"."odorlevel" AS ENUM('none', 'mild', 'strong');--> statement-breakpoint
CREATE TYPE "public"."phototype" AS ENUM('left_view', 'right_view', 'rear_view', 'front_view', 'dashboard', 'seats_view', 'interior', 'additional', 'tires', 'signature');--> statement-breakpoint
CREATE TYPE "public"."possessionstatus" AS ENUM('PENDING', 'COMPLETED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."priority" AS ENUM('LOW', 'MEDIUM', 'HIGH');--> statement-breakpoint
CREATE TYPE "public"."referencetypeenum" AS ENUM('TAX_PIN', 'URL');--> statement-breakpoint
CREATE TYPE "public"."servicestatus" AS ENUM('SCHEDULED', 'PENDING', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."vehicleservicestatus" AS ENUM('SCHEDULED', 'PENDING', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."verificationtypeenum" AS ENUM('AI', 'MANUAL', 'API');--> statement-breakpoint
CREATE TABLE "cities" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar NOT NULL,
	"country" varchar NOT NULL,
	"province" varchar NOT NULL,
	CONSTRAINT "uq_city_name_province" UNIQUE("name","province")
);
--> statement-breakpoint
CREATE TABLE "account_type" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "account_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "alembic_version" (
	"version_num" varchar(32) PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "company_ownership" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"company_id" integer NOT NULL,
	"fraction" numeric NOT NULL,
	"effective_from" timestamp with time zone NOT NULL,
	"effective_to" timestamp with time zone,
	"is_active" boolean NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "compliance_set" (
	"name" text NOT NULL,
	"description" text,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "compliance_requirement_type" (
	"name" varchar NOT NULL,
	"description" varchar,
	"default_validity_in_days" integer,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "compliance_requirement_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "address_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "address_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "bookings" (
	"vehicle_id" integer NOT NULL,
	"reference" varchar(50) NOT NULL,
	"start_datetime" timestamp NOT NULL,
	"end_datetime" timestamp NOT NULL,
	"status" "bookingstatus",
	"total_price" double precision,
	"notes" text,
	"party_id" integer NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "bookings_reference_key" UNIQUE("reference")
);
--> statement-breakpoint
CREATE TABLE "compliance_requirement" (
	"party_id" integer NOT NULL,
	"compliance_set_id" integer NOT NULL,
	"requirement_type_id" integer NOT NULL,
	"reviewed_by_id" integer,
	"status" "compliancerequirementstatusenum",
	"reference_type" "referencetypeenum",
	"reference" varchar,
	"submitted_at" timestamp,
	"reviewed_at" timestamp,
	"notes" text,
	"issue_date" timestamp,
	"expiry_date" timestamp,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	"issuing_authority_id" integer,
	"uploaded_individual_id" integer,
	"verification_id" integer
);
--> statement-breakpoint
CREATE TABLE "contact_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "contact_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "contract_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"display_order" integer,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "contract_status_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "contact_point" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"contact_point_type_id" integer NOT NULL,
	"value" text NOT NULL,
	"address_type_id" integer,
	"is_primary" boolean NOT NULL,
	"is_verified" boolean NOT NULL,
	"verification_date" timestamp,
	"verification_method" text,
	"mtadata" json,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "contact_point_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"validation_pattern" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "contact_point_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "contract_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "contract_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "identification_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"validation_pattern" text,
	"expiration_required" boolean,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "identification_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "dispute_media" (
	"dispute_id" integer NOT NULL,
	"media_path" varchar NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "document_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "document_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "emergency_contacts" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar NOT NULL,
	"phone_number" varchar NOT NULL,
	"contact_type" varchar NOT NULL,
	"description" varchar,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "individual" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"first_name" text NOT NULL,
	"last_name" text NOT NULL,
	"middle_name" text,
	"salutation" text,
	"suffix" text,
	"gender" text,
	"birth_date" timestamp,
	"marital_status" text,
	"nationality" text,
	"preferred_language" text,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "disputes" (
	"name" varchar NOT NULL,
	"description" text,
	"vehicle_id" integer NOT NULL,
	"dispute_type" "disputetype" NOT NULL,
	"party_offending" integer NOT NULL,
	"party_logging" integer NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"priority" "priority",
	"company_id" integer NOT NULL,
	"dispute_status" "disputestatus"
);
--> statement-breakpoint
CREATE TABLE "industry" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "industry_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "lead" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"company" text,
	"title" text,
	"status_id" integer NOT NULL,
	"source_id" integer,
	"rating" text,
	"annual_revenue" integer,
	"number_of_employees" integer,
	"industry" text,
	"description" text,
	"is_converted" boolean NOT NULL,
	"converted_date" timestamp,
	"converted_account_id" integer,
	"converted_contact_id" integer,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "lead_source" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "lead_source_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "lead_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"display_order" integer,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "lead_status_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "match_rule_type" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"confidence" integer,
	"is_active" boolean,
	"priority" integer,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "opportunity_stage" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"probability" integer,
	"display_order" integer,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "opportunity_stage_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "opportunity_type" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "opportunity_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "issuing_authority" (
	"party_id" integer NOT NULL,
	"name" varchar NOT NULL,
	"description" varchar,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	"country" varchar,
	CONSTRAINT "issuing_authority_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "party_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "party_status_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "party" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_type_id" integer NOT NULL,
	"status_id" integer NOT NULL,
	"external_id" text,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "party_external_id_key" UNIQUE("external_id")
);
--> statement-breakpoint
CREATE TABLE "party_identification" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"identification_type_id" integer NOT NULL,
	"document_number" text NOT NULL,
	"issuing_authority" text,
	"issue_date" timestamp,
	"expiry_date" timestamp,
	"is_verified" boolean NOT NULL,
	"verification_date" timestamp,
	"verification_method" text,
	"document_image_url" text,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "product_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "product_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "record_type" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "record_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "relationship_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "relationship_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "vehicle_inspections" (
	"scratches" "conditionlevel" NOT NULL,
	"dents" "conditionlevel" NOT NULL,
	"tires" "generalcondition" NOT NULL,
	"lights" "lightscondition" NOT NULL,
	"cleanliness" "cleanlinesslevel" NOT NULL,
	"seats" "generalcondition" NOT NULL,
	"dashboard_controls" "dashboardcondition" NOT NULL,
	"odors" "odorlevel" NOT NULL,
	"odometer" integer NOT NULL,
	"known_issues" text,
	"vehicle_id" integer NOT NULL,
	"possession_id" integer NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "social_media_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"base_url" text,
	"icon" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "social_media_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "subscription_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"display_order" integer,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "subscription_status_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"username" varchar(50) NOT NULL,
	"email" varchar(100) NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	"family_name" varchar(100),
	"given_name" varchar(100),
	"phone_number" varchar(20),
	"org_id" varchar(16),
	CONSTRAINT "users_username_key" UNIQUE("username"),
	CONSTRAINT "users_email_key" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "vehicle_maintenance" (
	"vehicle_id" integer NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" varchar(500),
	"due_date" timestamp NOT NULL,
	"due_odometer" double precision NOT NULL,
	"status" "vehicleservicestatus" NOT NULL,
	"expected_cost" double precision NOT NULL,
	"completed_date" timestamp,
	"completed_odometer" double precision,
	"actual_cost" double precision,
	"technician_notes" varchar(1000),
	"service_provider" varchar(1000),
	"created_at" timestamp,
	"updated_at" timestamp,
	"is_scheduled" boolean,
	"id" serial NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_make" (
	"name" varchar NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "party_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "party_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "social_profile" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"social_media_type_id" integer NOT NULL,
	"username" text NOT NULL,
	"url" text,
	"is_primary" boolean NOT NULL,
	"is_verified" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "vehicle_model_media" (
	"vehicle_model_id" integer NOT NULL,
	"media_path" varchar NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_photos" (
	"inspection_id" integer NOT NULL,
	"type" "phototype" NOT NULL,
	"file_url" varchar NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "verification" (
	"verifying_party_id" integer NOT NULL,
	"verification_outcome" varchar,
	"cost" double precision,
	"verification_type" "verificationtypeenum",
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "vehicle_possessions" (
	"from_party_id" integer NOT NULL,
	"to_party_id" integer NOT NULL,
	"vehicle_id" integer NOT NULL,
	"handover_expected_datetime" timestamp,
	"handover_actual_datetime" timestamp,
	"status" "possessionstatus" NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_media" (
	"vehicle_id" integer NOT NULL,
	"media_path" varchar NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_model" (
	"make_id" integer NOT NULL,
	"model" varchar NOT NULL,
	"year_model" integer NOT NULL,
	"description" text,
	"transmission" varchar,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicles" (
	"party_id" integer NOT NULL,
	"model_id" integer NOT NULL,
	"vin_number" varchar NOT NULL,
	"vehicle_registration" varchar,
	"country_of_registration" varchar,
	"manufacturing_year" integer,
	"purchase_date" timestamp with time zone,
	"color" varchar,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "vehicles_vin_number_key" UNIQUE("vin_number")
);
--> statement-breakpoint
CREATE TABLE "company" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"registration_number" varchar,
	"registration_country" varchar,
	"registration_date" timestamp with time zone,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"alias" varchar,
	"description" varchar,
	"city_id" integer,
	"purpose" varchar
);
--> statement-breakpoint
CREATE TABLE "dispute_comments" (
	"comment" text NOT NULL,
	"dispute_id" integer NOT NULL,
	"reply_to_comment_id" integer,
	"user_id" integer NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "organization" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"industry" text,
	"number_of_employees" integer,
	"annual_revenue" integer,
	"website_url" text,
	"logo_url" text,
	"is_deleted" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "vehicle_documents" (
	"id" serial PRIMARY KEY NOT NULL,
	"vehicle_id" integer NOT NULL,
	"media_path" varchar NOT NULL,
	"document_type" "documenttype" NOT NULL,
	"expiration_date" timestamp with time zone,
	"name" varchar,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "company_ownership_invite" (
	"id" serial PRIMARY KEY NOT NULL,
	"company_id" integer NOT NULL,
	"fraction" numeric NOT NULL,
	"status" "companyownershipinviteenum" NOT NULL,
	"email" varchar(100) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"first_name" text,
	"last_name" text
);
--> statement-breakpoint
CREATE TABLE "compliance_set_requirement_type_mapping" (
	"compliance_set_id" integer NOT NULL,
	"compliance_requirement_type_id" integer NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "voting_threshold" (
	"id" integer NOT NULL,
	"company_id" integer NOT NULL,
	"unanimous" boolean,
	"simple_majority" boolean,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "voting_threshold_pkey" PRIMARY KEY("id","company_id")
);
--> statement-breakpoint
CREATE TABLE "company_notification_preferences" (
	"id" integer NOT NULL,
	"company_id" integer NOT NULL,
	"booking_notifications" boolean,
	"payment_notifications" boolean,
	"maintenance_alerts" boolean,
	"member_activity" boolean,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "company_notification_preferences_pkey" PRIMARY KEY("id","company_id")
);
--> statement-breakpoint
ALTER TABLE "company_ownership" ADD CONSTRAINT "company_ownership_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."company"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company_ownership" ADD CONSTRAINT "company_ownership_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_requirement" ADD CONSTRAINT "compliance_requirement_compliance_set_id_fkey" FOREIGN KEY ("compliance_set_id") REFERENCES "public"."compliance_set"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_requirement" ADD CONSTRAINT "compliance_requirement_issuing_authority_id_fkey" FOREIGN KEY ("issuing_authority_id") REFERENCES "public"."issuing_authority"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_requirement" ADD CONSTRAINT "compliance_requirement_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_requirement" ADD CONSTRAINT "compliance_requirement_requirement_type_id_fkey" FOREIGN KEY ("requirement_type_id") REFERENCES "public"."compliance_requirement_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_requirement" ADD CONSTRAINT "compliance_requirement_reviewed_by_id_fkey" FOREIGN KEY ("reviewed_by_id") REFERENCES "public"."individual"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_requirement" ADD CONSTRAINT "compliance_requirement_uploaded_individual_id_fkey" FOREIGN KEY ("uploaded_individual_id") REFERENCES "public"."individual"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_requirement" ADD CONSTRAINT "compliance_requirement_verification_id_fkey" FOREIGN KEY ("verification_id") REFERENCES "public"."verification"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "contact_point" ADD CONSTRAINT "contact_point_address_type_id_fkey" FOREIGN KEY ("address_type_id") REFERENCES "public"."address_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "contact_point" ADD CONSTRAINT "contact_point_contact_point_type_id_fkey" FOREIGN KEY ("contact_point_type_id") REFERENCES "public"."contact_point_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "contact_point" ADD CONSTRAINT "contact_point_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispute_media" ADD CONSTRAINT "dispute_media_dispute_id_fkey" FOREIGN KEY ("dispute_id") REFERENCES "public"."disputes"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "individual" ADD CONSTRAINT "individual_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "disputes" ADD CONSTRAINT "disputes_party_offending_fkey" FOREIGN KEY ("party_offending") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "disputes" ADD CONSTRAINT "disputes_party_logging_fkey" FOREIGN KEY ("party_logging") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "disputes" ADD CONSTRAINT "disputes_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."company"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lead" ADD CONSTRAINT "lead_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lead" ADD CONSTRAINT "lead_source_id_fkey" FOREIGN KEY ("source_id") REFERENCES "public"."lead_source"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lead" ADD CONSTRAINT "lead_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."lead_status"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "issuing_authority" ADD CONSTRAINT "issuing_authority_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "party" ADD CONSTRAINT "party_party_type_id_fkey" FOREIGN KEY ("party_type_id") REFERENCES "public"."party_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "party" ADD CONSTRAINT "party_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."party_status"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "party_identification" ADD CONSTRAINT "party_identification_identification_type_id_fkey" FOREIGN KEY ("identification_type_id") REFERENCES "public"."identification_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "party_identification" ADD CONSTRAINT "party_identification_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_inspections" ADD CONSTRAINT "vehicle_inspections_possession_id_fkey" FOREIGN KEY ("possession_id") REFERENCES "public"."vehicle_possessions"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_inspections" ADD CONSTRAINT "vehicle_inspections_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_maintenance" ADD CONSTRAINT "vehicle_maintenance_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "social_profile" ADD CONSTRAINT "social_profile_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "social_profile" ADD CONSTRAINT "social_profile_social_media_type_id_fkey" FOREIGN KEY ("social_media_type_id") REFERENCES "public"."social_media_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_model_media" ADD CONSTRAINT "vehicle_model_media_vehicle_model_id_fkey" FOREIGN KEY ("vehicle_model_id") REFERENCES "public"."vehicle_model"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_photos" ADD CONSTRAINT "vehicle_photos_inspection_id_fkey" FOREIGN KEY ("inspection_id") REFERENCES "public"."vehicle_inspections"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "verification" ADD CONSTRAINT "verification_verifying_party_id_fkey" FOREIGN KEY ("verifying_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_possessions" ADD CONSTRAINT "vehicle_possessions_from_party_id_fkey" FOREIGN KEY ("from_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_possessions" ADD CONSTRAINT "vehicle_possessions_to_party_id_fkey" FOREIGN KEY ("to_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_possessions" ADD CONSTRAINT "vehicle_possessions_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_media" ADD CONSTRAINT "vehicle_media_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_model" ADD CONSTRAINT "vehicle_model_make_id_fkey" FOREIGN KEY ("make_id") REFERENCES "public"."vehicle_make"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicles" ADD CONSTRAINT "vehicles_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "public"."vehicle_model"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicles" ADD CONSTRAINT "vehicles_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company" ADD CONSTRAINT "company_city_id_fkey" FOREIGN KEY ("city_id") REFERENCES "public"."cities"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company" ADD CONSTRAINT "company_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispute_comments" ADD CONSTRAINT "dispute_comments_dispute_id_fkey" FOREIGN KEY ("dispute_id") REFERENCES "public"."disputes"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispute_comments" ADD CONSTRAINT "dispute_comments_reply_to_comment_id_fkey" FOREIGN KEY ("reply_to_comment_id") REFERENCES "public"."dispute_comments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "organization" ADD CONSTRAINT "organization_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_documents" ADD CONSTRAINT "vehicle_documents_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company_ownership_invite" ADD CONSTRAINT "company_ownership_invite_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."company"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_set_requirement_type_mapping" ADD CONSTRAINT "compliance_set_requirement_ty_compliance_requirement_type__fkey" FOREIGN KEY ("compliance_requirement_type_id") REFERENCES "public"."compliance_requirement_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_set_requirement_type_mapping" ADD CONSTRAINT "compliance_set_requirement_type_mapping_compliance_set_id_fkey" FOREIGN KEY ("compliance_set_id") REFERENCES "public"."compliance_set"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company_notification_preferences" ADD CONSTRAINT "company_notification_preferences_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."company"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "ix_cities_id" ON "cities" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_bookings_id" ON "bookings" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_dispute_media_id" ON "dispute_media" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_emergency_contacts_id" ON "emergency_contacts" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_disputes_id" ON "disputes" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_inspections_id" ON "vehicle_inspections" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_maintenance_id" ON "vehicle_maintenance" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_make_id" ON "vehicle_make" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_model_media_id" ON "vehicle_model_media" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_photos_id" ON "vehicle_photos" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_possessions_id" ON "vehicle_possessions" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_media_id" ON "vehicle_media" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_model_id" ON "vehicle_model" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicles_id" ON "vehicles" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_dispute_comments_id" ON "dispute_comments" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_documents_id" ON "vehicle_documents" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_voting_threshold_id" ON "voting_threshold" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_company_notification_preferences_id" ON "company_notification_preferences" USING btree ("id" int4_ops);
*/