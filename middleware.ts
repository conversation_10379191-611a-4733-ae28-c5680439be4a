import { runWithAmplifyServerContext } from "@/lib/amplifyServerUtils";
import { fetchAuthSession, fetchUserAttributes } from "aws-amplify/auth/server";
import { NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  const response = NextResponse.next();

  const authenticated = await runWithAmplifyServerContext({
    nextServerContext: { request, response },
    operation: async (contextSpec) => {
      try {
        const session = await fetchAuthSession(contextSpec);
        const atrr_cookie = request.cookies.get("user_attrs");
        const access_cookie = request.cookies.get("access_cookie");
        if (!atrr_cookie) {
          const attributes = await fetchUserAttributes(contextSpec);
          response.cookies.set("user_attrs", JSON.stringify(attributes), {
            path: "/",
            httpOnly: true,
          });
        }
        if (!access_cookie && session.tokens?.accessToken) {
          response.cookies.set(
            "access_cookie",
            session.tokens?.accessToken.toString(),
            {
              path: "/",
              httpOnly: true,
            }
          );
        }
        return (
          session.tokens?.accessToken !== undefined &&
          session.tokens?.idToken !== undefined
        );
      } catch (error) {
        console.log(error);
        return false;
      }
    },
  });

  if (authenticated) {
    return response;
  }

  return NextResponse.redirect(new URL("/login", request.url));
}
export const config = {
  matcher: [
    "/((?!_next|logout|signup|login|verification|welcome|splash-screen|/favicon.ico).*)",
  ],
};
