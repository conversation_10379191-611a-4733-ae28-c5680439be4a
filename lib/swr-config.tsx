"use client";

import { SWRConfig } from "swr";
import { ReactNode } from "react";

// Local storage provider for SWR to persist cache between sessions
const localStorageProvider = () => {
  // When initializing, we restore the data from localStorage
  const map = new Map(JSON.parse(localStorage.getItem("swr-cache") || "[]"));

  // Before unloading the app, we write back all data to localStorage
  window.addEventListener("beforeunload", () => {
    const appCache = JSON.stringify(Array.from(map.entries()));
    localStorage.setItem("swr-cache", appCache);
  });

  // We still use the map for write & read for performance
  return map;
};

// Global SWR configuration
export function SWRConfigProvider({ children }: { children: ReactNode }) {
  return (
    <SWRConfig
      value={{
        provider: typeof window !== "undefined" ? localStorageProvider : null,
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
        dedupingInterval: 300000, // 5 minutes cache
        errorRetryCount: 2,
        onError: (error) => {
          console.error("SWR Global Error:", error);
        },
      }}
    >
      {children}
    </SWRConfig>
  );
}
