"use server";

import { Amplify } from "aws-amplify";
import { fetchAuthSession } from "aws-amplify/auth/server";
import { cookies } from "next/headers";
import outputs from "../amplify_outputs.json";
import { runWithAmplifyServerContext } from "./amplifyServerUtils";
import { signOutAction } from "@/actions/signout";
Amplify.configure(outputs, { ssr: true });

export async function getAuthorizedFetch() {
  const cookieStore = await cookies();
  const cookie = cookieStore.get("access_cookie");

  const token = cookie?.value;
  if (!token) {
    console.warn("No token found, redirecting to login");
  }

  const baseURL = process.env.NEXT_PUBLIC_API_URL || "http://api.poollysa.com";

  return async function authorizedFetch(
    endpoint: string,
    options: RequestInit = {},
    nextOptions?: { revalidate?: number }
  ) {
    const headers = new Headers(options.headers || {});
    headers.set("Authorization", `Bearer ${token}`);
    headers.set("Content-Type", "application/json");

    const fetchInit = {
      ...options,
      headers,
    } as RequestInit & { next?: { revalidate?: number | false } };

    if (nextOptions?.revalidate !== undefined) {
      fetchInit.next = { revalidate: nextOptions.revalidate };
    } else {
      fetchInit.cache = "no-store";
    }

    const url = `${baseURL}${endpoint}`;
    let res = await fetch(url, fetchInit);

    if (res.status === 401) {
      try {
        let session = await runWithAmplifyServerContext({
          nextServerContext: { cookies },
          operation: async (contextSpec) => {
            try {
              return await fetchAuthSession(contextSpec);
            } catch (error) {
              console.error("Session fetch error", error);
              throw new Error("Authentication error");
            }
          },
        });
        session = await runWithAmplifyServerContext({
          nextServerContext: { cookies },
          operation: async (contextSpec) =>
            fetchAuthSession(contextSpec, { forceRefresh: true }),
        });

        const newToken = session?.tokens?.accessToken?.toString();
        if (newToken) {
          headers.set("Authorization", `Bearer ${newToken}`);
          res = await fetch(url, fetchInit);
        }
      } catch (error: any) {
        console.error("Token refresh failed:", error);
        if (error.message === "Unauthorized") {
          await signOutAction();
        }
      }
    }

    return res;
  };
}
