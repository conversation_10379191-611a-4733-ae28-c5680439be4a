import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import type { RootState } from "../../store";

type AuthState = {
  email: string | null;
  party_id: number | null;
  id: number | null;
  sub: string | null;
  first_name: string;
  last_name: string;
};

const initialState: AuthState = {
  email: null,
  party_id: null,
  id: null,
  sub: null,
  first_name: "",
  last_name: "",
};

const slice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setCredentials: (state, { payload }: PayloadAction<Partial<AuthState>>) => {
      if (payload.email !== undefined) state.email = payload.email;
      if (payload.party_id !== undefined) state.party_id = payload.party_id;
      if (payload.id !== undefined) state.id = payload.id;
      if (payload.sub !== undefined) state.sub = payload.sub;
      if (payload.first_name !== undefined)
        state.first_name = payload.first_name;
      if (payload.last_name !== undefined) state.last_name = payload.last_name;
    },
  },
});

export const { setCredentials } = slice.actions;

export default slice.reducer;

export const selectCurrentUser = (state: RootState) => state.auth;
