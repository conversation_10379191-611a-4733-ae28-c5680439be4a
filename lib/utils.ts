import { getCurrentUser } from "aws-amplify/auth";
import { getUrl, remove, uploadData } from "aws-amplify/storage";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { v4 as uuidv4 } from "uuid";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export async function DocumentUpload(
  file: File,
  folder: string = "profile-pictures"
) {
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("User not authenticated.");
  }
  if (file) {
    const fileType = file.type;
    const fileExtension = fileType.split("/")[1];
    const uniqueId = uuidv4();
    return await uploadData({
      path: `${folder}/${uniqueId}.${fileExtension}`,
      data: file,
      options: {
        metadata: {
          "Content-Disposition": "inline",
        },
        contentType: fileType,
      },
    }).result;
  }
}

export function formatDateForInput(
  dateString?: string,
  includeTime = false
): string {
  if (!dateString) return "";
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return "";

  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "long",
    day: "numeric",
    ...(includeTime && {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    }),
  };

  return date.toLocaleString("en-US", options);
}

// New function specifically for HTML date inputs in UI display (YYYY-MM-DD format)
export function formatDateForHtmlInputDisplay(dateString?: string): string {
  if (!dateString) return "";
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return "";

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

export function getFileExtension(url: string | undefined | null) {
  if (!url) return "unkown";
  return url.split(".").pop()?.toLowerCase();
}

export async function generateDocumentUrl(
  path: string | undefined | null
): Promise<string> {
  if (!path) return "";
  try {
    const getUrlResult = await getUrl({
      path,
      options: {
        validateObjectExistence: false,
        expiresIn: 20,
        useAccelerateEndpoint: false,
      },
    });
    return String(getUrlResult.url);
  } catch (error) {
    console.error("Error generating image upload URL:", error);
    throw error;
  }
}

export async function DocumentDelete(path: string) {
  try {
    await remove({
      path: path,
    });
  } catch (error) {
    console.log("Error ", error);
  }
}

export function apiErrorFormater(error: any) {
  const defaultErrorMessage = error?.response?.data?.message || error?.message;
  const details = error?.response?.data?.detail;
  let formErrors: string[] = [];

  if (Array.isArray(details)) {
    formErrors = details.map((err: any) => {
      const path = err?.loc?.slice(1).join(".") || "unknown field";
      return `${path}: ${err.msg}`;
    });
  } else if (typeof details === "string") {
    formErrors = [details];
  } else {
    formErrors = [defaultErrorMessage];
  }

  return {
    errors: {
      form: formErrors,
    },
  };
}

export function getStatusColor(status: string) {
  switch (status) {
    case "available":
      return "bg-[#e6ffe6] text-[#009639]";
    case "in-use":
      return "bg-[#FFD700] text-[#333333]";
    case "maintenance":
      return "bg-[#ffe6e6] text-[#d32f2f]";
    case "upcoming":
      return "bg-[#e6ffe6] text-[#009639]";
    case "overdue":
      return "bg-[#ffe6e6] text-[#d32f2f]";
    case "in-progress":
      return "bg-[#FFD700] text-[#333333]";
    default:
      return "bg-[#f2f2f2] text-[#333333]";
  }
}

export function getStatusLabel(status: string) {
  switch (status) {
    case "available":
      return "Available";
    case "in-use":
      return "In Use";
    case "maintenance":
      return "Maintenance";
    case "upcoming":
      return "Upcoming";
    case "overdue":
      return "Overdue";
    case "in-progress":
      return "In Progress";
    default:
      return status;
  }
}

export function calculateTimeRemaining(endTime: string) {
  const now = new Date();
  const target = new Date(endTime);
  const diffMs = target.getTime() - now.getTime();

  if (diffMs <= 0) {
    return { hours: 0, minutes: 0 };
  }

  const totalMinutes = Math.floor(diffMs / 1000 / 60);
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return { hours, minutes };
}

export function removeUndefined<T extends object>(obj: T): Partial<T> {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, v]) => v !== undefined)
  ) as Partial<T>;
}
