"use server";

import { Amplify } from "aws-amplify";
import { fetchAuthSession } from "aws-amplify/auth/server";
import axios from "axios";
import { cookies } from "next/headers";
import outputs from "../amplify_outputs.json";
import { runWithAmplifyServerContext } from "./amplifyServerUtils";

Amplify.configure(outputs, { ssr: true });

export async function getAuthorizedAxios() {
  let session = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: async (contextSpec) => {
      try {
        return await fetchAuthSession(contextSpec);
      } catch (error) {
        console.error("Session fetch error", error);
        throw new Error("Authentication error");
      }
    },
  });
  const token = session?.tokens?.accessToken?.toString();
  if (!token) {
    console.warn("No token found, redirecting to login");
  }

  const instance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || "http://api.poollysa.com",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  instance.interceptors.response.use(
    (res) => res,
    async (error) => {
      if (error.response?.status === 401) {
        try {
          session = await runWithAmplifyServerContext({
            nextServerContext: { cookies },
            operation: async (contextSpec) => {
              return await fetchAuthSession(contextSpec, {
                forceRefresh: true,
              });
            },
          });
          const newToken = session?.tokens?.accessToken?.toString();
          if (newToken) {
            error.config.headers.Authorization = `Bearer ${newToken}`;
            return axios.request(error.config);
          }
        } catch (refreshError) {
          console.error("Token refresh failed:", refreshError);
        }
      }
      return Promise.reject(error);
    }
  );

  return instance;
}
