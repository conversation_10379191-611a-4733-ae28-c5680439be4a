import outputs from "@/amplify_outputs.json";
import { createServerRunner } from "@aws-amplify/adapter-nextjs";
import { fetchUserAttributes } from "aws-amplify/auth/server";
import { cookies } from "next/headers";

export const { runWithAmplifyServerContext, createAuthRouteHandlers } =
  createServerRunner({
    config: outputs,
    runtimeOptions: {
      cookies: {
        domain: ".poollysa.com",
        sameSite: "strict",
        maxAge: 60 * 60 * 24 * 7,
      },
    },
  });

export async function getUserAttributes() {
  const cookieStore = await cookies();
  const cookie = cookieStore.get("user_attrs");
  if (cookie) {
    try {
      return JSON.parse(cookie.value);
    } catch (e) {
      console.error("Failed to parse user_attrs cookie", e);
    }
  }

  return await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: async (contextSpec) => {
      try {
        return await fetchUserAttributes(contextSpec);
      } catch (error: any) {
        if (
          error?.name === "AuthError" &&
          error?.recoverySuggestion?.includes("Sign in")
        ) {
          console.warn("Auth error: retrying after delay");
          await new Promise((res) => setTimeout(res, 300)); // retry after short delay
          try {
            return await fetchUserAttributes(contextSpec);
          } catch (retryError) {
            console.error("Retry failed", retryError);
            return null;
          }
        }

        console.error("Session fetch error", error);
        return null;
      }
    },
  });
}
