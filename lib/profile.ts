import { getIdentificationTypes } from "@/actions/identification-types";
import { getPartyIdentificationsByParty } from "@/actions/party-identifications";
import { generateImageUploadUrl } from "@/actions/images";

const cache = new Map<string, { data: string | null; expiry: number }>();
const CACHE_TTL = 60 * 60 * 1000;

export async function getProfileImageUrl(
  partyId: number
): Promise<string | null> {
  const cacheKey = `profileImageUrl_${partyId}`;

  const cached = cache.get(cacheKey);
  if (cached && cached.expiry > Date.now()) {
    return cached.data;
  }

  try {
    const identificationTypes = await getIdentificationTypes();
    const partyIdentifications = await getPartyIdentificationsByParty(partyId);
    const profilePicType = identificationTypes?.find(
      (c) => c.name === "ProfilePicture"
    );
    if (!profilePicType) {
      cache.set(cacheKey, { data: null, expiry: Date.now() + CACHE_TTL });
      return null;
    }
    const profilePic = partyIdentifications?.find(
      (id) => id.identification_type_id === profilePicType.id
    );
    if (!profilePic?.document_image_url) {
      cache.set(cacheKey, { data: null, expiry: Date.now() + CACHE_TTL });
      return null;
    }
    const imageUrl = await generateImageUploadUrl(
      profilePic.document_image_url
    );

    cache.set(cacheKey, { data: imageUrl, expiry: Date.now() + CACHE_TTL });
    return imageUrl;
  } catch (error) {
    cache.set(cacheKey, { data: null, expiry: Date.now() + CACHE_TTL });
    return null;
  }
}
