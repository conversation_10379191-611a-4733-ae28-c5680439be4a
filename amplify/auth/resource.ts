import { defineAuth } from "@aws-amplify/backend";
import { postConfirmation } from "./post-confirmation/resource";

/**
 * Define and configure your auth resource
 * @see https://docs.amplify.aws/gen2/build-a-backend/auth
 */
export const auth = defineAuth({
  loginWith: {
    email: true,
  },
  triggers: {
    postConfirmation,
  },
  userAttributes: {
    email: {
      mutable: true,
      required: true,
    },
    familyName: {
      mutable: true,
      required: false,
    },

    givenName: {
      mutable: true,
      required: false,
    },

    phoneNumber: {
      mutable: true,
      required: false,
    },
    "custom:db_id": {
      dataType: "String",
      mutable: true,
      maxLen: 16,
      minLen: 1,
    },
  },
  groups: ["ADMINS", "GROUP_ADMIN", "GROUP_USER"],
});
