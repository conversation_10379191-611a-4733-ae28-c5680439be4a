<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.5.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 750 500" style="enable-background:new 0 0 750 500;" xml:space="preserve">
<g id="Background_Simple">
	<g>
		<path style="fill:#92E3A9;" d="M735.091,206.941c-2.735-42.06-28.076-82.17-65.31-112.75
			c-21.783-17.89-47.726-33.412-75.825-45.329c-29.162-12.367-60.884-26.238-95.472-27.722c-8.536-0.366-17.253,0.087-25.574,2.028
			c-31.862,7.434,32.852,30.063,41.326,33.394C424.09,31.758,330.805,6.593,233.683,11.262c-18.727,0.9-38.097,3.095-54.254,10.763
			c-16.157,7.669-28.286,22.047-25.208,35.971c2.544,11.51,14.226,24.178,4.213,33.434c-2.863,2.647-7.018,4.265-11.104,5.68
			c-20.968,7.262-43.772,11.326-63.536,20.415C53.188,131.6,32.99,156.679,24.275,182.794c-8.715,26.116-7.027,53.322-1.978,79.586
			c9.503,49.429,31.525,98.192,73.37,135.885c59.771,53.84,154.988,79.859,249.316,82.443
			c114.14,3.126,236.313-28.854,308.598-98.311c10.721-10.301,20.466-21.668,23.965-34.371c3.5-12.703-0.36-27.009-13.232-35.023
			c-3.469-2.16-7.911-4.433-7.7-7.906c0.285-4.699,8.231-6.458,14.297-7.575c39.801-7.327,61.204-41.992,63.968-72.811
			C735.413,218.763,735.474,212.833,735.091,206.941z"/>
		<path style="opacity:0.7;fill:#FFFFFF;" d="M735.091,206.941c-2.735-42.06-28.076-82.17-65.31-112.75
			c-21.783-17.89-47.726-33.412-75.825-45.329c-29.162-12.367-60.884-26.238-95.472-27.722c-8.536-0.366-17.253,0.087-25.574,2.028
			c-31.862,7.434,32.852,30.063,41.326,33.394C424.09,31.758,330.805,6.593,233.683,11.262c-18.727,0.9-38.097,3.095-54.254,10.763
			c-16.157,7.669-28.286,22.047-25.208,35.971c2.544,11.51,14.226,24.178,4.213,33.434c-2.863,2.647-7.018,4.265-11.104,5.68
			c-20.968,7.262-43.772,11.326-63.536,20.415C53.188,131.6,32.99,156.679,24.275,182.794c-8.715,26.116-7.027,53.322-1.978,79.586
			c9.503,49.429,31.525,98.192,73.37,135.885c59.771,53.84,154.988,79.859,249.316,82.443
			c114.14,3.126,236.313-28.854,308.598-98.311c10.721-10.301,20.466-21.668,23.965-34.371c3.5-12.703-0.36-27.009-13.232-35.023
			c-3.469-2.16-7.911-4.433-7.7-7.906c0.285-4.699,8.231-6.458,14.297-7.575c39.801-7.327,61.204-41.992,63.968-72.811
			C735.413,218.763,735.474,212.833,735.091,206.941z"/>
	</g>
</g>
<g id="Buildings">
	<g style="opacity:0.31;">
		<g>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="36.036,135.486 36.036,236.479 49.334,236.479 
				49.334,361.954 64.677,361.954 64.677,174.657 93.318,174.657 93.318,98.762 139.348,98.762 139.348,238.519 156.737,238.519 
				156.737,182.922 195.607,182.922 195.607,195.164 228.85,195.164 228.85,360.424 242.148,360.424 242.148,330.33 278.461,330.33 
				278.461,359.914 295.85,359.914 295.85,148.798 354.666,148.798 354.666,350.733 371.543,350.733 			"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="27.17,135.486 36.036,135.486 36.036,128.856 
				26.28,128.856 			"/>
			<rect x="64.502" y="174.657" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="28.816" height="3.357"/>
			<rect x="93.182" y="98.762" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="46.166" height="3.266"/>
			<rect x="160.399" y="175.99" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="6.722" height="6.927"/>
			<rect x="181.236" y="178.895" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="3.361" height="4.022"/>
			<rect x="195.607" y="195.164" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="33.354" height="4.065"/>
			<rect x="295.85" y="145.766" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="58.807" height="3.032"/>
			<rect x="310.965" y="139.733" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="23.302" height="6.033"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="329.338,139.956 329.338,134.37 346.366,134.37 
				346.366,145.542 			"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="217.104,193.495 217.104,148.341 271.003,148.341 
				271.003,185.754 289.544,185.754 289.544,122.109 358.713,122.109 358.713,199.085 363.13,199.085 			"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="178.296,171.133 178.296,121.249 141.645,121.249 			
				"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="62.736,183.604 50.662,183.604 50.662,148.771 
				39.02,148.771 			"/>
			<g>
				<rect x="96.8" y="108.349" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="108.349" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="108.349" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="108.349" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="108.349" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="123.615" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="123.615" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="123.615" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="123.615" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="123.615" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="138.881" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="138.881" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="138.881" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="138.881" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="138.881" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="154.147" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="154.147" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="154.147" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="154.147" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="154.147" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="169.413" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="169.413" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="169.413" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="169.413" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="169.413" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="184.679" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="184.679" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="184.679" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="184.679" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="184.679" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<g>
					<rect x="68.403" y="184.679" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="184.679" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="184.679" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="68.403" y="199.515" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="199.515" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="199.515" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="68.403" y="214.351" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="214.351" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="214.351" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="68.403" y="229.187" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="229.187" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="229.187" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="68.403" y="244.023" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="244.023" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="244.023" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="68.403" y="258.859" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="258.859" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="258.859" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="68.403" y="273.695" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="273.695" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="273.695" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="68.403" y="288.531" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="288.531" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="288.531" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="68.403" y="303.367" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="303.367" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="303.367" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="68.403" y="318.203" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="318.203" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="318.203" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="68.403" y="333.039" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="76.733" y="333.039" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					<rect x="85.062" y="333.039" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
			</g>
			<g>
				<g>
					<rect x="142.67" y="245.743" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					<rect x="151" y="245.743" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="142.67" y="260.579" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					<rect x="151" y="260.579" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="142.67" y="275.415" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					<rect x="151" y="275.415" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="142.67" y="290.251" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					<rect x="151" y="290.251" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="142.67" y="305.087" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					<rect x="151" y="305.087" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="142.67" y="319.923" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					<rect x="151" y="319.923" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
				<g>
					<rect x="142.67" y="334.76" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					<rect x="151" y="334.76" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				</g>
			</g>
			<g>
				<rect x="96.8" y="199.945" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="199.945" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="199.945" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="199.945" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="199.945" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="215.211" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="215.211" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="215.211" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="215.211" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="215.211" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="230.477" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="230.477" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="230.477" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="230.477" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="230.477" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="245.743" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="245.743" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="245.743" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="245.743" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="245.743" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="261.009" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="261.009" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="261.009" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="261.009" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="261.009" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="276.276" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="276.276" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="276.276" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="276.276" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="276.276" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="291.541" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="291.541" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="291.541" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="291.541" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="291.541" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="306.807" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="306.807" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="306.807" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="306.807" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="306.807" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="322.074" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="322.074" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="322.074" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="322.074" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="322.074" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<g>
				<rect x="96.8" y="337.77" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="105.13" y="337.77" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="113.459" y="337.77" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				<rect x="121.789" y="337.77" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				<rect x="130.118" y="337.77" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
			</g>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="93.318" y1="178.013" x2="93.318" y2="362.066"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="164.067" y1="186.614" x2="164.067" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="173.122" y1="186.614" x2="173.122" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="182.177" y1="186.614" x2="182.177" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="191.232" y1="186.614" x2="191.232" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="200.287" y1="203.815" x2="200.287" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="209.342" y1="203.815" x2="209.342" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="218.397" y1="203.815" x2="218.397" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="145.331" x2="32.984" y2="145.331"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="156.512" x2="32.984" y2="156.512"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="167.693" x2="32.984" y2="167.693"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="178.874" x2="32.984" y2="178.874"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="190.054" x2="32.984" y2="190.054"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="201.235" x2="32.984" y2="201.235"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="212.416" x2="32.984" y2="212.416"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="223.597" x2="32.984" y2="223.597"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="234.777" x2="32.984" y2="234.777"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="245.958" x2="32.984" y2="245.958"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="257.139" x2="32.984" y2="257.139"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="268.32" x2="32.984" y2="268.32"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="279.501" x2="32.984" y2="279.501"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="290.681" x2="32.984" y2="290.681"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="301.862" x2="32.984" y2="301.862"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="313.043" x2="32.984" y2="313.043"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="324.224" x2="32.984" y2="324.224"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="335.405" x2="32.984" y2="335.405"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="346.585" x2="32.984" y2="346.585"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="357.766" x2="32.984" y2="357.766"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="20.5" y1="368.947" x2="32.984" y2="368.947"/>
			<rect x="297.737" y="215.426" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="206.41" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="197.394" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="188.378" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.161"/>
			<rect x="297.737" y="179.363" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="170.347" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="161.331" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="152.315" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="224.457" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.161"/>
			<rect x="297.737" y="233.487" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="242.518" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="251.549" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="260.579" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="269.61" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="278.641" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.161"/>
			<rect x="297.737" y="287.671" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="296.702" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="305.732" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.161"/>
			<rect x="297.737" y="314.763" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="323.794" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="332.824" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="341.855" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="350.886" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.16"/>
			<rect x="297.737" y="359.916" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="54.33" height="5.161"/>
			<rect x="247.287" y="316.053" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="27.165" height="14.191"/>
			<rect x="253.755" y="291.111" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.229" height="24.942"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="139.348" y1="238.519" x2="139.348" y2="361.725"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="295.378" y1="225.015" x2="229.414" y2="225.015"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="295.378" y1="232.365" x2="229.414" y2="232.365"/>
			<g>
				<rect x="232.362" y="237.878" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="244.771" y="237.878" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
				<rect x="257.181" y="237.878" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="269.59" y="237.878" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="281.999" y="237.878" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
			</g>
			<g>
				<rect x="232.362" y="249.271" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="244.771" y="249.271" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
				<rect x="257.181" y="249.271" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="269.59" y="249.271" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="281.999" y="249.271" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
			</g>
			<g>
				<rect x="232.362" y="260.664" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="244.771" y="260.664" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
				<rect x="257.181" y="260.664" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="269.59" y="260.664" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="281.999" y="260.664" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
			</g>
			<g>
				<rect x="232.362" y="281.245" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="244.771" y="281.245" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
				<rect x="257.181" y="281.245" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="269.59" y="281.245" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				<rect x="281.999" y="281.245" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
			</g>
		</g>
		<g>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="719.289,135.486 719.289,236.479 705.992,236.479 
				705.992,361.954 690.649,361.954 690.649,233.696 662.008,233.696 662.008,98.762 615.978,98.762 615.978,238.519 
				598.589,238.519 598.589,182.922 559.719,182.922 559.719,195.164 526.475,195.164 526.475,360.424 513.178,360.424 
				513.178,330.33 476.865,330.33 476.865,359.914 459.476,359.914 459.476,210.976 400.66,210.976 400.66,350.733 383.782,350.733 
							"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="728.156,135.486 719.289,135.486 719.289,128.856 
				729.045,128.856 			"/>
			
				<rect x="662.008" y="233.696" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1352.8312 470.7496)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="28.815" height="3.357"/>
			
				<rect x="615.978" y="98.762" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1278.1218 200.7895)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="46.166" height="3.266"/>
			
				<rect x="588.205" y="175.99" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1183.1318 358.9075)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="6.722" height="6.927"/>
			
				<rect x="570.729" y="178.895" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1144.8181 361.8125)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="3.361" height="4.022"/>
			
				<rect x="526.365" y="195.164" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1086.0844 394.3931)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="33.354" height="4.065"/>
			
				<rect x="400.669" y="207.944" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.1454 418.9196)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="58.807" height="3.032"/>
			
				<rect x="421.058" y="201.911" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 865.4187 409.8546)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="23.302" height="6.033"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="425.988,202.134 425.988,196.548 408.959,196.548 
				408.959,207.721 			"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="538.222,193.495 538.222,148.341 484.323,148.341 
				484.323,185.754 465.781,185.754 465.781,122.109 406.276,122.109 406.276,199.085 390.296,199.085 			"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="362.753,278.892 362.753,176.919 390.296,176.919 
				390.296,274.377 397.087,274.377 			"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="577.029,171.133 577.029,121.249 613.681,121.249 			
				"/>
			<polyline style="fill:none;stroke:#000000;stroke-miterlimit:10;" points="716.306,148.771 704.663,148.771 704.663,204.985 
				692.59,204.985 692.59,172.814 685.131,172.814 685.131,198.906 679.68,198.906 679.68,161.217 672.413,161.217 672.413,198.182 
				664.782,198.182 			"/>
			<g>
				
					<rect x="653.898" y="108.349" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 225.7276)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="108.349" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 225.7276)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="108.349" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 225.7276)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="108.349" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 225.7276)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="108.349" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 225.7276)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="123.615" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 256.2597)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="123.615" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 256.2597)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="123.615" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 256.2597)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="123.615" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 256.2597)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="123.615" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 256.2597)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="138.881" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 286.792)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="138.881" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 286.792)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="138.881" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 286.792)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="138.881" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 286.792)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="138.881" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 286.792)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="154.147" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 317.3241)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="154.147" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 317.3241)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="154.147" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 317.3241)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="154.147" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 317.3241)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="154.147" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 317.3241)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="169.413" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 347.8563)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="169.413" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 347.8563)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="169.413" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 347.8563)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="169.413" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 347.8563)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="169.413" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 347.8563)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="184.679" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 378.3885)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="184.679" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 378.3885)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="184.679" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 378.3885)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="184.679" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 378.3885)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="184.679" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 378.3885)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				<g>
					
						<rect x="682.295" y="244.023" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1369.2173 497.0769)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					
						<rect x="673.966" y="244.023" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1352.5583 497.0769)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					
						<rect x="665.636" y="244.023" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1335.8995 497.0769)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				</g>
				<g>
					
						<rect x="682.295" y="258.859" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1369.2173 526.7489)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					
						<rect x="673.966" y="258.859" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1352.5583 526.7489)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					
						<rect x="665.636" y="258.859" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1335.8995 526.7489)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				</g>
				<g>
					
						<rect x="682.295" y="273.695" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1369.2173 556.4211)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					
						<rect x="673.966" y="273.695" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1352.5583 556.4211)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					
						<rect x="665.636" y="273.695" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1335.8995 556.4211)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				</g>
				<g>
					
						<rect x="682.295" y="288.531" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1369.2173 586.0932)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					
						<rect x="673.966" y="288.531" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1352.5583 586.0932)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					
						<rect x="665.636" y="288.531" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1335.8995 586.0932)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				</g>
				<g>
					
						<rect x="682.295" y="303.367" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1369.2173 615.7653)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					
						<rect x="673.966" y="303.367" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1352.5583 615.7653)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					
						<rect x="665.636" y="303.367" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1335.8995 615.7653)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				</g>
				<g>
					
						<rect x="682.295" y="318.203" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1369.2173 645.4374)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					
						<rect x="673.966" y="318.203" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1352.5583 645.4374)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					
						<rect x="665.636" y="318.203" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1335.8995 645.4374)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				</g>
				<g>
					
						<rect x="682.295" y="333.039" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1369.2173 675.1095)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
					
						<rect x="673.966" y="333.039" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1352.5583 675.1095)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
					
						<rect x="665.636" y="333.039" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1335.8995 675.1095)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				</g>
			</g>
			<g>
				
					<rect x="653.898" y="199.945" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 408.9207)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="199.945" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 408.9207)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="199.945" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 408.9207)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="199.945" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 408.9207)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="199.945" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 408.9207)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="215.211" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 439.4527)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="215.211" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 439.4527)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="215.211" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 439.4527)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="215.211" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 439.4527)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="215.211" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 439.4527)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="230.477" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 469.9849)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="230.477" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 469.9849)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="230.477" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 469.9849)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="230.477" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 469.9849)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="230.477" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 469.9849)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="245.743" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 500.5171)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="245.743" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 500.5171)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="245.743" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 500.5171)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="245.743" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 500.5171)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="245.743" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 500.5171)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="261.009" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 531.0491)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="261.009" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 531.0491)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="261.009" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 531.0491)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="261.009" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 531.0491)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="261.009" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 531.0491)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="276.276" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 561.5815)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="276.276" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 561.5815)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="276.276" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 561.5815)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="276.276" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 561.5815)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="276.276" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 561.5815)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="291.541" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 592.1135)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="291.541" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 592.1135)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="291.541" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 592.1135)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="291.541" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 592.1135)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="291.541" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 592.1135)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="306.807" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 622.6457)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="306.807" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 622.6457)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="306.807" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 622.6457)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="306.807" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 622.6457)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="306.807" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 622.6457)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="322.074" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 653.1779)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="322.074" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 653.1779)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="322.074" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 653.1779)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="322.074" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 653.1779)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="322.074" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 653.1779)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<g>
				
					<rect x="653.898" y="337.77" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1312.4232 684.5701)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.627" height="9.031"/>
				
					<rect x="645.568" y="337.77" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1295.7644 684.5701)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="637.239" y="337.77" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1279.1055 684.5701)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="628.91" y="337.77" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1262.4467 684.5701)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
				
					<rect x="620.58" y="337.77" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1245.7878 684.5701)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="4.628" height="9.031"/>
			</g>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="662.008" y1="178.013" x2="662.008" y2="362.066"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="591.259" y1="186.614" x2="591.259" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="582.204" y1="186.614" x2="582.204" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="573.149" y1="186.614" x2="573.149" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="564.094" y1="186.614" x2="564.094" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="555.039" y1="203.815" x2="555.039" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="545.984" y1="203.815" x2="545.984" y2="359.486"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="536.928" y1="203.815" x2="536.928" y2="359.486"/>
			
				<rect x="403.258" y="215.426" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 436.0126)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="224.457" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 454.0739)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="233.487" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 472.1352)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="242.518" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 490.1964)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="251.549" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 508.2577)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="260.579" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 526.3191)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="269.61" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 544.3805)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="278.641" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 562.4416)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="287.671" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 580.5029)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="296.702" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 598.5642)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="305.733" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 616.6255)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="314.763" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 634.6867)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.161"/>
			
				<rect x="403.258" y="323.794" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 652.7479)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="332.824" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 670.8092)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="341.855" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 688.8705)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="350.886" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 706.9318)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="403.258" y="359.916" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 821.3405 724.9932)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="215.426" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 436.0126)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="224.457" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 454.0739)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="233.487" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 472.1352)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="242.518" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 490.1964)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="251.549" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 508.2577)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="260.579" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 526.3191)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="269.61" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 544.3805)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="278.641" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 562.4416)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="287.671" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 580.5029)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="296.702" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 598.5642)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="305.733" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 616.6255)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="314.763" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 634.6867)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.161"/>
			
				<rect x="422.979" y="323.794" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 652.7479)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="332.824" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 670.8092)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="341.855" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 688.8705)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="350.886" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 706.9318)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="422.979" y="359.916" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 860.7827 724.9932)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="215.426" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 436.0126)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="224.457" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 454.0739)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="233.487" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 472.1352)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="242.518" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 490.1964)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="251.549" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 508.2577)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="260.579" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 526.3191)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="269.61" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 544.3805)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="278.641" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 562.4416)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="287.671" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 580.5029)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="296.702" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 598.5642)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="305.733" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 616.6255)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="314.763" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 634.6867)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.161"/>
			
				<rect x="442.7" y="323.794" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 652.7479)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="332.824" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 670.8092)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="341.855" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 688.8705)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="350.886" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 706.9318)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="442.7" y="359.916" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 900.2251 724.9932)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.825" height="5.16"/>
			
				<rect x="480.873" y="316.053" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 988.9113 646.2974)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="27.165" height="14.191"/>
			
				<rect x="487.341" y="291.111" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 988.9114 607.1646)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="14.229" height="24.942"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="615.978" y1="238.519" x2="615.978" y2="361.725"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="459.948" y1="225.015" x2="525.912" y2="225.015"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="459.948" y1="232.365" x2="525.912" y2="232.365"/>
			<g>
				
					<rect x="512.902" y="237.878" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1035.8655 483.1064)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="500.493" y="237.878" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1011.0468 483.1064)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="488.083" y="237.878" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 986.2283 483.1064)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
				
					<rect x="475.674" y="237.878" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 961.4099 483.1064)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="463.265" y="237.878" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 936.5912 483.1064)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
			</g>
			<g>
				
					<rect x="512.902" y="249.271" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1035.8655 505.8923)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="500.493" y="249.271" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1011.0468 505.8923)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="488.083" y="249.271" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 986.2283 505.8923)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
				
					<rect x="475.674" y="249.271" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 961.4099 505.8923)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="463.265" y="249.271" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 936.5912 505.8923)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
			</g>
			<g>
				
					<rect x="512.902" y="260.664" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1035.8655 528.6782)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="500.493" y="260.664" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1011.0468 528.6782)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="488.083" y="260.664" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 986.2283 528.6782)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
				
					<rect x="475.674" y="260.664" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 961.4099 528.6782)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="463.265" y="260.664" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 936.5912 528.6782)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
			</g>
			<g>
				
					<rect x="512.902" y="281.245" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1035.8655 569.8399)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="500.493" y="281.245" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 1011.0468 569.8399)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="488.083" y="281.245" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 986.2283 569.8399)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.061" height="7.35"/>
				
					<rect x="475.674" y="281.245" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 961.4099 569.8399)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
				
					<rect x="463.265" y="281.245" transform="matrix(-1 -1.224647e-16 1.224647e-16 -1 936.5912 569.8399)" style="fill:none;stroke:#000000;stroke-miterlimit:10;" width="10.062" height="7.35"/>
			</g>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="211.411" x2="596.189" y2="211.411"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="224.318" x2="596.189" y2="224.318"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="237.225" x2="596.189" y2="237.225"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="250.133" x2="596.189" y2="250.133"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="263.04" x2="596.189" y2="263.04"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="275.947" x2="596.189" y2="275.947"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="288.854" x2="596.189" y2="288.854"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="301.761" x2="596.189" y2="301.761"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="314.668" x2="596.189" y2="314.668"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="327.575" x2="596.189" y2="327.575"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="340.482" x2="596.189" y2="340.482"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="529.938" y1="353.389" x2="596.189" y2="353.389"/>
			<line style="fill:none;stroke:#000000;stroke-miterlimit:10;" x1="563.526" y1="199.119" x2="596.189" y2="199.119"/>
		</g>
	</g>
</g>
<g id="Clouds">
	<path style="opacity:0.3;fill:none;stroke:#000000;stroke-miterlimit:10;" d="M570.913,50.617
		c-2.017-2.281-3.207-5.618-6.257-9.204c-7.051-8.289-19.801-10.694-28.953-4.908c-7.341,4.641-10.612,14.135-18.203,18.276
		c-9.085,4.955-15.692-4.13-28.907-2.478c-13.215,1.652-15.692,9.085-18.996,11.563c-3.304,2.478-10.737-4.13-16.519-4.13
		c-5.781,0-16.518,9.085-16.518,9.085h187.485c0,0-9.898-16.497-23.924-18.991c-6.983-1.242-12.966,3.434-19.703,3.782
		C575.243,53.879,572.739,52.681,570.913,50.617z"/>
	<path style="opacity:0.3;fill:none;stroke:#000000;stroke-miterlimit:10;" d="M362.645,56.818c-1.33-1.504-2.114-3.704-4.125-6.069
		c-4.649-5.465-13.056-7.051-19.09-3.236c-4.84,3.06-6.997,9.32-12.002,12.05c-5.99,3.267-10.347-2.723-19.06-1.634
		c-8.713,1.089-10.347,5.99-12.525,7.624c-2.178,1.634-7.079-2.723-10.891-2.723c-3.812,0-10.891,5.99-10.891,5.99h123.616
		c0,0-6.526-10.877-15.774-12.522c-4.604-0.819-8.549,2.264-12.991,2.494C365.5,58.969,363.849,58.179,362.645,56.818z"/>
</g>
<g id="Device">
	<g>
		<g>
			<g>
				<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M314.323,98.384v304.3c0,16.86-13.67,30.52-30.52,30.52
					h-144.74c-16.85,0-30.52-13.66-30.52-30.52v-304.3c0-16.85,13.67-30.51,30.52-30.51h60.52l0.06,0.22
					c1.79,6.44,5.91,10.63,10.48,10.63h2.62c4.57,0,8.69-4.19,10.48-10.63l0.07-0.22h60.51
					C300.653,67.874,314.323,81.534,314.323,98.384z"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M314.149,95.14c-1.621-15.324-14.593-27.266-30.346-27.266
					h-60.51l-0.07,0.22c-1.79,6.44-5.91,10.63-10.48,10.63h-2.62c-4.57,0-8.69-4.19-10.48-10.63l-0.06-0.22h-60.52
					c-15.754,0-28.725,11.941-30.346,27.266H314.149z"/>
				<g>
					<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M234.728,290.75v-96.287h44.202
						c0.782,0,1.493-0.456,1.818-1.168l33.571-73.4v-9.616l-36.673,80.183h-42.918V95.149h-4v22.016
						c-39.919-3.939-63.304,14.882-75.935,31.563c-10.903,14.398-15.751,29.317-17.408,35.435l-28.839-18.31v4.738l51.649,32.793
						h-0.033v58.504l-51.616-9.414v4.066l122.181,22.284v10.964l-122.181,97.6v5.119l55.256-44.139l42.114,56.978
						c0.383,0.518,0.983,0.811,1.608,0.811c0.165,0,0.332-0.021,0.496-0.063l66.389-17.002v44.11h4v-82.809
						c0-0.754-0.424-1.444-1.098-1.784l-14.911-7.544l25.483-27.799l26.434,10.167v-4.285l-26.27-10.104
						c-0.765-0.295-1.637-0.092-2.193,0.515l-27.163,29.631l-51.064-25.832l26.346-21.045
						C234.452,291.934,234.728,291.358,234.728,290.75z M274.411,351.623v33.34l-66.077,16.922l-41.404-56.016l37.264-29.767
						L274.411,351.623z M230.728,274.758l-66.565-12.141v-56.714l66.565,42.264V274.758z M230.728,243.428l-89.791-57.01
						c1.201-4.71,5.819-20.449,17.045-35.275c17.463-23.063,41.936-33.133,72.746-29.964V243.428z"/>
					<g>
						<polygon style="fill:#92E3A9;" points="314.323,249.584 308.333,249.584 293.213,287.394 262.973,287.394 278.933,250.424 
							255.413,250.424 255.413,214.304 290.693,214.304 314.323,168.434 						"/>
						<rect x="116.283" y="103.2" style="fill:#92E3A9;" width="30" height="25"/>
						<rect x="151.283" y="103.2" style="fill:#92E3A9;" width="21.6" height="18"/>
						<path style="fill:#92E3A9;" d="M149.967,182.822l74.242,47.948V130.234C224.209,130.234,171.105,125.078,149.967,182.822z"/>
						<rect x="179.283" y="103.2" style="fill:#92E3A9;" width="14" height="11.667"/>
						<polygon style="fill:#92E3A9;" points="219.283,338.2 192.283,357.2 207.283,385.2 261.283,382.2 264.283,356.2 
							243.283,347.2 234.283,361.2 225.283,356.2 233.283,341.2 						"/>
						<g>
							<polygon style="fill:#92E3A9;" points="132.763,281.794 132.763,335.814 108.543,348.134 108.543,280.654 							"/>
							<polygon style="fill:#92E3A9;" points="173.084,315.321 173.084,283.688 139.482,282.113 139.482,332.402 							"/>
							<polygon style="fill:#92E3A9;" points="178.124,283.924 178.124,312.759 198.286,302.51 198.286,284.869 							"/>
						</g>
					</g>
				</g>
			</g>
			<g>
				<path style="fill:#707070;stroke:#263238;stroke-linejoin:round;stroke-miterlimit:10;" d="M327.409,179.591V140.92h-3.498
					V91.412c0-16.854-13.663-30.517-30.517-30.517h-163.92c-16.854,0-30.517,13.663-30.517,30.517v333.072
					c0,16.854,13.663,30.517,30.517,30.517h163.92c16.854,0,30.517-13.663,30.517-30.517V212.549h3.498v-22.851h-3.498v-10.107
					H327.409z M314.323,402.684c0,16.86-13.67,30.52-30.52,30.52h-144.74c-16.85,0-30.52-13.66-30.52-30.52v-304.3
					c0-16.85,13.67-30.51,30.52-30.51h60.52l0.06,0.22c1.79,6.44,5.91,10.63,10.48,10.63h2.62c4.57,0,8.69-4.19,10.48-10.63
					l0.07-0.22h60.51c16.85,0,30.52,13.66,30.52,30.51V402.684z"/>
				<path style="fill:#263238;stroke:#263238;stroke-linejoin:round;stroke-miterlimit:10;" d="M289.86,65.099H133.006
					c-16.854,0-30.517,13.663-30.517,30.517V420.28c0,16.854,13.663,30.517,30.517,30.517H289.86
					c16.854,0,30.517-13.663,30.517-30.517V95.616C320.377,78.761,306.714,65.099,289.86,65.099z M314.323,402.684
					c0,16.86-13.67,30.52-30.52,30.52h-144.74c-16.85,0-30.52-13.66-30.52-30.52v-304.3c0-16.85,13.67-30.51,30.52-30.51h60.52
					l0.06,0.22c1.79,6.44,5.91,10.63,10.48,10.63h2.62c4.57,0,8.69-4.19,10.48-10.63l0.07-0.22h60.51
					c16.85,0,30.52,13.66,30.52,30.51V402.684z"/>
				<circle style="fill:#FFFFFF;" cx="210.805" cy="69.333" r="2.865"/>
				<g>
					<g>
						<path style="fill:#263238;" d="M126.833,80.311c0,2.899-3.255,3.943-3.269,6.154v0.151h3.159v0.976h-4.216v-0.824
							c0-3.104,3.256-3.709,3.256-6.415c0-0.989-0.33-1.511-1.127-1.511c-0.783,0-1.127,0.563-1.127,1.414v0.852h-1.002v-0.783
							c0-1.47,0.673-2.445,2.156-2.445C126.16,77.88,126.833,78.842,126.833,80.311z"/>
						<path style="fill:#263238;" d="M132.096,80.298v0.247c0,0.989-0.397,1.69-1.167,1.978c0.81,0.302,1.167,1.03,1.167,1.992
							v0.742c0,1.47-0.686,2.431-2.17,2.431c-1.469,0-2.156-0.962-2.156-2.431v-0.66h1.002v0.728c0,0.866,0.357,1.387,1.127,1.387
							c0.783,0,1.127-0.508,1.127-1.483v-0.742c0-0.948-0.399-1.401-1.21-1.428h-0.577v-0.962h0.632
							c0.714-0.027,1.155-0.48,1.155-1.318v-0.44c0-0.989-0.344-1.497-1.127-1.497c-0.769,0-1.127,0.535-1.127,1.401v0.494h-1.002
							v-0.44c0-1.47,0.687-2.418,2.156-2.418C131.409,77.88,132.096,78.828,132.096,80.298z"/>
						<path style="fill:#263238;" d="M134.172,81.026v1.333h-1.03v-1.333H134.172z M134.172,86.259v1.333h-1.03v-1.333H134.172z"/>
						<path style="fill:#263238;" d="M136.342,81.892c0.302-0.522,0.783-0.797,1.429-0.797c1.236,0,1.785,0.906,1.785,2.28v1.882
							c0,1.47-0.7,2.431-2.169,2.431c-1.471,0-2.157-0.962-2.157-2.431v-0.646h1.002v0.714c0,0.866,0.357,1.387,1.127,1.387
							c0.769,0,1.127-0.522,1.127-1.387v-1.868c0-0.865-0.357-1.388-1.127-1.388c-0.59,0-0.989,0.33-1.085,1.03v0.22h-1.002
							l0.261-5.344h3.791v0.961h-2.83L136.342,81.892z"/>
						<path style="fill:#263238;" d="M144.902,80.284v0.179H143.9v-0.247c0-0.866-0.357-1.374-1.14-1.374
							c-0.811,0-1.168,0.522-1.168,1.552v2.156c0.261-0.659,0.783-1.03,1.552-1.03c1.236,0,1.786,0.893,1.786,2.28v1.456
							c0,1.47-0.729,2.431-2.198,2.431c-1.484,0-2.212-0.962-2.212-2.431v-4.904c0-1.525,0.688-2.473,2.212-2.473
							C144.215,77.88,144.902,78.815,144.902,80.284z M141.592,83.883v1.442c0,0.866,0.357,1.387,1.14,1.387
							c0.769,0,1.14-0.522,1.14-1.387v-1.442c0-0.866-0.371-1.388-1.14-1.388C141.949,82.496,141.592,83.018,141.592,83.883z"/>
					</g>
					<g>
						<rect x="247.16" y="80.707" style="fill:#263238;" width="2.715" height="6.787"/>
						<rect x="250.78" y="79.347" style="fill:#263238;" width="2.715" height="8.147"/>
						<rect x="254.4" y="78.382" style="fill:#263238;" width="2.715" height="9.113"/>
						<rect x="258.02" y="76.703" style="fill:#263238;" width="2.715" height="10.792"/>
						<g>
							<g>
								<path style="fill:#263238;" d="M294.333,87.308h-16.29v-9.955h16.29V87.308z M278.948,86.403h14.48v-8.145h-14.48V86.403z"
									/>
							</g>
							<g>
								<rect x="280.305" y="79.243" style="fill:#263238;" width="8.598" height="6.175"/>
							</g>
							<g>
								<rect x="293.767" y="81.158" style="fill:#263238;" width="2.049" height="2.345"/>
							</g>
						</g>
						<g>
							<path style="fill:#263238;" d="M272.07,85.142c-0.106,0-0.214-0.027-0.311-0.086c-3.315-1.97-5.506-0.141-5.598-0.064
								c-0.253,0.22-0.637,0.191-0.857-0.061c-0.22-0.253-0.196-0.633,0.056-0.854c0.118-0.103,2.919-2.504,7.021-0.066
								c0.289,0.171,0.384,0.545,0.211,0.834C272.479,85.037,272.277,85.142,272.07,85.142z"/>
						</g>
						<g>
							<path style="fill:#263238;" d="M273.296,83.097c-0.106,0-0.214-0.027-0.311-0.085c-4.793-2.851-8.015-0.093-8.048-0.067
								c-0.252,0.223-0.636,0.197-0.858-0.055c-0.222-0.252-0.198-0.637,0.055-0.858c0.039-0.036,3.925-3.364,9.474-0.067
								c0.289,0.171,0.384,0.545,0.211,0.834C273.705,82.992,273.503,83.097,273.296,83.097z"/>
						</g>
						<g>
							<path style="fill:#263238;" d="M274.41,80.729c-0.106,0-0.214-0.027-0.311-0.086c-6.071-3.61-10.107-0.212-10.276-0.064
								c-0.253,0.22-0.638,0.193-0.858-0.059c-0.221-0.252-0.196-0.635,0.056-0.855c0.048-0.043,4.843-4.145,11.7-0.067
								c0.289,0.171,0.384,0.545,0.211,0.834C274.818,80.623,274.616,80.729,274.41,80.729z"/>
						</g>
						<g>
							<circle style="fill:#263238;" cx="268.712" cy="86.162" r="1.526"/>
						</g>
					</g>
				</g>
			</g>
		</g>
		<g>
			<g>
				<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M288.834,116.555
					c0-2.003-1.624-3.627-3.627-3.627c-2.003,0-3.627,1.624-3.627,3.627c0,2.003,1.624,3.627,3.627,3.627
					C287.21,120.182,288.834,118.559,288.834,116.555z"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M285.207,114.045
					c-1.386,0-2.51,1.124-2.51,2.51s1.124,2.51,2.51,2.51s2.51-1.124,2.51-2.51S286.593,114.045,285.207,114.045z"/>
				<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M285.207,114.647
					c-1.054,0-1.908,0.854-1.908,1.908c0,1.054,0.854,1.908,1.908,1.908c1.054,0,1.908-0.854,1.908-1.908
					C287.115,115.502,286.26,114.647,285.207,114.647z"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M287.707,116.362
					c-0.037-0.48-0.208-0.922-0.478-1.289c-0.058,0.11-0.461,0.815-1.322,0.763c-0.786-0.048-0.25-1.306-0.06-1.705
					c-0.205-0.054-0.419-0.086-0.641-0.086c-0.243,0-0.477,0.036-0.7,0.101c0.151,0.26,0.383,0.765,0.254,1.251
					c-0.169,0.637-1.018,0.425-1.693-0.151c-0.222,0.363-0.355,0.785-0.368,1.238c0.497-0.029,1.366-0.014,1.466,0.427
					c0.112,0.494-0.408,0.898-0.995,1.108c0.273,0.379,0.648,0.679,1.086,0.858c0.106-0.452,0.363-1.219,0.9-1.259
					c0.549-0.041,0.784,0.819,0.873,1.305c0.457-0.159,0.854-0.445,1.147-0.818c-0.374-0.142-1.325-0.579-1.016-1.265
					C286.433,116.238,287.254,116.285,287.707,116.362z"/>
			</g>
			<g>
				<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M259.479,116.555
					c0-2.003-1.624-3.627-3.627-3.627c-2.003,0-3.627,1.624-3.627,3.627c0,2.003,1.624,3.627,3.627,3.627
					C257.855,120.182,259.479,118.559,259.479,116.555z"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M255.852,114.045
					c-1.386,0-2.51,1.124-2.51,2.51s1.124,2.51,2.51,2.51c1.386,0,2.51-1.124,2.51-2.51S257.238,114.045,255.852,114.045z"/>
				<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M255.852,114.647
					c-1.054,0-1.908,0.854-1.908,1.908c0,1.054,0.854,1.908,1.908,1.908c1.054,0,1.908-0.854,1.908-1.908
					C257.76,115.502,256.905,114.647,255.852,114.647z"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M258.352,116.362
					c-0.037-0.48-0.208-0.922-0.478-1.289c-0.057,0.11-0.461,0.815-1.322,0.763c-0.786-0.048-0.25-1.306-0.06-1.705
					c-0.205-0.054-0.419-0.086-0.641-0.086c-0.243,0-0.477,0.036-0.7,0.101c0.151,0.26,0.383,0.765,0.254,1.251
					c-0.169,0.637-1.018,0.425-1.693-0.151c-0.222,0.363-0.355,0.785-0.368,1.238c0.497-0.029,1.366-0.014,1.466,0.427
					c0.112,0.494-0.408,0.898-0.995,1.108c0.273,0.379,0.648,0.679,1.086,0.858c0.106-0.452,0.363-1.219,0.9-1.259
					c0.55-0.041,0.785,0.819,0.873,1.305c0.457-0.159,0.854-0.445,1.147-0.818c-0.374-0.142-1.325-0.579-1.016-1.265
					C257.078,116.238,257.899,116.285,258.352,116.362z"/>
			</g>
			<path style="fill:#263238;" d="M292.778,116.56l-0.124-1.612c0,0,0.149-0.174,0.149-0.447c0-0.273-0.298-1.488-0.942-2.604
				c-0.645-1.116-1.513-1.662-2.678-2.232c-1.166-0.57-5.283-1.19-5.283-1.19s-0.01-0.007-0.028-0.018
				c-0.007-0.004-0.014-0.009-0.024-0.015c-0.014-0.009-0.033-0.021-0.053-0.034c-0.01-0.006-0.021-0.013-0.032-0.02
				c-0.014-0.009-0.029-0.019-0.044-0.029c-0.021-0.013-0.043-0.028-0.067-0.043c-0.022-0.014-0.046-0.029-0.071-0.045
				c-0.011-0.007-0.022-0.014-0.034-0.021c-0.084-0.054-0.181-0.116-0.29-0.186c-0.024-0.016-0.049-0.032-0.075-0.048
				c-0.017-0.011-0.034-0.021-0.051-0.033c-0.07-0.045-0.144-0.092-0.222-0.142c-0.031-0.02-0.064-0.041-0.097-0.062
				c-0.009-0.006-0.018-0.012-0.028-0.018c-0.111-0.071-0.228-0.146-0.351-0.224c-0.077-0.049-0.157-0.1-0.238-0.152
				c-0.056-0.036-0.113-0.072-0.171-0.109c-0.033-0.021-0.066-0.043-0.1-0.064c-0.269-0.172-0.555-0.354-0.852-0.543
				c-0.089-0.057-0.18-0.115-0.271-0.173c-0.02-0.013-0.041-0.026-0.061-0.039c-0.037-0.024-0.075-0.048-0.112-0.072
				c-0.021-0.013-0.042-0.027-0.063-0.04c-0.005-0.003-0.01-0.006-0.014-0.009c-0.204-0.13-0.41-0.261-0.618-0.394
				c-0.027-0.018-0.055-0.035-0.082-0.052c-0.101-0.064-0.202-0.128-0.302-0.192c-0.006-0.004-0.012-0.008-0.018-0.012
				c-0.327-0.208-0.653-0.415-0.972-0.618c-0.024-0.015-0.048-0.03-0.072-0.046c-0.039-0.025-0.078-0.05-0.117-0.075
				c-0.012-0.008-0.025-0.016-0.037-0.024c-0.268-0.17-0.529-0.335-0.779-0.493c-0.002-0.001-0.004-0.003-0.006-0.004
				c-0.049-0.031-0.097-0.061-0.145-0.092c-0.003-0.002-0.006-0.004-0.008-0.005c-0.045-0.029-0.09-0.057-0.135-0.085
				c-0.008-0.005-0.015-0.01-0.023-0.014c-0.046-0.029-0.091-0.058-0.136-0.086c0,0-0.001-0.001-0.001-0.001
				c-0.047-0.03-0.093-0.059-0.139-0.088c-0.004-0.002-0.007-0.004-0.011-0.007c-0.043-0.027-0.086-0.054-0.128-0.081
				c-0.007-0.005-0.015-0.009-0.022-0.014c-0.121-0.076-0.236-0.149-0.346-0.218c-0.01-0.007-0.021-0.013-0.031-0.02
				c-0.032-0.02-0.064-0.04-0.095-0.06c-0.009-0.006-0.019-0.012-0.028-0.018c-0.03-0.019-0.059-0.037-0.088-0.055
				c-0.012-0.007-0.023-0.015-0.035-0.022c-0.027-0.017-0.053-0.033-0.079-0.05c-0.013-0.008-0.027-0.017-0.04-0.025
				c-0.024-0.015-0.047-0.029-0.07-0.043c-0.013-0.008-0.027-0.017-0.04-0.025c-0.023-0.015-0.047-0.029-0.069-0.043
				c-0.007-0.004-0.014-0.008-0.021-0.013c-0.314-0.195-0.531-0.327-0.611-0.37c-0.595-0.322-1.19-0.397-1.414-0.422
				s-4.167-0.496-7.713-0.422c-3.547,0.074-7.093,0.223-10.441,0.57c0,0-1.017-0.074-1.017,0.174c0,0.248,0.124,0.322,0.124,0.322
				l-2.629,3.943c0,0-0.719,0.546-0.868,0.843c-0.034,0.067-0.093,0.259-0.166,0.515c-0.026,0.068-0.04,0.126-0.047,0.169
				c-0.251,0.908-0.605,2.367-0.605,2.367s-0.546,0.744-0.57,1.389c-0.025,0.645,0.174,2.257,0.174,2.257s-0.273,0.372-0.149,0.57
				c0.124,0.198,0.347,0.893,0.595,1.066s0.818,0.248,0.818,0.248l0.086,0.01c0.126-2.503,2.011-4.489,4.318-4.489
				c2.389,0,4.326,2.13,4.326,4.757c0,0.236-0.016,0.468-0.047,0.695l0.171,0.019l20.659,0.322l0.111,0.001
				c-0.037-0.249-0.056-0.505-0.056-0.765c0-2.627,1.937-4.757,4.326-4.757c2.389,0,4.326,2.13,4.326,4.757
				c0,0.294-0.026,0.581-0.072,0.86c0.635-0.012,2.58-0.067,2.85-0.295c0.322-0.273,0.471-0.57,0.496-0.744
				C292.853,116.783,292.778,116.56,292.778,116.56z M253.138,107.171h-0.727l2.542-3.826l0.441,0.154L253.138,107.171z
				 M260.462,107.236l-0.297-0.014l-0.819-0.039c-0.278-0.014-0.513-0.211-0.576-0.482l-0.657-2.875
				c-0.06-0.261,0.117-0.517,0.383-0.555c0.428-0.06,1.01-0.102,1.67-0.131c0.098-0.004,0.196-0.008,0.297-0.011
				c2.003-0.072,4.588-0.032,5.864-0.004l0.147,4.4L260.462,107.236z M268.79,107.634l-0.654-4.462l4.744,0.139
				c0.086,0.003,0.17,0.03,0.241,0.078l7.008,4.787L268.79,107.634z M281.61,108.294l-7.072-4.822h1.121l7.685,4.929L281.61,108.294
				z"/>
		</g>
		<g>
			<g>
				<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M151.694,144.81
					c0-2.003-1.624-3.627-3.627-3.627c-2.003,0-3.627,1.624-3.627,3.627c0,2.003,1.624,3.627,3.627,3.627
					C150.07,148.437,151.694,146.813,151.694,144.81z"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M148.067,142.3
					c-1.386,0-2.51,1.124-2.51,2.51c0,1.386,1.124,2.51,2.51,2.51c1.386,0,2.51-1.124,2.51-2.51
					C150.577,143.424,149.454,142.3,148.067,142.3z"/>
				<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M148.067,142.902
					c-1.054,0-1.908,0.854-1.908,1.908c0,1.054,0.854,1.908,1.908,1.908c1.054,0,1.908-0.854,1.908-1.908
					C149.975,143.756,149.121,142.902,148.067,142.902z"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M150.568,144.617
					c-0.037-0.48-0.208-0.922-0.478-1.289c-0.057,0.11-0.461,0.815-1.322,0.763c-0.786-0.048-0.25-1.306-0.06-1.705
					c-0.205-0.054-0.419-0.086-0.641-0.086c-0.243,0-0.477,0.036-0.7,0.101c0.151,0.26,0.383,0.766,0.254,1.251
					c-0.169,0.637-1.018,0.425-1.693-0.151c-0.222,0.363-0.355,0.785-0.368,1.238c0.497-0.029,1.366-0.014,1.466,0.427
					c0.112,0.494-0.408,0.898-0.995,1.108c0.273,0.379,0.648,0.679,1.086,0.858c0.106-0.452,0.363-1.219,0.9-1.258
					c0.55-0.041,0.784,0.819,0.873,1.305c0.457-0.159,0.854-0.445,1.147-0.818c-0.374-0.142-1.325-0.579-1.016-1.266
					C149.294,144.493,150.115,144.54,150.568,144.617z"/>
			</g>
			<g>
				<circle style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" cx="118.712" cy="144.81" r="3.627"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M118.712,142.3
					c-1.386,0-2.51,1.124-2.51,2.51c0,1.386,1.124,2.51,2.51,2.51c1.386,0,2.51-1.124,2.51-2.51
					C121.222,143.424,120.099,142.3,118.712,142.3z"/>
				<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M118.712,142.902
					c-1.054,0-1.908,0.854-1.908,1.908c0,1.054,0.854,1.908,1.908,1.908c1.054,0,1.908-0.854,1.908-1.908
					C120.62,143.756,119.766,142.902,118.712,142.902z"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M121.213,144.617
					c-0.037-0.48-0.208-0.922-0.478-1.289c-0.058,0.11-0.461,0.815-1.322,0.763c-0.786-0.048-0.25-1.306-0.06-1.705
					c-0.205-0.054-0.419-0.086-0.641-0.086c-0.243,0-0.477,0.036-0.7,0.101c0.151,0.26,0.383,0.766,0.254,1.251
					c-0.169,0.637-1.018,0.425-1.693-0.151c-0.222,0.363-0.355,0.785-0.368,1.238c0.497-0.029,1.366-0.014,1.466,0.427
					c0.112,0.494-0.408,0.898-0.995,1.108c0.273,0.379,0.648,0.679,1.086,0.858c0.106-0.452,0.363-1.219,0.9-1.258
					c0.55-0.041,0.785,0.819,0.873,1.305c0.457-0.159,0.854-0.445,1.147-0.818c-0.375-0.142-1.325-0.579-1.016-1.266
					C119.939,144.493,120.759,144.54,121.213,144.617z"/>
			</g>
			<path style="fill:#263238;" d="M155.639,144.814l-0.124-1.612c0,0,0.149-0.174,0.149-0.446s-0.298-1.488-0.942-2.604
				c-0.645-1.116-1.513-1.662-2.678-2.232c-1.166-0.57-5.283-1.191-5.283-1.191s-0.01-0.007-0.028-0.018
				c-0.007-0.004-0.014-0.009-0.024-0.015c-0.014-0.009-0.033-0.021-0.053-0.034c-0.01-0.006-0.021-0.013-0.032-0.02
				c-0.014-0.009-0.029-0.019-0.044-0.029c-0.021-0.013-0.043-0.028-0.067-0.043c-0.022-0.014-0.046-0.029-0.071-0.045
				c-0.011-0.007-0.022-0.014-0.034-0.021c-0.084-0.054-0.181-0.116-0.29-0.186c-0.025-0.016-0.049-0.032-0.075-0.048
				c-0.017-0.011-0.034-0.021-0.051-0.033c-0.07-0.045-0.144-0.092-0.222-0.142c-0.031-0.02-0.064-0.041-0.097-0.062
				c-0.009-0.006-0.018-0.012-0.028-0.018c-0.111-0.071-0.228-0.146-0.351-0.224c-0.077-0.049-0.157-0.1-0.238-0.152
				c-0.056-0.036-0.113-0.072-0.171-0.109c-0.033-0.021-0.066-0.042-0.1-0.064c-0.269-0.171-0.555-0.354-0.852-0.543
				c-0.089-0.057-0.18-0.115-0.271-0.173c-0.02-0.013-0.041-0.026-0.061-0.039c-0.037-0.024-0.075-0.048-0.112-0.071
				c-0.021-0.013-0.042-0.027-0.063-0.04c-0.005-0.003-0.01-0.006-0.014-0.009c-0.204-0.13-0.411-0.262-0.618-0.394
				c-0.027-0.017-0.055-0.035-0.082-0.052c-0.101-0.064-0.201-0.128-0.302-0.193c-0.006-0.004-0.012-0.008-0.018-0.012
				c-0.327-0.208-0.653-0.415-0.972-0.618c-0.024-0.015-0.048-0.031-0.072-0.046c-0.039-0.025-0.078-0.05-0.118-0.075
				c-0.012-0.008-0.025-0.016-0.037-0.024c-0.268-0.17-0.529-0.335-0.779-0.493c-0.002-0.001-0.004-0.003-0.006-0.004
				c-0.049-0.031-0.097-0.061-0.145-0.092c-0.003-0.002-0.006-0.004-0.008-0.005c-0.045-0.029-0.09-0.057-0.135-0.085
				c-0.008-0.005-0.015-0.01-0.023-0.014c-0.046-0.029-0.091-0.058-0.136-0.086c-0.001,0-0.001-0.001-0.001-0.001
				c-0.047-0.03-0.093-0.059-0.139-0.088c-0.004-0.002-0.007-0.004-0.011-0.007c-0.043-0.027-0.086-0.054-0.128-0.081
				c-0.007-0.005-0.014-0.009-0.022-0.014c-0.12-0.076-0.236-0.149-0.346-0.218c-0.01-0.007-0.021-0.013-0.031-0.02
				c-0.033-0.02-0.064-0.04-0.095-0.06c-0.009-0.006-0.019-0.012-0.028-0.018c-0.03-0.019-0.059-0.037-0.087-0.055
				c-0.012-0.007-0.024-0.015-0.035-0.022c-0.027-0.017-0.053-0.033-0.079-0.05c-0.014-0.008-0.027-0.017-0.04-0.025
				c-0.024-0.015-0.047-0.029-0.07-0.044c-0.013-0.008-0.026-0.016-0.04-0.025c-0.023-0.015-0.047-0.029-0.069-0.043
				c-0.007-0.004-0.014-0.008-0.02-0.013c-0.314-0.195-0.531-0.327-0.611-0.37c-0.595-0.322-1.191-0.397-1.414-0.422
				c-0.223-0.025-4.167-0.496-7.713-0.422c-3.547,0.074-7.093,0.223-10.441,0.57c0,0-1.017-0.074-1.017,0.174
				s0.124,0.322,0.124,0.322l-2.629,3.943c0,0-0.719,0.546-0.868,0.843c-0.033,0.067-0.093,0.259-0.166,0.515
				c-0.026,0.068-0.039,0.126-0.047,0.169c-0.251,0.909-0.605,2.367-0.605,2.367s-0.546,0.744-0.57,1.389
				c-0.025,0.645,0.174,2.257,0.174,2.257s-0.273,0.372-0.149,0.57c0.124,0.198,0.347,0.893,0.595,1.066s0.818,0.248,0.818,0.248
				l0.086,0.01c0.126-2.503,2.011-4.489,4.318-4.489c2.389,0,4.326,2.13,4.326,4.757c0,0.236-0.016,0.468-0.047,0.695l0.171,0.019
				l20.659,0.322l0.111,0.001c-0.037-0.249-0.057-0.505-0.057-0.765c0-2.627,1.937-4.757,4.326-4.757s4.326,2.13,4.326,4.757
				c0,0.294-0.025,0.581-0.072,0.86c0.635-0.012,2.58-0.067,2.85-0.295c0.322-0.273,0.471-0.57,0.496-0.744
				C155.713,145.038,155.639,144.814,155.639,144.814z M115.998,135.426h-0.727l2.542-3.826l0.441,0.154L115.998,135.426z
				 M123.323,135.491l-0.297-0.014l-0.819-0.039c-0.278-0.014-0.513-0.211-0.576-0.482l-0.657-2.875
				c-0.06-0.261,0.118-0.518,0.383-0.555c0.428-0.06,1.01-0.102,1.67-0.132c0.098-0.004,0.196-0.008,0.297-0.011
				c2.003-0.072,4.588-0.032,5.864-0.004l0.148,4.4L123.323,135.491z M131.651,135.889l-0.654-4.462l4.744,0.139
				c0.086,0.003,0.17,0.03,0.241,0.078l7.008,4.787L131.651,135.889z M144.471,136.549l-7.072-4.822h1.121l7.685,4.929
				L144.471,136.549z"/>
		</g>
		<g>
			<g>
				<circle style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" cx="169.725" cy="394.571" r="3.627"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M169.725,392.061
					c-1.386,0-2.51,1.124-2.51,2.51c0,1.386,1.124,2.51,2.51,2.51c1.386,0,2.51-1.124,2.51-2.51
					C172.235,393.185,171.112,392.061,169.725,392.061z"/>
				<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M169.725,392.663
					c-1.054,0-1.908,0.854-1.908,1.908c0,1.054,0.854,1.908,1.908,1.908c1.054,0,1.908-0.854,1.908-1.908
					C171.633,393.517,170.779,392.663,169.725,392.663z"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M172.226,394.378
					c-0.037-0.48-0.208-0.922-0.478-1.289c-0.057,0.11-0.461,0.815-1.322,0.763c-0.786-0.048-0.25-1.306-0.06-1.705
					c-0.205-0.054-0.419-0.086-0.641-0.086c-0.243,0-0.477,0.036-0.7,0.101c0.151,0.26,0.383,0.766,0.254,1.251
					c-0.169,0.637-1.018,0.425-1.693-0.151c-0.222,0.363-0.355,0.785-0.368,1.238c0.497-0.029,1.366-0.014,1.466,0.427
					c0.112,0.494-0.408,0.898-0.995,1.108c0.273,0.379,0.648,0.679,1.086,0.858c0.106-0.452,0.363-1.219,0.9-1.259
					c0.55-0.041,0.785,0.819,0.873,1.305c0.457-0.159,0.854-0.445,1.147-0.818c-0.375-0.142-1.325-0.579-1.016-1.266
					C170.952,394.254,171.773,394.3,172.226,394.378z"/>
			</g>
			<g>
				<circle style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" cx="140.37" cy="394.571" r="3.627"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M140.37,392.061
					c-1.386,0-2.51,1.124-2.51,2.51c0,1.386,1.124,2.51,2.51,2.51c1.386,0,2.51-1.124,2.51-2.51
					C142.88,393.185,141.756,392.061,140.37,392.061z"/>
				<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M140.37,392.663
					c-1.054,0-1.908,0.854-1.908,1.908c0,1.054,0.854,1.908,1.908,1.908c1.054,0,1.908-0.854,1.908-1.908
					C142.278,393.517,141.424,392.663,140.37,392.663z"/>
				<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M142.87,394.378
					c-0.037-0.48-0.208-0.922-0.477-1.289c-0.058,0.11-0.461,0.815-1.322,0.763c-0.786-0.048-0.25-1.306-0.06-1.705
					c-0.205-0.054-0.419-0.086-0.641-0.086c-0.243,0-0.477,0.036-0.7,0.101c0.151,0.26,0.383,0.766,0.254,1.251
					c-0.169,0.637-1.018,0.425-1.693-0.151c-0.222,0.363-0.355,0.785-0.368,1.238c0.497-0.029,1.366-0.014,1.466,0.427
					c0.112,0.494-0.408,0.898-0.995,1.108c0.273,0.379,0.648,0.679,1.086,0.858c0.106-0.452,0.363-1.219,0.9-1.259
					c0.55-0.041,0.785,0.819,0.873,1.305c0.457-0.159,0.854-0.445,1.147-0.818c-0.374-0.142-1.325-0.579-1.016-1.266
					C141.597,394.254,142.417,394.3,142.87,394.378z"/>
			</g>
			<path style="fill:#263238;" d="M177.297,394.575l-0.124-1.612c0,0,0.149-0.174,0.149-0.446s-0.298-1.488-0.942-2.604
				c-0.645-1.116-1.513-1.662-2.679-2.232c-1.166-0.571-5.283-1.191-5.283-1.191s-0.01-0.007-0.028-0.018
				c-0.007-0.004-0.014-0.009-0.023-0.015c-0.014-0.009-0.033-0.021-0.053-0.034c-0.01-0.006-0.021-0.013-0.032-0.021
				c-0.014-0.009-0.029-0.018-0.044-0.028c-0.021-0.013-0.043-0.028-0.067-0.043c-0.022-0.014-0.046-0.029-0.071-0.045
				c-0.011-0.007-0.022-0.014-0.034-0.022c-0.084-0.054-0.181-0.116-0.29-0.186c-0.025-0.016-0.049-0.032-0.075-0.048
				c-0.017-0.011-0.034-0.022-0.051-0.033c-0.07-0.045-0.144-0.092-0.222-0.142c-0.031-0.02-0.064-0.041-0.097-0.062
				c-0.009-0.006-0.018-0.012-0.028-0.018c-0.111-0.071-0.228-0.146-0.351-0.224c-0.077-0.049-0.157-0.1-0.238-0.152
				c-0.056-0.036-0.113-0.072-0.171-0.109c-0.033-0.021-0.066-0.042-0.1-0.064c-0.269-0.172-0.555-0.354-0.852-0.543
				c-0.089-0.057-0.18-0.115-0.271-0.173c-0.02-0.013-0.041-0.026-0.061-0.039c-0.037-0.024-0.075-0.048-0.112-0.072
				c-0.021-0.013-0.042-0.027-0.063-0.04c-0.005-0.003-0.01-0.006-0.014-0.009c-0.204-0.13-0.41-0.262-0.618-0.394
				c-0.027-0.017-0.055-0.035-0.082-0.052c-0.101-0.064-0.202-0.128-0.302-0.192c-0.006-0.004-0.012-0.008-0.018-0.012
				c-0.327-0.208-0.653-0.415-0.972-0.618c-0.024-0.015-0.048-0.031-0.072-0.046c-0.039-0.025-0.078-0.05-0.117-0.074
				c-0.012-0.008-0.025-0.016-0.037-0.024c-0.268-0.17-0.529-0.335-0.779-0.493c-0.002-0.001-0.004-0.003-0.006-0.004
				c-0.049-0.031-0.097-0.061-0.145-0.092c-0.003-0.002-0.006-0.004-0.008-0.005c-0.045-0.029-0.09-0.057-0.135-0.085
				c-0.008-0.005-0.015-0.01-0.023-0.014c-0.046-0.029-0.091-0.058-0.136-0.086c-0.001,0-0.001-0.001-0.002-0.001
				c-0.047-0.03-0.093-0.059-0.139-0.088c-0.004-0.002-0.007-0.004-0.011-0.007c-0.043-0.027-0.086-0.054-0.128-0.081
				c-0.007-0.005-0.015-0.009-0.022-0.014c-0.12-0.076-0.236-0.149-0.346-0.218c-0.01-0.007-0.021-0.013-0.031-0.02
				c-0.032-0.02-0.064-0.04-0.095-0.06c-0.009-0.006-0.019-0.012-0.028-0.018c-0.03-0.019-0.059-0.037-0.087-0.055
				c-0.012-0.007-0.024-0.015-0.035-0.022c-0.027-0.017-0.053-0.033-0.079-0.049c-0.014-0.008-0.027-0.017-0.04-0.025
				c-0.024-0.015-0.047-0.029-0.07-0.044c-0.014-0.008-0.027-0.017-0.04-0.025c-0.023-0.015-0.047-0.029-0.069-0.043
				c-0.007-0.004-0.014-0.008-0.021-0.013c-0.314-0.195-0.531-0.327-0.611-0.37c-0.595-0.322-1.19-0.397-1.414-0.422
				c-0.223-0.025-4.167-0.496-7.713-0.422c-3.546,0.074-7.093,0.223-10.441,0.57c0,0-1.017-0.074-1.017,0.174
				c0,0.248,0.124,0.322,0.124,0.322l-2.629,3.943c0,0-0.719,0.546-0.868,0.843c-0.034,0.067-0.093,0.259-0.166,0.515
				c-0.026,0.068-0.039,0.126-0.047,0.169c-0.25,0.909-0.605,2.367-0.605,2.367s-0.546,0.744-0.57,1.389s0.174,2.257,0.174,2.257
				s-0.273,0.372-0.149,0.57c0.124,0.198,0.347,0.893,0.595,1.066c0.248,0.174,0.818,0.248,0.818,0.248l0.086,0.01
				c0.126-2.503,2.011-4.489,4.318-4.489c2.389,0,4.326,2.13,4.326,4.757c0,0.236-0.016,0.468-0.047,0.695l0.171,0.019l20.659,0.322
				l0.111,0.001c-0.037-0.249-0.057-0.505-0.057-0.765c0-2.627,1.937-4.757,4.326-4.757s4.325,2.13,4.325,4.757
				c0,0.294-0.025,0.581-0.072,0.86c0.635-0.012,2.58-0.067,2.85-0.294c0.322-0.273,0.471-0.57,0.496-0.744
				C177.371,394.798,177.297,394.575,177.297,394.575z M137.656,385.187h-0.727l2.542-3.826l0.441,0.154L137.656,385.187z
				 M144.981,385.251l-0.297-0.014l-0.82-0.039c-0.278-0.014-0.513-0.211-0.576-0.483l-0.657-2.875
				c-0.06-0.262,0.117-0.517,0.383-0.555c0.428-0.06,1.01-0.102,1.67-0.131c0.098-0.004,0.196-0.008,0.297-0.011
				c2.003-0.072,4.588-0.032,5.864-0.004l0.148,4.401L144.981,385.251z M153.309,385.65l-0.654-4.462l4.744,0.139
				c0.086,0.003,0.17,0.03,0.241,0.078l7.008,4.787L153.309,385.65z M166.129,386.31l-7.072-4.822h1.121l7.685,4.929L166.129,386.31
				z"/>
		</g>
	</g>
</g>
<g id="Car">
	<g>
		<g>
			<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M304.629,423.462c0.13-1.559,0.206-3.136,0.206-4.732
				c0-28.699-21.155-51.965-47.251-51.965c-25.193,0-45.776,21.686-47.17,49.009L304.629,423.462z"/>
			<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M626.96,421.71c0-28.699-21.155-51.965-47.25-51.965
				c-26.096,0-47.251,23.265-47.251,51.965c0,2.847,0.216,5.638,0.617,8.361l93.1,1.031
				C626.682,428.054,626.96,424.919,626.96,421.71z"/>
			<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M626.313,431.104l6.624,0.073c0,0,0.013-0.089,0.037-0.257
				C630.279,431.017,627.932,431.073,626.313,431.104z"/>
		</g>
		<g>
			<g>
				<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M662.18,415.427l-1.355-17.61c0,0,1.625-1.896,1.625-4.876
					c0-2.98-3.251-16.255-10.295-28.447c-7.044-12.191-16.526-18.152-29.259-24.383c-12.733-6.231-57.706-13.004-57.706-13.004
					s-88.049-56.352-94.552-59.874c-6.502-3.522-13.004-4.335-15.443-4.606c-2.438-0.271-45.515-5.418-84.257-4.606
					c-38.742,0.813-77.483,2.438-114.058,6.231c0,0-11.108-0.813-11.108,1.896s1.355,3.522,1.355,3.522l-28.718,43.076
					c0,0-7.857,5.96-9.482,9.211c-1.625,3.251-8.94,33.323-8.94,33.323s-5.96,8.128-6.231,15.172
					c-0.271,7.044,1.896,24.654,1.896,24.654s-2.98,4.064-1.626,6.231c1.355,2.167,3.793,9.753,6.502,11.65
					c2.709,1.896,8.94,2.709,8.94,2.709l0.942,0.106c1.381-27.337,21.969-49.038,47.172-49.038c26.096,0,47.25,23.265,47.25,51.965
					c0,2.581-0.177,5.116-0.508,7.596l1.862,0.209l225.677,3.522l1.209,0.014c-0.401-2.723-0.617-5.513-0.617-8.361
					c0-28.699,21.155-51.965,47.251-51.965c26.096,0,47.251,23.265,47.251,51.965c0,3.21-0.278,6.347-0.785,9.396
					c6.938-0.13,28.188-0.729,31.128-3.217c3.522-2.98,5.147-6.231,5.418-8.128C662.992,417.865,662.18,415.427,662.18,415.427z"/>
				<g>
					<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M524.006,323.857l-214.841-10.281l-3.239-0.153
						l-8.952-0.428c-3.04-0.153-5.607-2.307-6.294-5.271l-7.18-31.41c-0.657-2.857,1.283-5.652,4.186-6.065
						c4.675-0.657,11.03-1.115,18.241-1.436c1.069-0.046,2.139-0.092,3.239-0.122c30.233-1.085,72.612,0.168,72.612,0.168
						l63.044,1.852c0.941,0.028,1.853,0.324,2.63,0.855L524.006,323.857z"/>
					<path style="fill:#707070;stroke:#263238;stroke-miterlimit:10;" d="M373.27,268.649l1.781,48.08l25.363,1.213l-7.896-48.768
						l-10.741-0.316C381.776,268.858,378.557,268.763,373.27,268.649z"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M309.165,313.576v-44.884c-1.1,0.03-2.169,0.076-3.239,0.122
						v44.609"/>
					<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M463.324,270.755l76.4,54.455l25.467,1.896
						c0,0-67.63-43.283-88.722-56.352H463.324z"/>
					<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M507.755,324.668v-12.733c0,0-0.813-2.438-3.522-3.522
						s-14.63-3.251-17.339-1.625c-2.709,1.625-1.626,18.152-1.626,18.152l27.363,2.709L507.755,324.668z"/>
					<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M618.02,344.716c0,0-7.586,1.896-5.419,2.98
						c2.167,1.084,11.108,2.709,19.235,11.65c8.128,8.94,12.192,15.172,14.63,16.797c2.438,1.626,10.837,2.167,10.837,2.167
						s-0.271-5.96-8.398-15.984C640.777,352.302,628.315,343.633,618.02,344.716z"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M552.454,344.458c-17.447-1.75-36.03-3.535-36.03-3.535"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M660.825,397.817h-10.798c-4.716,0-8.938-2.946-10.524-7.386
						c-2.913-8.153-8.498-21.053-16.607-28.104c-12.462-10.837-36.845-13.817-40.638-14.63c-1.443-0.309-11.315-1.363-22.994-2.551"
						/>
					<line style="fill:none;stroke:#263238;stroke-miterlimit:10;" x1="648.705" y1="418.136" x2="659.47" y2="418.136"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M621.813,384.813c0,0,6.941,6.463,10.475,28.837
						c0.406,2.574,2.601,4.486,5.207,4.486h5.619"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M388.155,327.657c1.069,10.986,2.761,31.654,3.645,59.594"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M381.776,262.898l3.176,30.023c0,0,1.114,14.347,1.683,20.192
						"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M390.988,399.984c0,0-1.355,14.63-1.355,16.255"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M527.821,363.821c-1.204,17.208-4.88,38.142-5.708,41.04
						c-1.084,3.793-0.813,12.191-3.793,13.004c-2.98,0.813-5.148,0.542-5.148,0.542"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M524.006,323.857c0,0,2.984,8.939,4.068,25.736
						c0.139,2.154,0.162,4.603,0.095,7.243"/>
					<line style="fill:none;stroke:#263238;stroke-miterlimit:10;" x1="507.484" y1="418.407" x2="328.134" y2="414.072"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M302.016,381.121c-2.21-3.471-4.631-7.078-7.205-10.668
						c-10.295-14.359-28.989-31.969-30.885-39.825c-1.896-7.857-1.626-14.901-1.626-14.901"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M321.903,413.259c0,0-5.689-3.522-7.315-8.669
						c-1.067-3.378-4.816-10.838-10.177-19.625"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M265.263,299.172c1.884-11.081,5.13-28.068,8.145-33.565"/>
					<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M263.655,309.226c0,0,0.287-1.961,0.785-5.068"/>
					<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M301.726,329.274h-25.209c-0.37,0-0.671-0.3-0.671-0.671
						v-3.535c0-0.371,0.3-0.671,0.671-0.671h25.209c0.371,0,0.671,0.3,0.671,0.671v3.535
						C302.396,328.974,302.096,329.274,301.726,329.274z"/>
					<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M420.389,336.318H395.18c-0.37,0-0.671-0.3-0.671-0.671
						v-3.535c0-0.371,0.3-0.671,0.671-0.671h25.209c0.37,0,0.671,0.3,0.671,0.671v3.535
						C421.06,336.017,420.76,336.318,420.389,336.318z"/>
					<path style="fill:#878787;stroke:#263238;stroke-miterlimit:10;" d="M201.343,353.386h8.669c0,0,4.064-20.048,14.63-24.383
						c10.566-4.335,20.319-3.793,20.319-3.793s-1.626-8.669-4.335-9.753c-2.709-1.084-9.753-2.98-21.945,1.897
						c-12.191,4.877-12.191,13.004-12.191,13.004L201.343,353.386z"/>
					<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M254.985,271.568l-7.857-1.896l-28.718,43.076
						c0,0,10.024-0.271,13.817-6.502C236.021,300.014,254.985,271.568,254.985,271.568z"/>
					
						<ellipse transform="matrix(0.7071 -0.7071 0.7071 0.7071 -174.2914 269.7398)" style="fill:none;stroke:#263238;stroke-miterlimit:10;" cx="238.459" cy="345.258" rx="9.211" ry="9.211"/>
					<g>
						
							<ellipse transform="matrix(0.9766 -0.2151 0.2151 0.9766 -75.774 134.3438)" style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" cx="579.469" cy="415.38" rx="39.621" ry="39.621"/>
						<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M579.469,387.96c-15.143,0-27.42,12.276-27.42,27.42
							c0,15.144,12.276,27.42,27.42,27.42c15.144,0,27.42-12.276,27.42-27.42C606.889,400.236,594.613,387.96,579.469,387.96z"/>
						<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M579.469,394.537c-11.511,0-20.843,9.332-20.843,20.843
							c0,11.511,9.332,20.843,20.843,20.843c11.511,0,20.843-9.332,20.843-20.843C600.312,403.869,590.98,394.537,579.469,394.537z"
							/>
						<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M606.783,413.27c-0.401-5.248-2.276-10.071-5.217-14.077
							c-0.628,1.201-5.032,8.902-14.445,8.332c-8.582-0.52-2.731-14.268-0.652-18.629c-2.238-0.59-4.576-0.936-7-0.936
							c-2.656,0-5.215,0.397-7.643,1.102c1.646,2.839,4.18,8.363,2.772,13.67c-1.846,6.959-11.122,4.644-18.494-1.648
							c-2.428,3.96-3.877,8.573-4.016,13.525c5.43-0.316,14.919-0.157,16.016,4.667c1.226,5.396-4.457,9.811-10.871,12.104
							c2.983,4.138,7.076,7.416,11.862,9.375c1.154-4.941,3.964-13.314,9.832-13.748c6.004-0.445,8.569,8.943,9.537,14.254
							c4.993-1.736,9.325-4.862,12.534-8.932c-4.091-1.55-14.47-6.321-11.093-13.825
							C592.869,411.914,601.833,412.422,606.783,413.27z"/>
					</g>
					<g>
						
							<ellipse transform="matrix(0.7071 -0.7071 0.7071 0.7071 -217.917 304.6615)" style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" cx="258.801" cy="415.38" rx="39.621" ry="39.621"/>
						<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M258.801,387.96c-15.144,0-27.42,12.276-27.42,27.42
							c0,15.144,12.276,27.42,27.42,27.42c15.144,0,27.42-12.276,27.42-27.42C286.22,400.236,273.944,387.96,258.801,387.96z"/>
						<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M258.801,394.537c-11.511,0-20.843,9.332-20.843,20.843
							c0,11.511,9.332,20.843,20.843,20.843c11.511,0,20.843-9.332,20.843-20.843C279.643,403.869,270.312,394.537,258.801,394.537z
							"/>
						<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M286.114,413.27c-0.401-5.248-2.276-10.071-5.216-14.077
							c-0.628,1.201-5.033,8.902-14.445,8.332c-8.582-0.52-2.73-14.268-0.652-18.629c-2.238-0.59-4.576-0.936-7-0.936
							c-2.656,0-5.215,0.397-7.643,1.102c1.646,2.839,4.18,8.363,2.772,13.67c-1.846,6.959-11.122,4.644-18.494-1.648
							c-2.428,3.96-3.878,8.573-4.016,13.525c5.43-0.316,14.919-0.157,16.016,4.667c1.226,5.396-4.457,9.811-10.872,12.104
							c2.983,4.138,7.076,7.416,11.862,9.375c1.154-4.941,3.964-13.314,9.832-13.748c6.004-0.445,8.569,8.943,9.537,14.254
							c4.993-1.736,9.325-4.862,12.534-8.932c-4.091-1.55-14.47-6.321-11.093-13.825C272.2,411.914,281.164,412.422,286.114,413.27z
							"/>
					</g>
				</g>
			</g>
		</g>
	</g>
</g>
<g id="Location">
	<g>
		<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M397.047,132.385c-20.486,0-37.094,16.608-37.094,37.094
			c0,20.486,37.094,59.246,37.094,59.246s37.094-38.76,37.094-59.246C434.141,148.993,417.533,132.385,397.047,132.385z
			 M397.047,186.732c-9.528,0-17.252-7.724-17.252-17.252c0-9.528,7.724-17.252,17.252-17.252c9.528,0,17.252,7.724,17.252,17.252
			C414.299,179.008,406.575,186.732,397.047,186.732z"/>
		<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M392.669,126.208c-20.487,0-37.094,16.608-37.094,37.094
			c0,20.486,37.094,59.246,37.094,59.246s37.094-38.76,37.094-59.246C429.763,142.816,413.155,126.208,392.669,126.208z
			 M392.669,180.555c-9.528,0-17.252-7.724-17.252-17.252s7.724-17.252,17.252-17.252s17.252,7.724,17.252,17.252
			S402.197,180.555,392.669,180.555z"/>
	</g>
</g>
<g id="Speech_Bubble">
	<g>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M610.591,223.053c6.739-6.498,10.938-15.613,10.938-25.714
			c0-19.73-15.995-35.725-35.725-35.725c-19.73,0-35.725,15.995-35.725,35.725s15.995,35.725,35.725,35.725
			c6.955,0,13.441-1.994,18.931-5.432l9.071,7.697L610.591,223.053z"/>
		<g>
			<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M580.926,155.06c-5.614,0-10.166,4.551-10.166,10.166
				c0,5.614,10.166,16.236,10.166,16.236s10.166-10.622,10.166-16.236C591.091,159.611,586.54,155.06,580.926,155.06z
				 M580.926,169.954c-2.611,0-4.728-2.117-4.728-4.728s2.117-4.728,4.728-4.728s4.728,2.117,4.728,4.728
				S583.537,169.954,580.926,169.954z"/>
			<g>
				<g>
					<circle style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" cx="605.157" cy="206.687" r="5.165"/>
					<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M605.157,203.113
						c-1.974,0-3.574,1.6-3.574,3.574s1.6,3.574,3.574,3.574s3.574-1.6,3.574-3.574S607.131,203.113,605.157,203.113z"/>
					<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M605.157,203.97
						c-1.5,0-2.717,1.216-2.717,2.717s1.216,2.717,2.717,2.717c1.501,0,2.717-1.216,2.717-2.717S606.658,203.97,605.157,203.97z"/>
					<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M608.718,206.412
						c-0.052-0.684-0.297-1.313-0.68-1.835c-0.082,0.157-0.656,1.16-1.883,1.086c-1.119-0.068-0.356-1.86-0.085-2.428
						c-0.292-0.077-0.596-0.122-0.912-0.122c-0.346,0-0.68,0.052-0.996,0.144c0.215,0.37,0.545,1.09,0.361,1.782
						c-0.241,0.907-1.45,0.605-2.411-0.215c-0.317,0.516-0.505,1.117-0.523,1.763c0.708-0.041,1.945-0.021,2.088,0.608
						c0.16,0.703-0.581,1.279-1.417,1.578c0.389,0.539,0.922,0.967,1.546,1.222c0.15-0.644,0.517-1.736,1.282-1.792
						c0.783-0.058,1.117,1.166,1.243,1.858c0.651-0.226,1.215-0.634,1.634-1.164c-0.533-0.202-1.886-0.824-1.446-1.802
						C606.904,206.236,608.072,206.302,608.718,206.412z"/>
				</g>
				<g>
					<circle style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" cx="563.358" cy="206.687" r="5.165"/>
					<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M563.358,203.113
						c-1.974,0-3.574,1.6-3.574,3.574s1.6,3.574,3.574,3.574s3.574-1.6,3.574-3.574S565.332,203.113,563.358,203.113z"/>
					<path style="fill:#263238;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M563.358,203.97
						c-1.5,0-2.717,1.216-2.717,2.717s1.216,2.717,2.717,2.717s2.717-1.216,2.717-2.717S564.858,203.97,563.358,203.97z"/>
					<path style="fill:#92E3A9;stroke:#263238;stroke-width:0.75;stroke-miterlimit:10;" d="M566.918,206.412
						c-0.052-0.684-0.297-1.313-0.68-1.835c-0.082,0.157-0.656,1.16-1.883,1.086c-1.119-0.068-0.356-1.86-0.085-2.428
						c-0.292-0.077-0.597-0.122-0.912-0.122c-0.346,0-0.68,0.052-0.996,0.144c0.215,0.37,0.545,1.09,0.361,1.782
						c-0.241,0.907-1.45,0.605-2.411-0.215c-0.316,0.516-0.505,1.117-0.523,1.763c0.708-0.041,1.945-0.021,2.088,0.608
						c0.16,0.703-0.581,1.279-1.417,1.578c0.389,0.539,0.922,0.967,1.546,1.222c0.15-0.644,0.517-1.736,1.282-1.792
						c0.783-0.058,1.117,1.166,1.243,1.858c0.651-0.226,1.216-0.634,1.634-1.164c-0.533-0.202-1.886-0.824-1.446-1.802
						C565.104,206.236,566.273,206.302,566.918,206.412z"/>
				</g>
				<path style="fill:#263238;" d="M615.939,206.693l-0.177-2.296c0,0,0.212-0.247,0.212-0.636c0-0.389-0.424-2.119-1.342-3.708
					c-0.918-1.589-2.154-2.366-3.814-3.178c-1.66-0.812-7.522-1.695-7.522-1.695s-0.014-0.009-0.04-0.026
					c-0.01-0.006-0.021-0.013-0.034-0.021c-0.021-0.013-0.047-0.03-0.075-0.048c-0.014-0.009-0.03-0.019-0.046-0.029
					c-0.019-0.012-0.041-0.026-0.063-0.041c-0.03-0.019-0.061-0.039-0.096-0.061c-0.032-0.02-0.065-0.042-0.1-0.064
					c-0.016-0.01-0.031-0.02-0.048-0.031c-0.119-0.076-0.258-0.165-0.414-0.264c-0.035-0.022-0.07-0.045-0.107-0.068
					c-0.024-0.015-0.048-0.031-0.073-0.046c-0.1-0.064-0.205-0.131-0.316-0.202c-0.045-0.029-0.091-0.058-0.138-0.088
					c-0.013-0.008-0.026-0.016-0.039-0.025c-0.158-0.101-0.325-0.208-0.5-0.319c-0.11-0.07-0.223-0.143-0.339-0.217
					c-0.08-0.051-0.161-0.103-0.244-0.156c-0.047-0.03-0.095-0.06-0.143-0.091c-0.383-0.244-0.79-0.504-1.213-0.774
					c-0.127-0.081-0.256-0.163-0.386-0.246c-0.029-0.019-0.058-0.037-0.087-0.056c-0.053-0.034-0.106-0.068-0.16-0.102
					c-0.03-0.019-0.06-0.038-0.089-0.057c-0.007-0.004-0.014-0.009-0.021-0.013c-0.29-0.185-0.584-0.372-0.88-0.561
					c-0.039-0.025-0.078-0.05-0.117-0.075c-0.143-0.091-0.287-0.183-0.431-0.274c-0.009-0.006-0.017-0.011-0.026-0.016
					c-0.465-0.296-0.93-0.591-1.384-0.88c-0.034-0.022-0.068-0.043-0.102-0.065c-0.056-0.035-0.112-0.071-0.167-0.106
					c-0.018-0.011-0.035-0.022-0.053-0.034c-0.381-0.242-0.753-0.478-1.109-0.703c-0.003-0.002-0.006-0.004-0.009-0.006
					c-0.07-0.044-0.138-0.087-0.207-0.131c-0.004-0.003-0.008-0.005-0.012-0.008c-0.065-0.041-0.128-0.081-0.192-0.121
					c-0.011-0.007-0.022-0.014-0.033-0.021c-0.065-0.041-0.13-0.082-0.194-0.123c-0.001,0-0.001-0.001-0.002-0.001
					c-0.067-0.042-0.133-0.084-0.198-0.125c-0.005-0.003-0.01-0.006-0.015-0.01c-0.061-0.039-0.122-0.077-0.182-0.115
					c-0.01-0.007-0.021-0.013-0.031-0.02c-0.172-0.108-0.336-0.212-0.493-0.31c-0.015-0.009-0.03-0.019-0.045-0.028
					c-0.046-0.029-0.091-0.057-0.136-0.085c-0.013-0.008-0.027-0.017-0.04-0.025c-0.042-0.027-0.084-0.052-0.125-0.078
					c-0.017-0.01-0.034-0.021-0.05-0.031c-0.038-0.024-0.076-0.048-0.113-0.071c-0.019-0.012-0.038-0.024-0.057-0.036
					c-0.034-0.021-0.067-0.042-0.099-0.062c-0.019-0.012-0.038-0.024-0.057-0.035c-0.033-0.021-0.067-0.041-0.099-0.061
					c-0.01-0.006-0.019-0.012-0.029-0.018c-0.447-0.277-0.756-0.465-0.87-0.527c-0.848-0.459-1.695-0.565-2.013-0.6
					c-0.318-0.035-5.933-0.706-10.983-0.6c-5.05,0.106-10.1,0.318-14.868,0.812c0,0-1.448-0.106-1.448,0.247
					c0,0.353,0.177,0.459,0.177,0.459l-3.743,5.615c0,0-1.024,0.777-1.236,1.201c-0.048,0.095-0.133,0.368-0.236,0.733
					c-0.036,0.097-0.056,0.179-0.067,0.241c-0.357,1.294-0.862,3.37-0.862,3.37s-0.777,1.059-0.812,1.978
					c-0.035,0.918,0.247,3.214,0.247,3.214s-0.388,0.53-0.212,0.812c0.177,0.283,0.494,1.271,0.848,1.519
					c0.353,0.247,1.165,0.353,1.165,0.353l0.123,0.014c0.18-3.563,2.864-6.392,6.149-6.392c3.402,0,6.159,3.033,6.159,6.774
					c0,0.336-0.023,0.667-0.066,0.99l0.243,0.027l29.417,0.459l0.158,0.002c-0.052-0.355-0.081-0.719-0.081-1.09
					c0-3.741,2.758-6.774,6.159-6.774s6.159,3.033,6.159,6.774c0,0.418-0.036,0.827-0.102,1.225
					c0.904-0.017,3.674-0.095,4.058-0.419c0.459-0.389,0.671-0.812,0.706-1.059C616.045,207.011,615.939,206.693,615.939,206.693z
					 M559.493,193.325h-1.035l3.619-5.448l0.629,0.219L559.493,193.325z M569.923,193.417l-0.422-0.02l-1.167-0.056
					c-0.396-0.02-0.731-0.301-0.82-0.687l-0.936-4.094c-0.086-0.372,0.167-0.737,0.546-0.791c0.609-0.086,1.438-0.145,2.378-0.187
					c0.139-0.006,0.279-0.012,0.422-0.016c2.853-0.102,6.533-0.045,8.349-0.006l0.21,6.266L569.923,193.417z M581.781,193.984
					l-0.931-6.353l6.755,0.199c0.123,0.004,0.242,0.042,0.343,0.111l9.979,6.816L581.781,193.984z M600.036,194.924l-10.07-6.866
					h1.596l10.943,7.019L600.036,194.924z"/>
			</g>
		</g>
	</g>
</g>
<g id="Character_3">
	<g>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M645.875,253.392c0,0-7.096,1.812-8.002,4.529
			c-0.906,2.718-4.378,22.496-4.378,22.496l-1.812,0.453c0,0-3.473,7.851-0.151,12.38c3.322,4.529,4.831,4.378,7.7,1.51
			c2.869-2.869,4.68-6.945,4.831-7.549c0.151-0.604-0.151,15.098,0.302,16.306c0.453,1.208,0.453,1.208,0.453,1.208
			s28.233-1.661,30.196-2.416c1.963-0.755,2.265-1.51,1.812-2.567c-0.453-1.057,3.925-19.929,3.925-19.929s1.51,10.116,1.359,10.871
			c-0.151,0.755-3.501,6.725-2.444,9.443c1.057,2.718,5.161,3.844,7.728,1.579c2.567-2.265,3.322-9.512,3.322-9.512
			s-0.906-24.157-2.114-28.686c-1.208-4.529-6.945-8.908-6.945-8.908L645.875,253.392z"/>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M658.786,244.74c0,0,0.476,2.475,0.381,3.332
			c-0.095,0.857-1.333,3.141-3.713,5.045c-2.38,1.904-2.38,1.904-2.38,1.904s0.571,1.809,7.996,3.427
			c7.425,1.618,13.137-3.332,13.137-3.332s1.618-1.333,0-6.663c-1.618-5.331-5.426-11.518-5.426-11.518l-2.665-2.951
			c0,0-1.809,4.569-3.713,6.473C660.5,242.36,658.786,244.74,658.786,244.74z"/>
		<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M653.403,239.808c0,0-2.158,1.918-3.117,4.076
			c-0.959,2.158,2.158,3.596,1.678,4.555c-0.479,0.959-5.154,1.558-7.552,4.555c-2.398,2.997,2.997,9.35,8.391,9.11
			c5.394-0.24-0.36-5.514,3.476-8.871c3.836-3.356,4.294-4.4,2.507-8.493C656.999,240.647,653.403,239.808,653.403,239.808z"/>
		<g>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M652.638,221.087c0,0-1.448,3.041-1.738,8.835
				c-0.29,5.793,0.579,8.69,1.738,11.007c1.159,2.317,2.607,4.924,6.228,4.49c3.621-0.434,7.097-4.779,7.821-6.807
				c0.724-2.028,0.724-8.11-1.738-14.048S656.113,215.294,652.638,221.087z"/>
			<line style="fill:none;stroke:#263238;stroke-miterlimit:10;" x1="653.362" y1="233.977" x2="654.81" y2="234.991"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M654.086,237.163c0,0,4.2,0,5.938-2.897"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M651.076,227.224c0,0,0.952-1.523,2.094-0.571"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M656.311,225.035c0,0,1.999-2.57,3.998-0.857"/>
			<ellipse style="fill:#263238;" cx="659.024" cy="228.081" rx="0.999" ry="1.428"/>
			<path style="fill:#263238;" d="M653.406,230.095c0,0.675-0.383,1.223-0.856,1.223c-0.473,0-0.856-0.548-0.856-1.223
				s0.383-1.223,0.856-1.223C653.023,228.872,653.406,229.419,653.406,230.095z"/>
		</g>
		<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M656.787,218.276c0,0,4.188,1.142,5.807,6.473
			c1.618,5.331,3.332,16.659,6.568,21.038c3.237,4.379,6.378,7.615,1.809,13.422c-4.569,5.807-6.092,11.233-2.57,15.707
			s11.328,1.523,15.041-2.951c3.712-4.474,3.046-7.901,1.047-11.518c-1.999-3.617-1.744-2.815-0.03-1.768
			c1.713,1.047,3.808,1.428,2.951-3.617c-0.857-5.045-3.492-10.036-5.681-11.654s-5.331-5.236-4.569-11.994
			c0.762-6.759-2.285-11.804-7.425-15.136c-5.141-3.332-14.66-0.571-15.707,0.952c-1.047,1.523-1.047,1.523-1.047,1.523
			s-2.475,1.333-2.38,3.236c0.095,1.904,1.523,1.238,1.523,1.238S653.456,218.943,656.787,218.276z"/>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M614.442,258.605c0,0-1.635-3.27-1.09-4.36
			c0.545-1.09,3.542-3.452,4.087-2.634c0.545,0.817,0.272,1.181-0.454,1.726c-0.727,0.545-1.635,1.453-1.635,1.453l0.908,2.18
			L614.442,258.605z"/>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M615.831,267.433l4.831,5.888c0,0,6.794,15.551,8.002,17.514
			c1.208,1.963,3.322,3.775,5.888,2.567c2.567-1.208,2.114-4.982,1.057-7.247c-1.057-2.265-7.247-9.059-8.455-10.87
			c-1.208-1.812-2.416-4.227-2.416-4.227s-0.302-4.529-0.453-6.19c-0.151-1.661-6.341-7.7-6.492-8.153
			c-0.151-0.453-2.718,2.114-2.718,2.114L615.831,267.433z"/>
		<polygon style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" points="611.808,242.438 619.71,263.147 627.067,263.328 
			619.074,241.438 		"/>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M614.078,261.239c0,0-0.545-3.906,0.908-4.723
			c1.453-0.817,6.267-2.452,6.721-2.089c0.454,0.363-0.363,1.181-1.181,1.544c-0.818,0.363-3.906,1.635-3.906,1.635l0.454,1.544"/>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M614.714,264.963c0,0-0.908,0.999-0.454,1.544
			c0.454,0.545,1.998,0,3.542,0c1.544,0,3.542,0.091,3.361-0.817c-0.182-0.908-2.18-1.181-2.997-1.181
			S615.532,264.6,614.714,264.963z"/>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M614.714,264.963c0,0-1.09-1.907-0.999-2.725
			c0.091-0.818,2.271-2.997,3.361-3.088c1.09-0.091,5.541-0.727,5.631-0.091c0.091,0.636-1.907,0.908-2.816,1.272
			c-0.908,0.363-2.089,0.636-2.089,0.636l-1.181,2.089l0.182,0.636"/>
		<g>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M699.975,415.96l3.269,8.025c0,0,4.012,9.957,3.864,13.375
				c-0.149,3.418,1.486,4.607,2.824,4.755c1.337,0.149,4.161-3.418,5.053-7.579c0.892-4.161-1.486-13.672-1.486-14.861
				c0-1.189,1.04-6.836-1.338-8.768c-2.378-1.932-5.796-2.378-5.796-2.378L699.975,415.96z"/>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M699.975,415.96c0,0,3.864,2.378,7.133,0.892
				c3.269-1.486-0.297-5.35-0.297-5.35L699.975,415.96z"/>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M714.984,434.536c0.892-4.161-1.486-13.672-1.486-14.861
				c0-1.189,1.04-6.836-1.338-8.768c-0.487-0.395-1.017-0.725-1.552-1.005c-0.255,2.28,1.104,6.79,1.701,9.773
				c0.743,3.715,1.932,9.659,1.04,14.117c-0.81,4.048-4.427,6.985-5.088,7.493c0.51,0.509,1.112,0.767,1.67,0.829
				C711.269,442.263,714.092,438.697,714.984,434.536z"/>
		</g>
		<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M644.366,303.517c0,0-3.09,13.62-1.753,24.616
			c1.338,10.997,14.563,66.724,14.563,66.724l5.35,31.802l10.254-0.892l-1.338-34.477l-4.458-42.353c0,0,9.956,32.099,11.888,35.368
			c1.932,3.269,19.022,30.018,19.022,30.018s3.418,0.297,5.647-1.337c2.229-1.635,2.823-4.458,2.823-4.458
			s-6.687-20.656-11.145-28.235c-4.458-7.579-5.498-7.728-5.498-7.728s1.486-7.133,1.486-9.214c0-2.08,0-5.201,0-7.876
			s-4.755-31.653-8.173-40.421c-3.418-8.768-7.43-13.672-7.43-13.672L644.366,303.517z"/>
		<line style="fill:none;stroke:#263238;stroke-miterlimit:10;" x1="659.257" y1="332.741" x2="665.647" y2="347.007"/>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M663.863,429.037c0,0-0.149,8.025-0.297,8.916
			c-0.149,0.892-8.619,10.848-10.402,12.037c-1.783,1.189-2.378,1.783-2.081,2.972c0.297,1.189,3.715,1.783,7.133,1.337
			s9.214-4.012,10.551-4.904c1.337-0.892,7.876-2.675,7.876-6.241c0-3.567-2.972-9.362-2.972-9.362l-1.04-5.944L663.863,429.037z"/>
		<g>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M674.901,436.5c-0.991,0.355-1.989,0.614-2.716,0.562
				c-2.08-0.149-5.498-1.189-7.133-1.486c-0.916-0.167-1.272,0.6-1.408,1.307c-0.026,0.523-0.052,0.913-0.078,1.071
				c-0.149,0.892-8.619,10.848-10.402,12.037c-1.783,1.189-2.378,1.783-2.081,2.972c0.297,1.189,3.715,1.783,7.133,1.337
				c3.418-0.446,9.214-4.012,10.551-4.904c1.337-0.892,7.876-2.675,7.876-6.241C676.643,441.165,675.719,438.484,674.901,436.5z"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M658.662,452.517c-2.442,0.849-5.861,0.107-7.62-0.387
				c-0.05,0.251-0.037,0.521,0.041,0.833c0.297,1.189,3.715,1.783,7.133,1.337c3.418-0.446,9.214-4.012,10.551-4.904
				c1.337-0.892,7.876-2.675,7.876-6.241c0-0.269-0.019-0.552-0.051-0.842c-1.439,0.543-2.963,1.16-4.11,1.734
				C669.51,445.533,662.08,451.328,658.662,452.517z"/>
		</g>
		<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M661.188,423.539c0,0,0.594,6.687,1.486,6.836
			s11.591-0.297,11.74-1.635c0.149-1.337,0.149-4.458-0.446-5.647C673.374,421.904,661.783,421.458,661.188,423.539z"/>
		<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M695.071,410.907c-3.29-0.646,1.635,5.201,3.567,6.39
			c1.932,1.189,6.836-1.04,8.173-4.755c1.338-3.715,0.892-6.687-0.149-7.133c-1.04-0.446-1.486,0.297-1.486,0.297
			S703.393,412.542,695.071,410.907z"/>
		<g>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M686.115,293.146c0,0-8.537-5.869-9.604-7.737
				c-1.067-1.867-2.001-2.935-2.001-2.935s-0.267-4.269-0.8-6.403c-0.534-2.134-1.867-5.469-2.668-6.269
				c-0.8-0.8-4.669-1.867-4.535-0.4c0.133,1.467,2.134,1.067,2.534,1.201c0.4,0.133,1.867,2.668,1.867,2.668
				s-4.135-0.133-4.802,0.133c-0.667,0.267-3.201,2.134-3.735,3.068c-0.534,0.934,2.134,4.935,2.268,5.336
				c0.133,0.4,0.8,1.467,1.867,1.734c1.067,0.267,1.734-1.067,1.734-1.067s0.534,2.801,1.467,3.735
				c0.934,0.934,2.534,1.067,2.534,1.067s5.869,9.471,7.07,11.605c1.201,2.134,3.468,4.669,6.003,2.134
				C687.849,298.482,687.048,294.613,686.115,293.146z"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M663.112,278.482c0,0,2.048-2.911,3.126-3.234
				s4.42-0.431,4.42-0.431"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M664.458,280.992c0,0,1.576-2.232,2.889-2.889
				c1.313-0.657,4.202-0.263,4.202-0.263"/>
		</g>
	</g>
</g>
<g id="Character_2">
	<g>
		<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M349.746,326.702c0,0-1.542,16.086-2.644,20.714
			c-1.102,4.628-5.509,35.698-7.712,39.664c-2.204,3.966-1.322,7.492-2.644,9.916c-1.322,2.424-2.204,3.966-2.204,3.966
			s-2.424,21.375-3.305,26.663c-0.881,5.289-0.441,5.068,0,7.492c0.441,2.424-2.424,4.187-2.424,4.187s3.085,3.526,4.187,5.509
			c1.102,1.983,7.492,2.865,10.577,3.746c3.085,0.881,3.305,3.305,2.644,3.966s-3.085,0.881-3.085,0.881h-33.274
			c0,0-0.22-4.628-0.22-7.051c0-2.424,0.441-5.509,0.661-6.17c0.22-0.661,0.661-2.203,0.661-5.289c0-3.085,3.966-7.492,4.187-9.255
			c0.22-1.763-0.661-10.357,0.881-18.29c1.542-7.933,6.39-27.545,6.39-28.206c0-0.661,0.441-22.917,0.441-22.917
			s-7.933-20.273-8.814-21.154c-0.882-0.881-4.628-14.103-3.966-16.086c0.661-1.983,3.085-5.509,15.205-5.509
			c12.12,0,16.967,5.95,18.069,7.272C344.458,322.074,349.746,326.702,349.746,326.702z"/>
		<g>
			<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M328.813,439.305c0,0,3.085,3.526,4.187,5.509
				c1.102,1.983,7.492,2.865,10.577,3.746c3.085,0.881,3.305,3.305,2.644,3.966c-0.661,0.661-3.085,0.881-3.085,0.881h-33.274
				c0,0-0.22-4.628-0.22-7.051c0-0.566,0.026-1.169,0.065-1.768c0.787-0.126,1.645-0.27,2.579-0.435
				C319.778,442.831,328.813,439.305,328.813,439.305z"/>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M309.729,450.169l27.898,1.697l8.894-0.182
				c-0.013,0.364-0.124,0.667-0.3,0.843c-0.661,0.661-3.085,0.881-3.085,0.881h-33.274
				C309.862,453.408,309.79,451.908,309.729,450.169z"/>
		</g>
		<g>
			<g>
				<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M309.862,426.304l1.102,8.594c0,0,7.051,9.916,7.933,11.018
					c0.881,1.102-0.882,1.983-2.644,2.644c-1.763,0.661-14.103-4.187-16.527-6.39c-2.424-2.204-7.051-7.052-7.051-7.052
					l-1.983,1.322c0,0-5.289-6.611-4.628-8.153c0.661-1.543,8.594-7.492,8.594-7.492L309.862,426.304z"/>
				<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M287.748,426.409c2.738,3.213,10.223,11.898,13.079,14.219
					c3.034,2.465,14.705,5.743,17.907,6.611c-0.522,0.509-1.493,0.952-2.481,1.322c-1.763,0.661-14.103-4.187-16.527-6.39
					c-2.424-2.204-7.051-7.052-7.051-7.052l-1.983,1.322c0,0-5.289-6.611-4.628-8.153
					C286.232,427.892,286.881,427.207,287.748,426.409z"/>
			</g>
			<path style="fill:#5E5E5E;stroke:#263238;stroke-miterlimit:10;" d="M338.949,322.074c0,0,3.746,21.154,3.966,23.358
				c0.22,2.204,3.305,26.443,1.102,31.511c-2.204,5.068-13.883,26.443-13.883,26.443s-13.221,14.984-14.103,15.645
				c-0.881,0.661-0.22,2.204-0.661,3.085c-0.441,0.881-3.085,1.763-3.085,1.763s-0.661,2.865-1.102,3.966s-3.305-0.22-7.713-1.102
				c-4.407-0.882-8.814-5.95-8.814-5.95s0.882-3.746,1.322-4.407c0.441-0.661-1.322-0.441,0.661-3.966
				c1.983-3.526,3.526-5.068,3.526-5.068l18.951-26.223l2.424-6.17c0,0-1.102-9.696-0.882-11.238
				c0.22-1.543,1.322-4.628,1.322-5.289s-9.696-21.815-10.137-23.578c-0.441-1.763-4.407-8.153-4.628-10.357
				c-0.22-2.204,2.865-5.509,2.865-5.509s9.916-5.068,13.662-5.068c3.746,0,4.848-0.22,7.272,1.102
				c2.424,1.322,6.39,3.966,6.39,3.966L338.949,322.074z"/>
			<path style="fill:none;stroke:#263238;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="M323.078,372.594
				c0,0,5.496-1.749,7.495-1.999c1.999-0.25,2.038,1.131-3.498,2.748"/>
			<path style="fill:none;stroke:#263238;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="M322.079,323.626
				c0,0,0.749,12.742,3.248,17.988c2.498,5.246,4.497,12.242,5.746,17.488c1.249,5.246-1.249,4.247-0.999,5.746
				c0.25,1.499,2.998,3.748,2.998,3.748"/>
			<path style="fill:none;stroke:#263238;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="M327.825,376.591
				c0,0-1.749,7.495-7.245,15.99c-5.496,8.494-14.74,18.238-14.99,20.237c-0.25,1.999,0.999,4.497,0.999,4.497s-3.997,0-4.247,1.499
				c-0.25,1.499-0.999,3.248-0.999,3.248"/>
		</g>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M349.086,240.983c0,0,1.542,9.255,1.763,10.577
			c0.22,1.322,3.746,13.222,2.204,22.256c-1.543,9.035-2.204,29.748-2.204,29.748l0.882,25.782c0,0-3.526,2.204-10.798-2.644
			c-7.272-4.848-8.594-9.696-13.001-9.696c-4.407,0-10.798,3.305-13.883,4.187c-3.085,0.882-8.814,2.865-8.814,2.865
			s3.526-25.562,5.509-33.495c1.983-7.933,3.526-30.63,7.712-39.885c4.187-9.255,8.594-13.662,10.577-14.984
			c1.983-1.322,3.305-1.102,3.305-1.102S345.56,235.914,349.086,240.983z"/>
		<g>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M358.609,209.409c0,0,2.264,6.307,1.779,8.248
				c-0.485,1.941-0.809,2.588-0.485,3.396c0.324,0.809,2.749,4.69,1.941,5.499c-0.809,0.809-1.779,1.617-1.779,1.617l-5.014,3.882
				l-2.426,3.558l-2.103,3.72l-15.688-9.219l0.97-2.264c0,0-3.235-10.189-1.617-14.394c1.617-4.205,11.968-4.205,15.85-4.367
				C353.919,208.923,358.609,209.409,358.609,209.409z"/>
			<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M349.751,204.522c0,0,4.977-1.745,9.667,0.52
				c4.69,2.264,0,6.469-1.132,6.631c0,0,0,4.69-0.809,5.499c-0.809,0.809-0.809,0.809-0.809,0.809s-0.647,5.822-0.324,7.278
				c0.324,1.456,0.485,2.102,1.294,2.426c0.809,0.324,2.749-1.132,2.426,0.485c-0.323,1.617-0.809,3.073-0.809,3.073
				s0.162,4.205,0,5.661c-0.162,1.456-3.882,1.456-5.337,0.647c-1.456-0.809-5.337-4.367-4.205-5.822
				c1.132-1.456,3.558-3.396,3.558-5.337c0-1.941-0.809-7.925-2.911-7.763c-2.103,0.162-2.75,1.294-2.911,2.911
				c-0.162,1.617,0.324,2.426-0.162,3.235c-0.485,0.809-1.132,1.941-4.852,3.235c-3.72,1.294-5.661,1.294-6.631-0.162
				c-0.97-1.456-3.396-10.027-3.396-14.718s0.485-5.822,6.307-8.734C344.539,201.484,348.008,203.518,349.751,204.522z"/>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M347.612,223.479c0,0,0.647,3.558,2.103,4.205
				c1.456,0.647,2.103-0.485,2.103-0.485"/>
		</g>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M329.033,235.694c0,0,1.601-6.696,4.187-7.272
			c2.586-0.576,10.136,4.848,13.001,6.831c2.865,1.983,5.729,5.95,5.729,5.95l-1.102,2.865c0,0-6.169-5.944-9.474-6.826
			C338.069,236.36,329.033,235.694,329.033,235.694z"/>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M346,253.102c0,0-0.661,2.424-1.763,4.407
			c-1.102,1.983-2.424,4.848-2.424,4.848l0.882,27.765l7.272,16.527l-11.899,1.983c0,0-11.238-18.951-11.459-21.154
			c-0.22-2.204-5.729-29.528-5.729-31.07c0-1.543-1.983-4.187,2.865-9.916"/>
		<path style="fill:none;stroke:#263238;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="M318.015,261.696
			c0,0,3.967,5.289,5.068,5.95"/>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;" d="M350.628,308.633
			c0,0,7.712,12.34,8.374,13.221s4.187,5.509,4.848,7.052c0.661,1.542,2.204,8.594,2.424,9.916c0.22,1.322-0.22,2.644-1.543,1.983
			c-1.322-0.661-1.102-4.628-1.102-4.628l-1.983-5.068c0,0,0.441,10.136,0,11.679c-0.441,1.543-1.983,0.441-1.983-2.865
			c0-3.305,0.22-4.848,0.22-4.848l-1.322-2.424c0,0-1.102,6.39-1.542,8.374c-0.441,1.983-1.983,2.204-2.204,0.441
			c-0.22-1.763,0.661-3.305,0.661-3.966c0-0.661-0.881-4.848-0.881-4.848s-0.22,6.17-1.542,6.831
			c-1.322,0.661-1.763-0.661-1.102-2.204c0.661-1.543,0.661-1.983,0.661-2.865c0-0.881-0.881-5.068-0.881-5.068
			s-0.441-5.289-0.441-5.95s-11.459-13.442-11.459-13.442L350.628,308.633z"/>
		<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M348.865,302.683c0,0,5.068,5.068,4.407,6.17
			c-0.661,1.102-11.899,3.085-13.883,2.644c-1.983-0.441-3.526-1.983-3.746-3.085C335.423,307.31,344.899,301.802,348.865,302.683z"
			/>
	</g>
</g>
<g id="Character_1">
	<g>
		<g>
			<path style="fill:#C9C9C9;stroke:#263238;stroke-miterlimit:10;" d="M57.289,221.866c0,0,3.01,6.021,3.462,7.375
				c0.452,1.355,0.753,4.064,0.602,5.118c-0.15,1.054-2.86,2.86-2.86,2.86l15.052,12.192c0,0,2.86-4.666,4.365-8.58
				c1.505-3.913,2.974-7.749,4.479-8.502c1.505-0.753,4.798-0.589,5.756-2.486c0.455-0.901-0.301-6.171-0.753-8.278
				c-0.451-2.107-1.204-3.612-1.204-3.612s1.806-1.505,1.505-2.86c-0.301-1.355-1.806-1.656-3.01-1.957
				c-1.204-0.301-1.957-0.451-2.559-1.054c-0.602-0.602-0.301-1.355-1.957-3.462c-1.656-2.107-5.118-2.86-5.118-2.86
				s-2.709-0.452-7.074,0.602c-4.365,1.054-9.934,10.235-10.536,11.74C56.837,219.608,57.289,221.866,57.289,221.866z"/>
			<path style="fill:#263238;" d="M80.508,212.396c0.293,0.552,0.252,1.148-0.092,1.331c-0.344,0.183-0.861-0.117-1.154-0.669
				c-0.293-0.552-0.252-1.148,0.092-1.331C79.699,211.544,80.215,211.844,80.508,212.396z"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M77.254,211.032c0,0,1.263-2.02,2.609-0.926"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M85.84,219.955c0,0-3.199,1.263-6.145-0.842"/>
			<path style="fill:#A6A6A6;stroke:#263238;stroke-miterlimit:10;" d="M57.439,218.103c-0.602,1.505-0.151,3.763-0.151,3.763
				s1.486,2.973,2.527,5.211c0.351-0.264,0.708-0.613,1.053-1.056c1.363-1.752,4.673-4.478,4.868-4.867
				c0.195-0.389,1.46-4.576,1.46-4.576s0.876-0.389,1.558-1.071c0.681-0.681,0.389-1.071,0.292-1.947
				c-0.097-0.876,3.115-4.089,3.699-5.257c0.584-1.168-0.779-2.434-0.779-2.434l-0.191-0.131c-1.054,0.088-2.33,0.27-3.8,0.625
				C63.611,207.416,58.041,216.598,57.439,218.103z"/>
			<path style="fill:#C9C9C9;stroke:#263238;stroke-miterlimit:10;" d="M69.387,216.769c0,0-4.422-2.579-4.572,1.936
				c-0.15,4.516,2.559,3.763,4.516,4.064c1.957,0.301,2.107-1.656,2.107-1.656"/>
			<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M58.794,205.309c0,0-1.505-1.204-3.763,2.559
				c-2.258,3.763-1.355,4.817-1.355,4.817s-1.656,0.301-2.107,3.01c-0.451,2.709,0.903,5.268,3.311,6.021
				c2.408,0.753,2.408,0,4.365-2.107c1.957-2.107,0.753-6.472,4.516-9.332c3.763-2.86,7.074-3.612,9.482-4.064
				c2.408-0.451,3.913-1.505,3.161-3.311c-0.753-1.806-4.666-4.064-6.773-2.709c-2.107,1.355-2.86,1.656-2.86,1.656
				S59.847,201.245,58.794,205.309z"/>
		</g>
		<g>
			<g>
				<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M51.116,427.763c0,0-0.01,0.556-0.028,1.408
					c-0.057,2.669-0.19,8.247-0.323,8.78c-0.176,0.703-2.108,2.284-2.459,5.445s-0.176,7.026,0,7.905
					c0.176,0.878-0.351,0.527,8.783,1.757c9.134,1.23,21.781,2.81,25.821,1.581c4.04-1.23,3.513-3.689,2.811-4.567
					c-0.703-0.878-5.621-1.23-9.134-4.216c-3.513-2.986-10.891-7.905-11.945-9.134c-1.054-1.23-1.23-0.703-1.757-2.284
					c-0.527-1.581-1.23-4.567-1.23-4.567L51.116,427.763z"/>
				<path style="fill:#878787;stroke:#263238;stroke-miterlimit:10;" d="M76.586,445.855c-3.487-2.964-10.774-7.827-11.913-9.102
					c-1.646,1.888-4.639,4.994-6.707,5.237c-2.986,0.351-7.378-2.635-7.378-2.635h-0.725c-0.618,0.908-1.354,2.2-1.559,4.04
					c-0.351,3.162-0.176,7.026,0,7.904c0.176,0.878-0.351,0.527,8.783,1.757c9.134,1.23,21.781,2.81,25.821,1.581
					c4.04-1.23,3.513-3.689,2.811-4.567C85.017,449.193,80.099,448.842,76.586,445.855z"/>
			</g>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M82.91,454.638c1.862-0.567,2.747-1.394,3.099-2.208
				c-3.714,0.834-12.575,1.303-19.799-0.431c-6.042-1.45-14.773-2.514-18.079-2.888c0.037,1.039,0.105,1.845,0.174,2.19
				c0.176,0.878-0.351,0.527,8.783,1.757C66.222,454.287,78.869,455.868,82.91,454.638z"/>
		</g>
		<g>
			<g>
				<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M107.15,423.371c0,0,2.811,4.391,3.864,5.094
					c1.054,0.703,9.485,4.567,12.823,3.865c3.337-0.703,10.364-3.337,10.188-1.054c-0.176,2.283-3.162,6.324-6.851,8.431
					s-9.134,2.811-10.539,3.513c-1.405,0.703-3.338,1.054-4.04,1.405c-0.703,0.351,0.176,1.757,0.176,1.757
					s-5.094,2.284-6.675,2.986c-1.581,0.703-5.094,1.054-5.094,1.054l-0.351-2.635c0,0-1.932-5.094-2.284-7.553
					c-0.351-2.459-1.054-6.851-1.405-9.486c-0.351-2.635-1.054-4.743-1.054-4.743L107.15,423.371z"/>
				<path style="fill:#878787;stroke:#263238;stroke-miterlimit:10;" d="M110.312,434.438c0,0-1.932,1.23-6.499,2.108
					c-2.47,0.475-4.522,0.641-5.892,0.694c0.17,1.12,0.329,2.17,0.447,2.995c0.351,2.459,2.284,7.553,2.284,7.553l0.351,2.635
					c0,0,3.513-0.351,5.094-1.054c1.581-0.703,6.675-2.986,6.675-2.986s0.353-1.319,1.055-1.671
					c0.703-0.351,3.466-0.614,4.871-1.316c1.405-0.703,4.789-1.58,8.477-3.688c3.689-2.108,6.675-6.148,6.851-8.431
					c0.176-2.284-6.851,0.351-10.188,1.054c-3.338,0.703-11.769-3.162-12.823-3.864c0,0-0.703,1.23-0.703,3.162
					S110.312,434.438,110.312,434.438z"/>
			</g>
			<path style="fill:#FFFFFF;stroke:#263238;stroke-miterlimit:10;" d="M134.023,431.29c-3.084,4.069-9.538,7.884-15.581,9.818
				c-4.741,1.517-12.952,4.963-17.746,7.023l0.305,2.291c0,0,3.513-0.351,5.094-1.054c1.581-0.703,6.675-2.986,6.675-2.986
				s0.353-1.319,1.055-1.671c0.703-0.351,3.466-0.614,4.871-1.316c1.405-0.703,4.789-1.58,8.477-3.688
				C130.856,437.604,133.837,433.577,134.023,431.29z"/>
		</g>
		<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M50.237,314.64c0,0-5.445,9.31-4.391,16.863
			c1.054,7.553,4.567,12.823,4.567,12.823l1.757,28.983c0,0-5.27,20.903-2.986,34.429c2.284,13.526,0.703,23.187,0.703,23.187
			l13.526,0.527l5.445-39.347l4.743-15.633l1.932-10.012l6.851,19.146l12.296,42.333l12.472-4.567l-6.324-45.144
			c0,0-5.797-26.7-6.324-28.983c-0.527-2.284-6.324-25.119-6.324-25.119l-1.22-4.277c0,0-3.874-4.154-7.563-5.559
			c-3.689-1.405-7.553-1.581-14.755-0.878C57.439,314.113,50.237,314.64,50.237,314.64z"/>
		<g>
			<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M84.19,284.121c0,0,6.232-1.246,8.032-1.939
				c1.8-0.692,2.493-1.246,2.493-1.246s0.415,4.708,1.246,5.954c0.831,1.246,0.831,1.246,0.831,1.246s-2.631,3.462-5.539,5.262
				c-2.908,1.8-5.955,3.047-5.955,3.047L84.19,284.121z"/>
			<path style="fill:#C9C9C9;stroke:#263238;stroke-miterlimit:10;" d="M94.714,280.936l2.908-1.8c0,0,2.493-3.6,3.877-3.877
				c1.385-0.277,12.325,0.415,12.325,0.415s-0.554,1.108-1.523,2.216c-0.969,1.108-1.8,2.631-2.077,3.6
				c-0.277,0.969-0.416,3.047-2.493,4.016c-2.077,0.969-6.785,2.354-7.893,2.631c-1.108,0.277-3.047,0-3.047,0
				S94.853,284.952,94.714,280.936z"/>
			<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M111.196,274.322c0.071-0.213,8.519-6.815,8.945-6.744
				c0.426,0.071,0.497,0.284,0.923,1.349c0.426,1.065-9.37,8.377-9.37,8.377L111.196,274.322z"/>
			<path style="fill:#C9C9C9;stroke:#263238;stroke-miterlimit:10;" d="M97.173,280.62c0,0,0.79-3.724,1.806-4.966
				c1.016-1.241,2.708-2.031,5.304-2.483c2.596-0.451,4.514,0.903,4.514,0.903s1.693,0.564,0,1.354c-1.693,0.79-3.611,0-3.611,0
				s-0.339,3.273-1.58,4.401c-1.241,1.128-3.273,1.354-3.273,1.354"/>
			<path style="fill:#C9C9C9;stroke:#263238;stroke-miterlimit:10;" d="M113.875,276.558c0,0,0.451-3.837,1.58-4.288
				c1.129-0.452,1.918,1.129,1.467,2.257c-0.451,1.128-1.58,1.918-2.144,2.483C114.214,277.573,113.875,276.558,113.875,276.558z"/>
			<path style="fill:#C9C9C9;stroke:#263238;stroke-miterlimit:10;" d="M113.751,279.221c0,0-2.201-1.562-3.266-2.272
				c-1.065-0.71-3.024-2.767-1.689-2.874c1.334-0.107,2.115,0.177,3.393,0.815c1.278,0.639,2.769,1.704,2.84,2.84
				C115.1,278.866,114.39,279.079,113.751,279.221z"/>
			<path style="fill:#C9C9C9;stroke:#263238;stroke-miterlimit:10;" d="M113.751,279.221c0,0-1.846-0.639-3.692-1.633
				c-1.846-0.994-3.975-1.349-4.33-0.639c-0.355,0.71,0.284,1.917,1.136,2.272c0.852,0.355,3.62,1.278,4.685,1.562
				C112.616,281.066,114.106,280.711,113.751,279.221z"/>
			<path style="fill:#C9C9C9;stroke:#263238;stroke-miterlimit:10;" d="M107.291,283.693l-1.136-1.633c0,0-2.769-0.142-3.549-0.994
				c-0.781-0.852,1.704-1.278,2.769-1.42c1.065-0.142,3.762,0.71,3.762,0.71s1.278,2.982,1.207,3.549
				c-0.071,0.568-1.362,1.382-1.988,1.491c-0.625,0.109-1.988,0.497-1.988,0.497"/>
		</g>
		<g>
			<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M60.372,235.238c0,0,10.801,7.478,15.786,14.817
				c4.985,7.339,7.893,21.879,9.278,29.911c1.385,8.032,0.969,21.326,1.8,24.787c0.831,3.462,1.246,4.57,0.554,6.093
				c-0.692,1.523-1.385,2.077-1.385,2.077s1.246,4.293,1.246,4.985c0,0.692-0.692,1.939-0.692,1.939s-6.924-4.431-11.078-5.124
				c-4.154-0.692-10.801,0.692-17.171,1.108c-6.37,0.415-9.001,0.277-9.97,0c-0.969-0.277,2.216-5.124,2.354-5.816
				c0.139-0.692-3.047-0.416-3.185-3.601c-0.138-3.185-0.277-4.016-0.138-6.37c0.138-2.354,0.277-18.694,0.277-18.694
				s-2.216-25.064-1.108-29.496c1.108-4.431,0.415-8.447,3.739-11.217C54.002,237.869,60.372,235.238,60.372,235.238z"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M52.618,309.011c0,0,17.08-1.928,20.11-1.791
				c3.03,0.138,11.846,4.683,11.846,4.683"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M60.745,297.303c0,0,7.576,0.413,5.648,1.515
				c-1.928,1.102-7.714,3.306-7.714,3.306"/>
		</g>
		<g>
			<path style="fill:#737373;stroke:#263238;stroke-miterlimit:10;" d="M48.348,246.614c0,0-1.102-0.826-2.893-2.204
				c-1.791-1.377-7.3-1.791-7.3-1.791s-1.653-5.096-2.755-5.647c-1.102-0.551-5.234-0.964-5.785,1.377
				c-0.551,2.342,0.413,6.612,0.413,6.612s-5.51,7.576-6.336,8.953c-0.826,1.377-3.03,0.826-2.617,4.545
				c0.413,3.719,1.102,10.882,1.377,13.223c0.276,2.342-0.138,4.408,0.964,5.51c1.102,1.102,2.342,0.827,2.342,0.827l0.413,2.066
				c0,0-1.377-0.276-2.066,2.342c-0.689,2.617,0.413,7.3,1.377,10.744c0.964,3.444,1.377,6.474,3.168,8.816
				c1.791,2.342,3.443,1.791,3.443,1.791s6.061,3.444,8.953,3.995c2.893,0.551,8.264-0.138,11.708-2.617
				c3.444-2.479,4.408-4.959,5.51-7.3c1.102-2.342,2.617-6.749,2.755-9.917c0.138-3.168-0.413-5.372-0.551-5.785
				c-0.138-0.413-5.372-10.882-7.3-19.422C51.241,254.19,48.348,246.614,48.348,246.614z"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M24.243,256.118c0,0,3.168-0.826,3.719,0.551
				c0.551,1.377,3.168,18.595,2.479,19.973c-0.689,1.378-3.306,1.24-3.306,1.24"/>
			<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M27.687,280.223c0,0,2.755-1.653,3.443,0.413
				c0.689,2.066,6.612,19.835,5.923,20.937c-0.689,1.102-2.617,1.791-3.03,1.928"/>
			<polyline style="fill:none;stroke:#263238;stroke-miterlimit:10;" points="30.579,239.727 32.783,244.685 37.191,243.721 			"/>
			<path style="fill:#263238;stroke:#263238;stroke-miterlimit:10;" d="M39.808,306.807c0,0,1.24,0,1.102-3.444
				c-0.138-3.443-0.964-5.372-0.964-5.372l14.738-5.785c0,0,2.066,3.03,2.342,4.546c0.276,1.515-0.564,3.753-2.479,6.336
				c-2.06,2.778-5.973,4.06-10.055,3.719C40.928,306.51,39.808,306.807,39.808,306.807z"/>
		</g>
		<path style="fill:#737373;stroke:#263238;stroke-miterlimit:10;" d="M44.078,244.548c0,0,4.821-4.683,9.642-6.887
			c4.821-2.204,6.749-2.204,8.402-0.964c1.653,1.24,18.044,15.565,19.146,19.008c1.102,3.444,1.515,7.438,1.515,7.438
			s-13.223-18.595-17.631-21.075c-4.408-2.479-6.198-3.168-9.229-3.03c-3.03,0.138-9.642,8.127-9.642,8.127L44.078,244.548z"/>
		<path style="fill:#C9C9C9;stroke:#263238;stroke-miterlimit:10;" d="M76.989,265.149c0,0-1.108-5.678-0.831-7.062
			c0.277-1.385,2.493-2.493,2.493-2.493s2.631-1.8,3.739-2.354c1.108-0.554,1.662,0,1.939,0.969
			c0.277,0.969-0.139,1.662-0.139,1.662s2.493,2.354,2.908,4.57c0.415,2.216,1.246,3.877,0.277,5.816s-5.124,5.539-5.124,5.539
			l-2.354-0.692L76.989,265.149z"/>
		<path style="fill:#92E3A9;stroke:#263238;stroke-miterlimit:10;" d="M54.556,239.67c0,0-5.955,1.939-7.062,6.231
			c-1.108,4.293-4.016,28.388-4.016,35.45c0,7.062,2.908,8.586,4.708,8.863c1.8,0.277,17.171-5.678,22.987-9.139
			c5.816-3.462,11.078-9.278,11.078-9.278s-0.554-3.047-1.8-4.57c-1.246-1.523-3.462-2.077-3.462-2.077s-5.539,3.739-8.586,4.57
			c-3.047,0.831-7.893,1.662-7.893,1.662l5.954-15.925c0,0,2.216-6.231-0.831-9.555"/>
		<path style="fill:none;stroke:#263238;stroke-miterlimit:10;" d="M49.016,279.136c0,0,0.831-2.216,3.323-4.431
			c2.493-2.216,6.37-3.6,6.37-3.6"/>
	</g>
</g>
<g id="Floor">
	<line style="fill:none;stroke:#263238;stroke-miterlimit:10;" x1="734.695" y1="455" x2="14.031" y2="455"/>
</g>
</svg>
