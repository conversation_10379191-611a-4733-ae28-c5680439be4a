import { PartyRead } from "./party";
import { ContactPointRead } from "./contact-points";
import { IndividualRead } from "@/types/individuals";
export type PossessionStatus = "pending" | "completed" | "cancelled";

export interface VehiclePossessionBase {
  from_party_id: number;
  to_party_id: number;
  vehicle_id: number;
  handover_expected_datetime: string; // ISO 8601 datetime string
  handover_actual_datetime?: string | null;
  status: PossessionStatus;
}

export type VehiclePossessionCreate = VehiclePossessionBase;

export interface VehiclePossessionRead extends VehiclePossessionBase {
  to_party: PartyRead;
  from_party: PartyRead;
  id: number;
}
export interface VehiclePossessionUpdate extends VehiclePossessionBase {
  id: number;
}

export interface PartyWithContactRead {
  id: number;
  contact_points: ContactPointRead[];
  individual?: IndividualRead;
}

export interface VehiclePossessionReadWithContactRead {
  id: number;
  vehicle_id: number;
  handover_expected_datetime: string; // ISO datetime string
  handover_actual_datetime?: string | null;
  status: string; // Or use a union like: 'PENDING' | 'COMPLETED' | etc.
  from_party: PartyWithContactRead;
  to_party: PartyWithContactRead;
}

export interface VehiclePossessionWithContactRead
  extends VehiclePossessionBase {
  id: number;
  from_party: PartyWithContactRead;
  to_party: PartyWithContactRead;
  created_at: string;
}
