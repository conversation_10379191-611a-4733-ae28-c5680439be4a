export interface CompanyBase {
  party_id: number;
  registration_number?: string | null;
  registration_country?: string | null;
  registration_date?: string | null;
  alias: string;
}

export interface CompanyCreate extends CompanyBase {}

export interface CompanyRead extends CompanyBase {
  id: number;
  created_at: string;
  updated_at: string;
}
export interface CompanyUpdate extends CompanyBase {
  id: number;
}
