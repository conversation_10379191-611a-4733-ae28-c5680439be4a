export enum VerificationTypeEnum {
  AI = "AI",
  MANUAL = "Manual",
  API = "API",
}

export enum ComplianceRequirementStatusEnum {
  ACCEPTED = "accepted",
  REJECTED = "rejected",
  INCOMPLETE = "incomplete",
  PENDING = "pending",
}

export enum ReferenceTypeEnum {
  TAX_PIN = "tax_pin",
  URL = "url",
}

export interface TimestampMixin {
  created_at?: string;
  updated_at?: string;
}

export interface ComplianceSetBase {
  name: string;
  description?: string;
}

export interface ComplianceSetCreate extends ComplianceSetBase {}

export interface ComplianceSetRead extends ComplianceSetBase, TimestampMixin {
  id: number;
}

export interface ComplianceRequirementBase {
  party_id: number;
  compliance_set_id: number;
  requirement_type_id: number;
  verification_id?: number;
  reviewed_by_id?: number;
  status?: ComplianceRequirementStatusEnum;
  reference_type?: ReferenceTypeEnum;
  reference?: string;
  submitted_at?: string;
  reviewed_at?: string;
  notes?: string;
  issuing_authority_id?: number;
  issue_date?: string;
  expiry_date?: string;
  uploaded_individual_id?: number;
}

export interface ComplianceRequirementCreate
  extends ComplianceRequirementBase {}

export interface ComplianceRequirementRead
  extends ComplianceRequirementBase,
    TimestampMixin {
  id: number;
}

export interface PartyRead {
  id: number;
  name: string;
}

export interface ComplianceRequirementReadWithRelations
  extends ComplianceRequirementRead {
  party?: PartyRead;
  compliance_set?: ComplianceSetRead;
  requirement_type?: ComplianceRequirementTypeRead;
}

export interface VerificationBase {
  verification_type?: VerificationTypeEnum;
  verifying_party_id: number;
  verification_outcome: string;
  cost?: string;
}

export interface VerificationCreate extends VerificationBase {}

export interface VerificationRead extends VerificationBase, TimestampMixin {
  id: number;
}

export interface VerificationReadWithRelations extends VerificationRead {
  party?: PartyRead;
}

export interface IssuingAuthorityBase {
  name: string;
  description?: string;
  country?: string;
  party_id: number;
}

export interface IssuingAuthorityCreate extends IssuingAuthorityBase {}

export interface IssuingAuthorityRead
  extends IssuingAuthorityBase,
    TimestampMixin {
  id: number;
}

export interface ComplianceRequirementTypeBase {
  name: string;
  description?: string;
  default_validity_in_days: number;
}

export interface ComplianceRequirementTypeCreate
  extends ComplianceRequirementTypeBase {}

export interface ComplianceRequirementTypeRead
  extends ComplianceRequirementTypeBase,
    TimestampMixin {
  id: number;
}

export interface ComplianceSetRequirementMappingBase {
  compliance_set_id: number;
  compliance_requirement_id: number;
}

export interface ComplianceSetRequirementMappingCreate
  extends ComplianceSetRequirementMappingBase {}

export interface ComplianceSetRequirementMappingRead
  extends ComplianceSetRequirementMappingBase,
    TimestampMixin {
  id: number;
}

export interface ComplianceRequirementTypeReadWithRelations
  extends ComplianceRequirementTypeRead {
  compliance_set?: ComplianceSetRead;
  requirement_type?: ComplianceRequirementTypeRead;
}

export type Entity = {
  id: string;
  name: string;
  type: string;
  status: string;
  province: string;
  formationDate: string | null;
};

export type ComplianceItem = {
  id: number;
  entityId: string;
  type: ReferenceTypeEnum;
  description: string;
  dueDate: string;
  status: string;
  priority: string;
  fee: number;
  documentRequired: boolean;
  notes: string;
};

export type Document = {
  id: number;
  entityId: string;
  name?: string;
  type: string;
  date: string;
  status: string;
  expirationDate: string | null;
};
