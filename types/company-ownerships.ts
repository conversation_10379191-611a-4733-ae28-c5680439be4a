import { CompanyRead } from "./company";
import { PartyRead } from "./party";
import { IndividualRead } from "./individuals";
export interface CompanyOwnershipBase {
  party_id: number;
  company_id: number;
  fraction: number;
  effective_from: Date;
  effective_to?: Date | null;
  is_active: boolean;
}

export interface CompanyOwnershipCreate extends CompanyOwnershipBase {}

export interface CompanyOwnershipRead extends CompanyOwnershipBase {
  id: number;
  created_at: Date;
  updated_at: Date;
}

export interface CompanyOwnershipReadWithRelations
  extends CompanyOwnershipRead {
  party?: PartyRead | null;
  company?: CompanyRead | null;
}

export interface CompanyOwnershipUpdate extends CompanyOwnershipBase {
  id: number;
}

export interface IndividualWithFractionRead {
  individual: IndividualRead;
  fraction: number;
  company_id: number;
}
export interface CompanyMembershipRead {
  individuals: IndividualWithFractionRead[];
  companies: CompanyRead[];
}
