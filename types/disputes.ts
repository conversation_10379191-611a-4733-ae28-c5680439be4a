export enum DisputeType {
  BOOKING = "Booking",
  VEHICLE_DAMAGE = "Vehicle Damage",
  VEHICLE_MAINTENANCE = "Vehicle Maintenance",
  MAINTENANCE_COST_DISPUTE = "Maintanence Cost Dispute",
  OTHER = "Other",
}

export enum DisputeStatus {
  OPEN = "Open",
  RESOLVED = "Resolved",
  DELETED = "Deleted",
}

export enum Priority {
  LOW = "Low",
  MEDIUM = "Medium",
  HIGH = "High",
}

export interface DisputeMediaBase {
  media_path: string;
}

export interface DisputeMediaCreate extends DisputeMediaBase {
  dispute_id: number;
}

export interface DisputeMediaRead extends DisputeMediaBase {
  id: number;
  dispute_id: number;
  created_at: string;
}

export interface DisputeCommentBase {
  comment: string;
  reply_to_comment_id?: number | null;
}

export interface DisputeCommentCreate extends DisputeCommentBase {
  dispute_id: number;
  user_id: number;
}

export interface DisputeCommentRead extends DisputeCommentBase {
  id: number;
  dispute_id: number;
  user_id: number;
  created_at: string;
}
export interface DisputeCommentUpdate extends DisputeCommentBase {
  dispute_id: number;
  user_id: number;
}

export interface DisputeBase {
  name: string;
  description?: string | null;
  vehicle_id: number;
  dispute_type: DisputeType;
  party_offending: number;
  party_logging: number;
  dispute_status: DisputeStatus;
  priority: Priority;
  company_id: number;
}

export interface DisputeCreate extends DisputeBase {}

export interface DisputeUpdate extends DisputeBase {
  id: number;
}

export interface DisputeRead extends DisputeBase {
  created_date: string;
  id: number;
  comments: DisputeCommentRead[];
  media: DisputeMediaRead[];
}
export interface DisputeCreateWithMedia extends DisputeCreate {
  media: string[];
}
