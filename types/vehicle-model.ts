export interface VehicleModelBase {
    make_id: number;
    model: string;
    year_model: number;
    description?: string | null;
    transmission?: string | null;
    is_active: boolean;
}

export interface VehicleModelCreate extends VehicleModelBase {}

export interface VehicleModelRead extends VehicleModelBase {
    id: number;
    created_at: Date;
    updated_at: Date;
}

export interface VehicleModelReadWithMake extends VehicleModelRead {
    make: VehicleMakeRead | null;
}

export interface VehicleMakeRead {
    id: number;
    model: string;
    country_of_origin: string;
}
