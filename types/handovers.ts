export interface FullInspection {
  vehicle_id: number;
  handing_over: boolean;
  scratches: string;
  dents: string;
  tires: string;
  lights: string;
  cleanliness: string;
  seats: string;
  dashboard_controls: string;
  odors: string;
  odometer: number;
  known_issues?: string; // Optional
}

export interface FullMediaItem {
  type: string; // e.g., "front_view", "left_view", etc.
  url: string; // e.g., "vehicle/<filename>.jpeg"
}

export interface Possession {
  from_party_id: number;
  to_party_id: number;
  vehicle_id: number;
  status: string; // e.g., "completed"
}

export interface FullInspectionData {
  inspection: FullInspection;
  media: FullMediaItem[];
  possession: Possession;
}

export interface FullInspectionDataRead {
  inspection: FullInspection;
  possession: Possession;
}

export interface VehicleInspectionCreate {}
export interface VehicleInspectionRead {}
export interface VehicleInspectionReadWithPhotos {}
