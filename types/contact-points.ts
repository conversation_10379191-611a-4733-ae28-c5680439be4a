import type { TimestampMixin } from "./base";
import type { ContactPointTypeRead } from "./contact-point-types";
export interface ContactPointBase {
  party_id: number;
  contact_point_type_id: number;
  value: string;
  address_type_id?: number;
  is_primary: boolean;
  is_verified: boolean;
  verification_date?: string;
  verification_method?: string;
  mtadata?: any;
}

export interface ContactPointCreate extends ContactPointBase {}

export interface ContactPointRead extends ContactPointBase, TimestampMixin {
  id: number;
  contact_point_type: ContactPointTypeRead;
}
