export interface IdentificationTypeBase {
    name: string;
    description?: string;
    validation_pattern?: string;
    expiration_required?: boolean;
    is_active?: boolean;
  }
  
  export interface IdentificationTypeCreate extends IdentificationTypeBase {}
  
  export interface IdentificationTypeUpdate extends IdentificationTypeBase {}
  
  export interface IdentificationTypeRead extends IdentificationTypeBase {
    id: number;
    created_at: string;
    updated_at: string;
  }
  