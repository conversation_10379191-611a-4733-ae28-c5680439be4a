export enum VehicleServiceStatus {
  SCHEDULED = "Scheduled",
  PENDING = "Pending",
  COMPLETED = "Completed",
}

export interface VehicleMaintenanceBase {
  vehicle_id: number;
  name: string;
  description?: string;
  due_date: string; // ISO date string
  due_odometer: number;
  status: VehicleServiceStatus;
  expected_cost: number;
  completed_date?: string; // ISO date string
  completed_odometer?: number;
  actual_cost?: number;
  technician_notes?: string;
  service_provider?: string;
  is_scheduled?: boolean;
}

export type VehicleMaintenanceCreate = VehicleMaintenanceBase;

export interface TimestampMixin {
  created_at: string;
  updated_at: string;
}

export interface VehicleMaintenanceRead
  extends VehicleMaintenanceBase,
    TimestampMixin {
  id: number;
}

export interface VehicleMaintenanceUpdate {
  name?: string;
  description?: string;
  due_date?: string;
  due_odometer?: number;
  status?: VehicleServiceStatus;
  expected_cost?: number;
  completed_date?: string;
  completed_odometer?: number;
  actual_cost?: number;
  technician_notes?: string;
  service_provider?: string;
  is_scheduled?: boolean;
}

export interface MaintenanceItem {
  id: number;
  type: string;
  dueDate: string;
  dueKm: string;
  description: string;
  estimatedCost: string;
  status: "scheduled" | "pending" | "completed";
  provider: string;
  odometer: string;
}
export interface CategorizedMaintenance {
  upcomingMaintenance: MaintenanceItem[];
  maintenanceHistory: MaintenanceItem[];
}
