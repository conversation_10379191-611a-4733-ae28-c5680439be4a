export enum ServiceTypeEnum {
  Maintenance = "MAINTENANCE",
  Insurance = "INSURANCE",
  Cleaning = "CLEANING",
  Fuel = "FUEL",
  Other = "OTHER",
}

export interface ServiceProviderBase {
  name: string;
  description?: string;
  service_type: ServiceTypeEnum;
  effective_from: string;
  effective_to: string;
}

export interface ServiceProviderCreate extends ServiceProviderBase {}

export interface ServiceProviderRead extends ServiceProviderBase {
  id: number;
}
