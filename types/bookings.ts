import { TimestampMixin } from "./base";
export enum BookingStatus {
  PENDING = "Pending",
  CONFIRMED = "Confirmed",
  CANCELLED = "Cancelled",
  COMPLETED = "Completed",
}

export interface BookingBase  {
  vehicle_id: number;
  reference: string;
  start_datetime: string; // ISO string date
  end_datetime: string; // ISO string date
  status: BookingStatus;
  total_price?: number | null;
  notes?: string | null;
  party_id: number;
}

export interface BookingCreate extends BookingBase {}

export interface BookingUpdate {
  vehicle_id: number;
  reference: string;
  start_datetime: string;
  end_datetime: string;
  status?: BookingStatus | null;
  total_price?: number | null;
  notes?: string | null;
  party_id: number;
}

export interface BookingRead extends BookingBase, TimestampMixin {
  id: number;
}
