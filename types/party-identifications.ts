import { IdentificationTypeRead } from "./identification-types";
export interface PartyIdentificationBase {
  party_id: number;
  identification_type_id: number;
  document_number: string;
  issuing_authority?: string | null;
  issue_date?: string | null;
  expiry_date?: string | null;
  is_verified: boolean;
  verification_date?: string | null;
  verification_method?: string | null;
  document_image_url?: string | null;
}

export interface PartyIdentificationCreate extends PartyIdentificationBase {
  party_id: number;
}

export interface PartyIdentificationCreateFull {
  identification_type_id: number;
  document_number: string;
  issuing_authority?: string | null;
  issue_date?: string | null;
  expiry_date?: string | null;
  is_verified: boolean;
  verification_date?: string | null;
  verification_method?: string | null;
  document_image_url?: string | null;
}

export interface TimestampMixin {
  created_at: string;
  updated_at: string;
}

export interface PartyIdentificationRead
  extends PartyIdentificationBase,
    TimestampMixin {
  id: number;
  identification_type: IdentificationTypeRead;
}
