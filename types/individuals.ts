export type IndividualRead = {
  username: string;
  email: string;
  id: number;
  party_id: number;
  first_name: string;
  last_name: string;
  birth_date: string;
  middle_name?: string | null;
  created_at: string;
};

export interface IndividualFullCreate {
  first_name: string;
  last_name: string;
  middle_name?: string | null;
  salutation?: string | null;
  suffix?: string | null;
  gender?: string | null;
  birth_date?: string | null;
  marital_status?: string | null;
  nationality?: string | null;
  preferred_language?: string | null;

  party_type_id: number;
  party_status_id: number;
  external_id: string;

  contact_points: ContactPointCreateFull[];
  identifications: PartyIdentificationCreateFull[];
}

export interface ContactPointCreateFull {
  contact_point_type_id: number;
  value: string;
  address_type_id?: number | null;
  is_primary: boolean;
  is_verified?: boolean;
  verification_date?: string | null;
  verification_method?: string | null;
  mtadata?: any;
}

export interface PartyIdentificationCreateFull {
  identification_type_id: number;
  document_number: string;
  issuing_authority?: string | null;
  issue_date?: string | null;
  expiry_date?: string | null;
  is_verified?: boolean;
  verification_date?: string | null;
  verification_method?: string | null;
  document_image_url?: string | null;
}

export interface IndividualFullUpdate {
  first_name: string;
  last_name: string;
  middle_name?: string;
  salutation?: string;
  suffix?: string;
  gender?: string;
  birth_date?: string;
  marital_status?: string;
  nationality?: string;
  preferred_language?: string;
  contact_points: ContactPointCreateFull[];
  identifications: PartyIdentificationCreateFull[];
  id?: number;
}
