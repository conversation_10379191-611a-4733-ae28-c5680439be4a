"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  VehicleDocumentCreate,
  VehicleDocumentRead,
  VehicleDocumentBase,
} from "@/types/vehicles";
import { revalidatePath } from "next/cache";
import { apiErrorFormater } from "@/lib/utils";

export async function createVehicleDocument(
  vehicleDocument: VehicleDocumentCreate
): Promise<VehicleDocumentRead> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch("/api/vehicle-documents", {
    method: "POST",
    body: JSON.stringify(vehicleDocument),
    headers: { "Content-Type": "application/json" },
  });
  return response.json();
}

export async function getVehicleDocument(
  id: number
): Promise<VehicleDocumentRead> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch(`/api/vehicle-documents/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function updateVehicleDocument(
  vehicleDocument: VehicleDocumentRead
): Promise<VehicleDocumentRead> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch(`/api/vehicle-documents/${vehicleDocument.id}`, {
    method: "PUT",
    body: JSON.stringify(vehicleDocument),
    headers: { "Content-Type": "application/json" },
  });
  return response.json();
}

export async function deleteVehicleDocument(id: number): Promise<void> {
  const fetch = await getAuthorizedFetch();
  await fetch(`/api/vehicle-documents/${id}`, {
    method: "DELETE",
  });
}

export async function addVehicleDocument(_: any, formData: FormData) {
  const vehicleID = Number(formData.get("vehicleID"));
  const documentImageUrl = (formData.get("documentUrl") as string)?.trim();
  const documentType = (formData.get("documentType") as string)?.trim();

  const errors = [];

  if (!vehicleID) errors.push("Select a vehicle");
  if (!documentImageUrl) errors.push("File required");
  if (!documentType) errors.push("Select a document type");

  const vehicleDocument = {
    vehicle_id: vehicleID,
    media_path: documentImageUrl,
    document_type: documentType,
  } as VehicleDocumentBase;
  try {
    await createVehicleDocument(vehicleDocument);
    revalidatePath("/vehicle-dashboard");
    return { success: true, message: "Document uploaded successfully." };
  } catch (error: any) {
    return apiErrorFormater(error);
  }
}
