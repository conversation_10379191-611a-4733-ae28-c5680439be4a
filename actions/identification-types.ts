"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type { IdentificationTypeRead } from "@/types/identification-types";

export async function getIdentificationTypes(): Promise<IdentificationTypeRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/identification-types/", {
    method: "GET",
  });
  return response.json();
}