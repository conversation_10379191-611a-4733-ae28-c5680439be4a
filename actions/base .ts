export function serializeParams(params: Record<string, any>): string {
  const query = new URLSearchParams();

  for (const key in params) {
    const value = params[key];
    if (Array.isArray(value)) {
      // For arrays, add repeated params without indices
      value.forEach((v) => query.append(key, String(v)));
    } else if (value !== undefined && value !== null) {
      query.append(key, String(value));
    }
  }

  return query.toString();
}