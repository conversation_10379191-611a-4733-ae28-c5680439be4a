import { getAuthorizedFetch } from "@/lib/fetchClient";
import type { AddressTypeRead } from "@/types/address-type";

export async function getAddressTypes(): Promise<AddressTypeRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    "/api/address-types/",
    {
      method: "GET",
    },
    {
      revalidate: 3600,
    }
  );
  return response.json();
}
