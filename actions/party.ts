"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type { PartyRead } from "@/types/party";

export async function getPartyByExternalID(
  externalId: string
): Promise<PartyRead> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch(`/api/parties/by-external-id/${externalId}`, {
    method: "GET",
  });
  return response.json();
}

export async function getParty(party_id: number): Promise<PartyRead> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch(`/api/parties/${party_id}`, {
    method: "GET",
  });
  return response.json();
}
