"use server";

import { getCurrentUser } from "aws-amplify/auth/server";
import { runWithAmplifyServerContext } from "@/lib/amplifyServerUtils";
import { cookies } from "next/headers";
import { 
  createListingInterestExpressionDrizzle, 
  deleteListingInterestExpressionDrizzle,
  checkUserInterestDrizzle,
  getListingInterestExpressionsDrizzle,
  type ListingInterestExpressionRead 
} from "@/drizzle-actions/listing-interest";

// Get user's party ID from authentication
async function getCurrentUserPartyId(): Promise<number> {
  const user = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: (contextSpec) => getCurrentUser(contextSpec),
  });

  const dbId = user.signInDetails?.loginId || user.userId;
  if (!dbId) {
    throw new Error("User not authenticated");
  }

  // Extract party ID from custom:db_id attribute
  const partyId = parseInt(dbId);
  if (isNaN(partyId)) {
    throw new Error("Invalid user party ID");
  }

  return partyId;
}

// Express interest in a listing
export async function expressInterestInListing(listingId: number): Promise<void> {
  try {
    const partyId = await getCurrentUserPartyId();
    await createListingInterestExpressionDrizzle({ listingId, partyId });
  } catch (error) {
    console.error("Error expressing interest:", error);
    throw error;
  }
}

// Remove interest from a listing
export async function removeInterestFromListing(listingId: number): Promise<void> {
  try {
    const partyId = await getCurrentUserPartyId();
    await deleteListingInterestExpressionDrizzle(listingId, partyId);
  } catch (error) {
    console.error("Error removing interest:", error);
    throw error;
  }
}

// Check if current user has expressed interest
export async function checkCurrentUserInterest(listingId: number): Promise<boolean> {
  try {
    const partyId = await getCurrentUserPartyId();
    return await checkUserInterestDrizzle(listingId, partyId);
  } catch (error) {
    console.error("Error checking user interest:", error);
    return false;
  }
}

// Get all interest expressions for a listing (for listing authors)
export async function getListingInterestExpressions(listingId: number): Promise<ListingInterestExpressionRead[]> {
  try {
    return await getListingInterestExpressionsDrizzle(listingId);
  } catch (error) {
    console.error("Error fetching interest expressions:", error);
    throw error;
  }
} 