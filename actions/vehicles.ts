"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  VehicleCreate,
  VehicleRead,
  VehicleUpdate,
  VehicleReadWithModelAndParty,
} from "@/types/vehicles";

const BASE_URL = "/api/vehicles";

export async function createVehicle(
  vehicle: VehicleCreate
): Promise<VehicleRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(BASE_URL, {
    method: "POST",
    body: JSON.stringify(vehicle),
  });
  return response.json();
}

export async function getVehicle(
  id: number
): Promise<VehicleReadWithModelAndParty> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function getVehicles(): Promise<VehicleRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(BASE_URL, {
    method: "GET",
  });
  return response.json();
}

export async function updateVehicle(
  vehicle: VehicleUpdate
): Promise<VehicleRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${vehicle.id}`, {
    method: "PUT",
    body: JSON.stringify(vehicle),
  });
  return response.json();
}

export async function getVehiclesByParty(
  partyId: number
): Promise<VehicleRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/by-party/${partyId}`, {
    method: "GET",
  });
  return response.json();
}

export async function getVehiclesByParties(
  partyIds: number[]
): Promise<VehicleReadWithModelAndParty[]> {
  const authorizedFetch = await getAuthorizedFetch();

  const params = new URLSearchParams();
  partyIds.forEach((id) => params.append("party_ids", id.toString()));

  const response = await authorizedFetch(
    `${BASE_URL}/by-party/ids?${params.toString()}`,
    {
      method: "GET",
    },
    {
      revalidate: 3600,
    }
  );
  return response.json();
}
