"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  VehiclePossessionCreate,
  VehiclePossessionRead,
  VehiclePossessionBase,
  VehiclePossessionWithContactRead,
} from "@/types/vehicle-possessions";

const BASE_URL = "/api/vehicle-possessions";

export async function createVehiclePossession(
  possession: VehiclePossessionCreate
): Promise<VehiclePossessionRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(BASE_URL, {
    method: "POST",
    body: JSON.stringify(possession),
  });
  return response.json();
}

export async function getVehiclePossession(
  id: number
): Promise<VehiclePossessionRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function getAllVehiclePossessions(): Promise<
  VehiclePossessionRead[]
> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(BASE_URL, {
    method: "GET",
  });
  return response.json();
}

export async function updateVehiclePossession(
  id: number,
  possessionUpdate: VehiclePossessionBase
): Promise<VehiclePossessionRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${id}`, {
    method: "PUT",
    body: JSON.stringify(possessionUpdate),
  });
  return response.json();
}

export async function deleteVehiclePossession(
  id: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${id}`, {
    method: "DELETE",
  });
  return response.json();
}

export async function getVehiclePossessionsByVehicle(
  vehicleId: number
): Promise<VehiclePossessionWithContactRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `${BASE_URL}/vehicle/by-vehicle/${vehicleId}`,
    {
      method: "GET",
    }
  );
  return response.json();
}

export async function getVehiclePossessionByVehicle(
  vehicleId: number,
  possessionId: number
): Promise<VehiclePossessionWithContactRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `${BASE_URL}/vehicle/by-vehicle/${vehicleId}/${possessionId}`,
    {
      method: "GET",
    }
  );
  return response.json();
}
