"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  CompanyCreate,
  CompanyRead,
  CompanyUpdate,
} from "@/types/company";

export async function createCompany(
  company: CompanyCreate
): Promise<CompanyRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/companies", {
    method: "POST",
    body: JSON.stringify(company),
  });
  return response.json();
}

export async function getCompany(id: number): Promise<CompanyRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/companies/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function updateCompany(
  company: CompanyUpdate
): Promise<CompanyRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/companies/${company.id}`, {
    method: "PUT",
    body: JSON.stringify(company),
  });
  return response.json();
}

export async function getCompanies(): Promise<CompanyRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    "/api/companies",
    {
      method: "GET",
    },
    {
      revalidate: 3600,
    }
  );
  return response.json();
}
