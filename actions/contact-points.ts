"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  ContactPointRead,
  ContactPointBase,
  ContactPointCreate,
} from "@/types/contact-points";

export async function getContactPoints(): Promise<ContactPointRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/contact-points/", {
    method: "GET",
  });
  return response.json();
}

export async function getContactPointById(
  id: number
): Promise<ContactPointRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/contact-points/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function createContactPoint(
  data: ContactPointCreate
): Promise<ContactPointRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/contact-points/", {
    method: "POST",
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function updateContactPoint(
  id: number,
  data: ContactPointBase
): Promise<ContactPointRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/contact-points/${id}`, {
    method: "PUT",
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function deleteContactPoint(
  id: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/contact-points/${id}`, {
    method: "DELETE",
  });
  return response.json();
}

export async function getContactPointsByPartyId(
  partyId: number
): Promise<ContactPointRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/contact-points/by-party/id/${partyId}`,
    {
      method: "GET",
    }
  );
  return response.json();
}

export async function getContactPointsByPartyIds(
  partyIds: number[]
): Promise<ContactPointRead[]> {
  const authorizedFetch = await getAuthorizedFetch();

  // Serialize the partyIds array as query parameters like ?party_ids=1&party_ids=2...
  const query = new URLSearchParams();
  partyIds.forEach((id) => query.append("party_ids", id.toString()));

  const response = await authorizedFetch(
    `/api/contact-points/by/party/ids/?${query.toString()}`,
    {
      method: "GET",
    }
  );
  return response.json();
}
