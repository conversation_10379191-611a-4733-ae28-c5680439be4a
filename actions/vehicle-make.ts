"use server";

import { getAuthorizedAxios } from "@/lib/axiosClient";
import type {
  VehicleMakeCreate,
  VehicleMakeRead,
  VehicleMakeUpdate,
} from "@/types/vehicle-makes";

const BASE_URL = "/api/vehicle-makes";

export async function createVehicleMake(
  vehicleMake: VehicleMakeCreate
): Promise<VehicleMakeRead> {
  const axios = await getAuthorizedAxios();
  const response = await axios.post(BASE_URL, vehicleMake);
  return response.data;
}

export async function getVehicleMake(id: number): Promise<VehicleMakeRead> {
  const axios = await getAuthorizedAxios();
  const response = await axios.get(`${BASE_URL}/${id}`);
  return response.data;
}

export async function getVehicleMakes(): Promise<VehicleMakeRead[]> {
  const axios = await getAuthorizedAxios();
  const response = await axios.get(BASE_URL);
  return response.data;
}

export async function updateVehicleMake(
  vehicleMake: VehicleMakeUpdate
): Promise<VehicleMakeRead> {
  const axios = await getAuthorizedAxios();
  const response = await axios.put(`${BASE_URL}/${vehicleMake.id}`, vehicleMake);
  return response.data;
}
