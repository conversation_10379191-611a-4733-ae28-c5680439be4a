"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  ComplianceRequirementTypeCreate,
  ComplianceRequirementTypeRead,
} from "@/types/compliance";

export async function getComplianceRequirementTypes(): Promise<
  ComplianceRequirementTypeRead[]
> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/compliance-requirement-types/", {
    method: "GET",
  });
  return response.json();
}

export async function getComplianceRequirementType(
  id: number
): Promise<ComplianceRequirementTypeRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/compliance-requirement-types/${id}`,
    {
      method: "GET",
    }
  );
  return response.json();
}

export async function createComplianceRequirementType(
  payload: ComplianceRequirementTypeCreate
): Promise<ComplianceRequirementTypeRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/compliance-requirement-types/", {
    method: "POST",
    body: JSON.stringify(payload),
  });
  return response.json();
}

export async function updateComplianceRequirementType(
  id: number,
  payload: ComplianceRequirementTypeCreate
): Promise<ComplianceRequirementTypeRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/compliance-requirement-types/${id}`,
    {
      method: "PUT",
      body: JSON.stringify(payload),
    }
  );
  return response.json();
}

export async function deleteComplianceRequirementType(
  id: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/compliance-requirement-types/${id}`,
    {
      method: "DELETE",
    }
  );
  return response.json();
}
