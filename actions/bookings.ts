"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  BookingCreate,
  BookingRead,
  BookingStatus,
  BookingUpdate,
} from "@/types/bookings";
import { apiErrorFormater } from "@/lib/utils";

export async function createBooking(
  booking: BookingCreate
): Promise<BookingRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/bookings", {
    method: "POST",
    body: JSON.stringify(booking),
  });
  return response.json();
}

export async function getBooking(id: number): Promise<BookingRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/bookings/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function updateBooking(
  id: number,
  booking: BookingUpdate
): Promise<BookingRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/bookings/${id}`, {
    method: "PUT",
    body: JSON.stringify(booking),
  });
  return response.json();
}

export async function deleteBooking(id: number): Promise<void> {
  const authorizedFetch = await getAuthorizedFetch();
  await authorizedFetch(`/api/bookings/${id}`, {
    method: "DELETE",
  });
}

export async function getBookingsByVehicle(
  vehicleId: number
): Promise<BookingRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/bookings/by-vehicle/id/${vehicleId}`,
    {
      method: "GET",
    }
  );
  return response.json();
}

export async function getBookingsByParty(
  party_id: number
): Promise<BookingRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/bookings/by-party/id/${party_id}`,
    {
      method: "GET",
    }
  );
  return response.json();
}
export async function addBooking(_: any, formData: FormData) {
  try {
    const start_datetime = (formData.get("start_datetime") as string)?.trim();
    const end_datetime = (formData.get("end_datetime") as string)?.trim();
    const status = (formData.get("status") as string)?.trim();
    const reference = (formData.get("reference") as string)?.trim();
    const vehicle_id = Number(formData.get("vehicle_id"));
    const party_id = Number(formData.get("party_id"));
    const bookingData = {
      vehicle_id,
      reference,
      start_datetime,
      end_datetime,
      party_id,
      status: status as BookingStatus,
    };
    const booking = await createBooking(bookingData);
    return {
      success: true,
      message: "Booking created successfully.",
      bookingID: booking.id,
    };
  } catch (error: any) {
    return apiErrorFormater(error);
  }
}
