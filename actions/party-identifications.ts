"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  PartyIdentificationCreate,
  PartyIdentificationRead,
} from "@/types/party-identifications";

export async function getPartyIdentificationsByParty(
  party_id: number
): Promise<PartyIdentificationRead[]> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch(`/api/party-identifications/by-party/${party_id}`, {
    method: "GET",
  });
  return response.json();
}

export async function postPartyIdentifications(
  partyIdentification: PartyIdentificationCreate
): Promise<PartyIdentificationRead[]> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch("/api/party-identifications", {
    method: "POST",
    body: JSON.stringify(partyIdentification),
  });
  return response.json();
}

export async function createPartyIdentifications(_: any, formData: FormData) {
  const partyId = Number(formData.get("partyId"));
  const documentImageUrl = (formData.get("documentUrl") as string)?.trim();
  const identificationTypeId = Number(formData.get("identificationTypeId"));
  const documentNumber = (formData.get("documentNumber") as string)?.trim();

  const errors = [];

  if (!identificationTypeId) errors.push("Select the identification type");
  if (!documentImageUrl) errors.push("File required");
  if (!partyId) errors.push("File Party Required");

  if (errors.length > 0) {
    return { success: false, errors };
  }

  const partyIdentification: PartyIdentificationCreate = {
    party_id: partyId,
    identification_type_id: identificationTypeId,
    document_image_url: documentImageUrl,
    document_number: documentNumber,
    is_verified: false,
  };

  try {
    await postPartyIdentifications(partyIdentification);
    return { success: true, message: "Document uploaded successfully." };
  } catch (error: any) {
    errors.push(error?.details || "An error occurred");
    console.error("Error creating party identification:", error);
    return { success: false, errors };
  }
}
