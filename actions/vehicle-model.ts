"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  VehicleModelCreate,
  VehicleModelRead,
  VehicleModelReadWithMake,
} from "@/types/vehicle-model";

export async function createVehicleModel(
  vehicleModel: VehicleModelCreate
): Promise<VehicleModelRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/vehicle-models", {
    method: "POST",
    body: JSON.stringify(vehicleModel),
  });
  return response.json();
}

export async function getVehicleModel(id: number): Promise<VehicleModelRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/vehicle-models/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function getVehicleModels(): Promise<VehicleModelReadWithMake[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/vehicle-models", {
    method: "GET",
  });
  return response.json();
}

export async function getVehicleModelsByMake(
  makeId: number
): Promise<VehicleModelReadWithMake[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/vehicle-models/by-make/${makeId}`,
    {
      method: "GET",
    }
  );
  return response.json();
}

export async function updateVehicleModel(
  vehicleModel: VehicleModelRead
): Promise<VehicleModelRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/vehicle-models/${vehicleModel.id}`,
    {
      method: "PUT",
      body: JSON.stringify(vehicleModel),
    }
  );
  return response.json();
}

export async function getVehicleModelWithMake(
  id: number
): Promise<VehicleModelReadWithMake> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/vehicle-models/${id}/with-make`,
    {
      method: "GET",
    }
  );
  return response.json();
}
