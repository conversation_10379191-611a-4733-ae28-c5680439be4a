"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  VehicleInspectionCreate,
  VehicleInspectionRead,
  VehicleInspectionReadWithPhotos,
  FullInspectionData,
  FullInspectionDataRead,
} from "@/types/handovers";

export async function createVehicleInspection(
  inspection: VehicleInspectionCreate
): Promise<VehicleInspectionRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/vehicle-inspections", {
    method: "POST",
    body: JSON.stringify(inspection),
  });
  return response.json();
}

export async function getVehicleInspection(
  inspectionId: number
): Promise<VehicleInspectionReadWithPhotos> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/vehicle-inspections/${inspectionId}`, {
    method: "GET",
  });
  return response.json();
}

export async function getAllVehicleInspections(): Promise<
  VehicleInspectionReadWithPhotos[]
> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/vehicle-inspections", {
    method: "GET",
  });
  return response.json();
}

export async function updateVehicleInspection(
  inspectionId: number,
  inspectionUpdate: VehicleInspectionCreate
): Promise<VehicleInspectionRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/vehicle-inspections/${inspectionId}`, {
    method: "PUT",
    body: JSON.stringify(inspectionUpdate),
  });
  return response.json();
}

export async function deleteVehicleInspection(
  inspectionId: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/vehicle-inspections/${inspectionId}`, {
    method: "DELETE",
  });
  return response.json();
}

export async function createInspectionWithMedia(
  data: FullInspectionData
): Promise<FullInspectionDataRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/vehicle-inspections/create/with/media", {
    method: "POST",
    body: JSON.stringify(data),
  });
  return response.json();
}
