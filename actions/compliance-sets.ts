"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  ComplianceSetBase,
  ComplianceSetCreate,
  ComplianceSetRead,
} from "@/types/compliance";

const BASE_URL = "/api/compliance-sets";

export async function createComplianceSet(
  data: ComplianceSetCreate
): Promise<ComplianceSetRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(BASE_URL, {
    method: "POST",
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function getComplianceSetById(
  id: number
): Promise<ComplianceSetRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function getAllComplianceSets(): Promise<ComplianceSetRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(BASE_URL, {
    method: "GET",
  });
  return response.json();
}

export async function updateComplianceSet(
  id: number,
  data: ComplianceSetBase
): Promise<ComplianceSetRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${id}`, {
    method: "PUT",
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function deleteComplianceSet(
  id: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${id}`, {
    method: "DELETE",
  });
  return response.json();
}
