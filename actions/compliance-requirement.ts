"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  ComplianceRequirementCreate,
  ComplianceRequirementRead,
  ComplianceRequirementReadWithRelations,
} from "@/types/compliance";

export async function getComplianceRequirements(): Promise<
  ComplianceRequirementRead[]
> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/compliance-requirements/", {
    method: "GET",
  });
  return response.json();
}

export async function getComplianceRequirementById(
  id: number
): Promise<ComplianceRequirementRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/compliance-requirements/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function createComplianceRequirement(
  payload: ComplianceRequirementCreate
): Promise<ComplianceRequirementRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/compliance-requirements/", {
    method: "POST",
    body: JSON.stringify(payload),
  });
  return response.json();
}

export async function updateComplianceRequirement(
  id: number,
  payload: ComplianceRequirementCreate
): Promise<ComplianceRequirementRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/compliance-requirements/${id}`, {
    method: "PUT",
    body: JSON.stringify(payload),
  });
  return response.json();
}

export async function deleteComplianceRequirement(
  id: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/compliance-requirements/${id}`, {
    method: "DELETE",
  });
  return response.json();
}

export async function getComplianceRequirementByPartyId(
  party_id: number
): Promise<ComplianceRequirementReadWithRelations[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/compliance-requirements/by-party/id/${party_id}`,
    {
      method: "GET",
    },
    {
      revalidate: 3600,
    }
  );
  return response.json();
}
