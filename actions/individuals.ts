"use server";
import { getAuthorizedFetch } from "@/lib/fetchClient";
import { apiErrorFormater } from "@/lib/utils";
import { profileSetupSchema } from "@/schemas/profile";
import type {
  IndividualFullCreate,
  IndividualFullUpdate,
  IndividualRead,
} from "@/types/individuals";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { serializeParams } from "./base ";

export async function createIndividualFull(individual: IndividualFullCreate) {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/individuals/full", {
    method: "POST",
    body: JSON.stringify(individual),
  });
  return response.json();
}

export async function updateIndividualFull(
  id: number,
  individual: IndividualFullUpdate
) {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/individuals/full/${id}`, {
    method: "PUT",
    body: JSON.stringify(individual),
  });
  return response.json();
}

export async function getIndividuals(): Promise<IndividualRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/individuals/", {
    method: "GET",
  });
  return response.json();
}

export async function getIndividualByPartyId(
  partyId: number
): Promise<IndividualRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/individuals/by-party/id/${partyId}`,
    {
      method: "GET",
    },
    {
      revalidate: 3600,
    }
  );
  return response.json();
}

export async function getIndividual(id: number): Promise<IndividualRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/individuals/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function getIndividualsByParties(
  partyIds: number[]
): Promise<IndividualRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const queryString = serializeParams({ party_ids: partyIds });
  const response = await authorizedFetch(
    `/api/individuals/by-party/ids?${queryString}`,
    {
      method: "GET",
    }
  );
  return response.json();
}

export async function createProfile(_: any, formData: FormData) {
  try {
    const dateOfBirth = (formData.get("dateOfBirth") as string)?.trim();
    const idNumber = (formData.get("idNumber") as string)?.trim();
    const sub = (formData.get("sub") as string)?.trim();
    const address = (formData.get("address") as string)?.trim();
    const email = (formData.get("email") as string)?.trim();
    const phone = (formData.get("phone") as string)?.trim();
    const firstName = (formData.get("firstName") as string)?.trim();
    const lastName = (formData.get("lastName") as string)?.trim();
    const image_url = (formData.get("image") as string)?.trim();
    const activePartyTypeId = Number(formData.get("activePartyTypeId"));
    const emailId = Number(formData.get("emailId"));
    const phoneId = Number(formData.get("phoneId"));
    const addressId = Number(formData.get("addressId"));
    const profilePicId = Number(formData.get("profilePicId"));
    const nationalId = Number(formData.get("nationalId"));
    const mainAddressId = Number(formData.get("mainAddressId"));
    const activePartyStatusId = Number(formData.get("activePartyStatusId"));

    const individual = {
      first_name: firstName,
      last_name: lastName,
      birth_date: dateOfBirth,
      party_type_id: activePartyTypeId,
      party_status_id: activePartyStatusId,
      external_id: sub,
      contact_points: [
        ...(email
          ? [
              {
                contact_point_type_id: emailId,
                value: email,
                is_primary: true,
              },
            ]
          : []),
        ...(phone
          ? [
              {
                contact_point_type_id: phoneId,
                value: phone,
                is_primary: true,
              },
            ]
          : []),
        ...(address
          ? [
              {
                contact_point_type_id: addressId,
                value: address,
                address_type_id: mainAddressId,
                is_primary: true,
              },
            ]
          : []),
      ],
      identifications: [
        {
          identification_type_id: nationalId,
          document_number: idNumber,
        },
        {
          identification_type_id: profilePicId,
          document_number: sub,
          document_image_url: image_url,
        },
      ],
    };

    try {
      await createIndividualFull(individual);
    } catch (error: any) {
      return apiErrorFormater(error);
    }
  } catch (error: any) {
    return {
      errors: {
        form: [
          error?.detail || "Could not update profile. Please try again later.",
        ],
      },
    };
  }
  redirect("/home");
}

export async function updateProfile(_: any, formData: FormData) {
  try {
    const dateOfBirth = (formData.get("dateOfBirth") as string)?.trim();
    const address = (formData.get("address") as string)?.trim();
    const email = (formData.get("email") as string)?.trim();
    const phone = (formData.get("phone") as string)?.trim();
    const firstName = (formData.get("firstName") as string)?.trim();
    const lastName = (formData.get("lastName") as string)?.trim();
    const emailId = Number(formData.get("emailId"));
    const phoneId = Number(formData.get("phoneId"));
    const addressId = Number(formData.get("addressId"));
    const addressTypeId = Number(formData.get("addressTypeId"));
    const contactPointEmailId = Number(formData.get("contactPointEmailId"));
    const contactPointPhoneId = Number(formData.get("contactPointPhoneId"));
    const contactPointAddressId = Number(formData.get("contactPointAddressId"));
    const id = Number(formData.get("id"));
    const data = Object.fromEntries(formData.entries());
    const result = profileSetupSchema.safeParse(data);
    if (!result.success) {
      return {
        errors: result.error.flatten().fieldErrors,
      };
    }

    const individual = {
      first_name: firstName,
      last_name: lastName,
      birth_date: dateOfBirth,
      identifications: [],
      contact_points: [
        ...(email
          ? [
              {
                contact_point_type_id: contactPointEmailId,
                value: email,
                is_primary: true,
                id: emailId,
              },
            ]
          : []),
        ...(phone
          ? [
              {
                contact_point_type_id: contactPointPhoneId,
                value: phone,
                is_primary: true,
                id: phoneId,
              },
            ]
          : []),
        ...(address
          ? [
              {
                contact_point_type_id: contactPointAddressId,
                value: address,
                address_type_id: addressTypeId,
                is_primary: true,
                id: addressId,
              },
            ]
          : []),
      ],
    };
    try {
      await updateIndividualFull(id, individual);
    } catch (error: any) {
      return apiErrorFormater(error);
    }
    revalidatePath("/profile/personal");
    return { success: true };
  } catch (error: any) {
    return {
      errors: {
        form: [
          error?.detail || "Could not update profile. Please try again later.",
        ],
      },
    };
  }
}
