"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type { DisputeMediaCreate, DisputeMediaRead } from "@/types/disputes";
export async function createDisputeMedia(
  media: DisputeMediaCreate
): Promise<DisputeMediaRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/dispute-media", {
    method: "POST",
    body: JSON.stringify(media),
  });
  return response.json();
}

export async function getDisputeMedia(id: number): Promise<DisputeMediaRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/dispute-media/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function getAllDisputeMedia(): Promise<DisputeMediaRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/dispute-media", {
    method: "GET",
  });
  return response.json();
}

export async function deleteDisputeMedia(
  id: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/dispute-media/${id}`, {
    method: "DELETE",
  });
  return response.json();
}
