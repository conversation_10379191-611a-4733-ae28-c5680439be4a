"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  IssuingAuthorityCreate,
  IssuingAuthorityRead,
} from "@/types/compliance";

const BASE_URL = "/api/issuing-authorities";

export async function createIssuingAuthority(
  data: IssuingAuthorityCreate
): Promise<IssuingAuthorityRead> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch(BASE_URL + "/", {
    method: "POST",
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function getIssuingAuthorityById(
  id: number
): Promise<IssuingAuthorityRead> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch(`${BASE_URL}/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function getAllIssuingAuthorities(): Promise<
  IssuingAuthorityRead[]
> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch(BASE_URL + "/", {
    method: "GET",
  });
  return response.json();
}

export async function updateIssuingAuthority(
  id: number,
  data: IssuingAuthorityCreate
): Promise<IssuingAuthorityRead> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch(`${BASE_URL}/${id}`, {
    method: "PUT",
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function deleteIssuingAuthority(
  id: number
): Promise<{ message: string }> {
  const fetch = await getAuthorizedFetch();
  const response = await fetch(`${BASE_URL}/${id}`, {
    method: "DELETE",
  });
  return response.json();
}
