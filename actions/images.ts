"use server";

import { runWithAmplifyServerContext } from "@/lib/amplifyServerUtils";
import { getUrl } from "aws-amplify/storage/server";
import { cookies } from "next/headers";

export async function generateImageUploadUrl(path: string): Promise<string> {
  const result = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: async (contextSpec) => {
      try {
        const getUrlResult = await getUrl(contextSpec, {
          path,
          options: {
            validateObjectExistence: false,
            expiresIn: 20,
            useAccelerateEndpoint: false,
          },
        });
        return String(getUrlResult.url);
      } catch (error) {
        console.error("Error generating image upload URL:", error);
        throw error;
      }
    },
  });

  return result;
}

