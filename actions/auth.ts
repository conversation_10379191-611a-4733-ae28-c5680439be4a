import {
  signUp,
  confirmSignUp,
  resendSignUpCode,
  signIn,
} from "aws-amplify/auth";

import type { SignUpFormData, SignInFormData } from "@/types/auth";

export async function signup(form: SignUpFormData): Promise<any> {
  const result = await signUp({
    username: form.email,
    password: form.password,
    options: {
      userAttributes: {
        email: form.email,
        phone_number: form.phone,
        family_name: form.lastName,
        given_name: form.firstName,
      },
    },
  });
  return result;
}
export async function confirmUserSignUp(confirmationData: {
  username: string;
  confirmationCode: string;
}): Promise<any> {
  const { nextStep } = await confirmSignUp({
    username: confirmationData.username,
    confirmationCode: confirmationData.confirmationCode,
  });

  return nextStep;
}

export async function resendCode(username: string): Promise<any> {
  const result = await resendSignUpCode({
    username,
  });

  return result;
}

export async function signin(form: SignInFormData): Promise<any> {
  const { nextStep } = await signIn({
    username: form.email,
    password: form.password,
  });
  return nextStep;
}
