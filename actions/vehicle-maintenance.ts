"use server";

import { getAuthorizedAxios } from "@/lib/axiosClient";
import { revalidatePath } from "next/cache";
import type {
  VehicleMaintenanceCreate,
  VehicleMaintenanceRead,
  VehicleMaintenanceUpdate,
  VehicleServiceStatus,
} from "@/types/maintanance";
import { apiErrorFormater } from "@/lib/utils";
import {
  MaintenanceSchema,
  MaintenanceUpdateSchema,
} from "@/schemas/maintenance";

const BASE_URL = "/api/maintenance";

export async function createVehicleMaintenance(
  vehicleMaintenance: VehicleMaintenanceCreate
): Promise<VehicleMaintenanceRead> {
  const axios = await getAuthorizedAxios();
  try {
    const response = await axios.post(BASE_URL, vehicleMaintenance);
    revalidatePath("/maintenance"); // Adjust path if needed
    return response.data;
  } catch (error) {
    throw apiErrorFormater(error);
  }
}

export async function getVehicleMaintenance(
  id: number
): Promise<VehicleMaintenanceRead> {
  const axios = await getAuthorizedAxios();
  try {
    const response = await axios.get(`${BASE_URL}/${id}`);
    return response.data;
  } catch (error) {
    throw apiErrorFormater(error);
  }
}

export async function listVehicleMaintenances(): Promise<
  VehicleMaintenanceRead[]
> {
  const axios = await getAuthorizedAxios();
  try {
    const response = await axios.get(BASE_URL);
    return response.data;
  } catch (error) {
    throw apiErrorFormater(error);
  }
}

export async function updateVehicleMaintenance(
  id: number,
  updatedData: VehicleMaintenanceUpdate
): Promise<VehicleMaintenanceRead> {
  const axios = await getAuthorizedAxios();
  try {
    const response = await axios.put(`${BASE_URL}/${id}`, updatedData);
    revalidatePath("/maintenance"); // Adjust path if needed
    return response.data;
  } catch (error) {
    throw apiErrorFormater(error);
  }
}

export async function deleteVehicleMaintenance(id: number): Promise<void> {
  const axios = await getAuthorizedAxios();
  try {
    await axios.delete(`${BASE_URL}/${id}`);
    revalidatePath("/maintenance"); // Adjust path if needed
  } catch (error) {
    throw apiErrorFormater(error);
  }
}


export async function addVehicleMaintenance(_: any, formData: FormData) {
  const raw = Object.fromEntries(formData.entries());
  const parsedInput = {
    vehicle_id: Number(raw.vehicle_id),
    name: raw.name,
    description: raw.description || undefined,
    due_date: raw.due_date,
    due_odometer: Number(raw.due_odometer),
    status: raw.status as VehicleServiceStatus,
    expected_cost: Number(raw.expected_cost),
    completed_date: raw.completed_date || undefined,
    completed_odometer: raw.completed_odometer
      ? Number(raw.completed_odometer)
      : undefined,
    actual_cost: raw.actual_cost ? Number(raw.actual_cost) : undefined,
    technician_notes: raw.technician_notes || undefined,
    service_provider: raw.service_provider || undefined,
    is_scheduled: raw.is_scheduled === "on",
  };

  const result = MaintenanceSchema.safeParse(parsedInput);
  if (!result.success) {
    return {
      success: false,
      errors: result.error.flatten().fieldErrors,
    };
  }

  const validData = result.data as VehicleMaintenanceCreate;

  try {
    await createVehicleMaintenance(validData);
    revalidatePath("/vehicle-dashboard");
    return {
      success: true,
      message: "Maintenance successfully scheduled.",
    };
  } catch (error: any) {
    console.error("Error creating maintenance:", error);
    return apiErrorFormater(error);
  }
}

export async function editVehicleMaintenance(_: any, formData: FormData) {
  const raw = Object.fromEntries(formData.entries());

  const parsedInput = {
    maintenance_id: Number(raw.maintenance_id),
    vehicle_id: Number(raw.vehicle_id),
    name: raw.name,
    description: raw.description || undefined,
    due_date: raw.due_date,
    due_odometer: Number(raw.due_odometer),
    status: raw.status as VehicleServiceStatus,
    expected_cost: Number(raw.expected_cost),
    completed_date: raw.completed_date || undefined,
    completed_odometer: raw.completed_odometer
      ? Number(raw.completed_odometer)
      : undefined,
    actual_cost: raw.actual_cost ? Number(raw.actual_cost) : undefined,
    technician_notes: raw.technician_notes || undefined,
    service_provider: raw.service_provider || undefined,
    is_scheduled: raw.is_scheduled === "on",
  };

  const result = MaintenanceUpdateSchema.safeParse(parsedInput);
  if (!result.success) {
    return {
      success: false,
      errors: result.error.flatten().fieldErrors,
    };
  }

  const validData = result.data as VehicleMaintenanceUpdate;

  try {
    await updateVehicleMaintenance(parsedInput.maintenance_id, validData);
    revalidatePath("/edit-maintenance/*");
    return {
      success: true,
      message: "Maintenance successfully updated.",
    };
  } catch (error: any) {
    console.error("Error updating maintenance:", error);
    return apiErrorFormater(error);
  }
}
