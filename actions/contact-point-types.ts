"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type { ContactPointTypeRead } from "@/types/contact-point-types";

export async function getContactPointTypes(): Promise<ContactPointTypeRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/contact-point-types/", {
    method: "GET",
  });
  return response.json();
}
