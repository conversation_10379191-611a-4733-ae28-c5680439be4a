"use server";

import type {
  FullInspectionData,
  FullInspectionDataRead,
} from "@/types/handovers";
import { possessionSchema } from "@/schemas/possessions";
import { getAuthorizedFetch } from "@/lib/fetchClient";
import { apiErrorFormater } from "@/lib/utils";

export async function createInspectionWithMedia(
  data: FullInspectionData
): Promise<FullInspectionDataRead> {
  const fetch = await getAuthorizedFetch();

  const response = await fetch("/api/vehicle-inspections/create/with/media", {
    method: "POST",
    body: JSON.stringify(data),
    headers: {
      "Content-Type": "application/json",
    },
  });

  return response.json();
}

export async function addPossessionsFull(_: any, formData: FormData) {
  try {
    const vehicle_id = Number(formData.get("vehicleId"));
    const handing_over = formData.get("handing_over") === "true";

    const scratches = formData.get("scratches") as string;
    const dents = formData.get("dents") as string;
    const tires = formData.get("tires") as string;
    const lights = formData.get("lights") as string;

    const cleanliness = formData.get("cleanliness") as string;
    const seats = formData.get("seats") as string;
    const dashboard_controls = formData.get("dashboard_controls") as string;
    const odors = formData.get("odors") as string;

    const odometer = Number(formData.get("odometer") as string);
    const known_issues = formData.get("known_issues") as string;

    const front_view = formData.get("front_view") as string;
    const rear_view = formData.get("rear_view") as string;
    const left_view = formData.get("left_view") as string;
    const right_view = formData.get("right_view") as string;
    const dashboard = formData.get("dashboard") as string;
    const seats_view = formData.get("seats_view") as string;

    const from_party_id = Number(formData.get("from_party_id"));
    const to_party_id = Number(formData.get("to_party_id"));

    const additional = formData.getAll("additional");
    const data = {
      ...Object.fromEntries(formData.entries()),
      additional: formData.getAll("additional"), // force array
    };
    const result = possessionSchema.safeParse(data);
    if (!result.success) {
      return {
        errors: result.error.flatten().fieldErrors,
      };
    }
    const possessionData = {
      possession: {
        from_party_id,
        to_party_id,
        vehicle_id,
        status: "completed",
      },
      inspection: {
        vehicle_id,
        handing_over,
        scratches,
        dents,
        tires,
        lights,
        cleanliness,
        seats,
        dashboard_controls,
        odors,
        odometer,
        known_issues,
      },
      media: [
        { url: front_view, type: "front_view" },
        { url: left_view, type: "left_view" },
        { url: rear_view, type: "rear_view" },
        { url: right_view, type: "right_view" },
        { url: dashboard, type: "dashboard" },
        { url: seats_view, type: "seats_view" },
        ...additional.map((url) => ({ url, type: "additional" })),
      ],
    };
    const pos = await createInspectionWithMedia(
      possessionData as FullInspectionData
    );
    return {
      success: true,
      message: "Vehicle handover complete.",
      possessionID: pos?.possession?.id,
    };
  } catch (error: any) {
    return apiErrorFormater(error);
  }
}
