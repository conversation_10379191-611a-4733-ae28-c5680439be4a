"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  CompanyOwnershipCreate,
  CompanyOwnershipRead,
  CompanyOwnershipUpdate,
  CompanyOwnershipReadWithRelations,
  CompanyMembershipRead,
} from "@/types/company-ownerships";

export async function createCompanyOwnership(
  companyOwnership: CompanyOwnershipCreate
): Promise<CompanyOwnershipRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/company-ownerships", {
    method: "POST",
    body: JSON.stringify(companyOwnership),
  });
  return response.json();
}

export async function getCompanyOwnership(
  id: number
): Promise<CompanyOwnershipRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/company-ownerships/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function updateCompanyOwnership(
  companyOwnership: CompanyOwnershipUpdate
): Promise<CompanyOwnershipRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/company-ownerships/${companyOwnership.id}`,
    {
      method: "PUT",
      body: JSON.stringify(companyOwnership),
    }
  );
  return response.json();
}

export async function getCompanyOwnershipByParty(
  partyId: number
): Promise<CompanyOwnershipReadWithRelations[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/company-ownerships/by-party/${partyId}`,
    {
      method: "GET",
    },
    {
      revalidate: 3600,
    }
  );
  return response.json();
}

export async function getCompanyOwnershipsByCompanies(
  companyIds: number[]
): Promise<CompanyMembershipRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const query = new URLSearchParams();
  companyIds.forEach((id) => query.append("company_ids", id.toString()));

  const response = await authorizedFetch(
    `/api/company-ownerships/members/by/companies?${query.toString()}`,
    {
      method: "GET",
    }
  );
  return response.json();
}
