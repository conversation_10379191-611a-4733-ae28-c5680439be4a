"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  CompanyOwnershipInviteCreate,
  CompanyOwnershipInviteRead,
  CompanyOwnershipInviteUpdate,
} from "@/types/company-ownerships-invites";

export async function createCompanyOwnershipInvite(
  companyOwnershipInvite: CompanyOwnershipInviteCreate
): Promise<CompanyOwnershipInviteRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/company-ownership-invites", {
    method: "POST",
    body: JSON.stringify(companyOwnershipInvite),
  });
  return response.json();
}

export async function getCompanyOwnershipInvite(
  id: number
): Promise<CompanyOwnershipInviteRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/company-ownership-invites/${id}`,
    {
      method: "GET",
    }
  );
  return response.json();
}

export async function updateCompanyOwnershipInvite(
  companyOwnershipInvite: CompanyOwnershipInviteUpdate
): Promise<CompanyOwnershipInviteRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/company-ownership-invites/${companyOwnershipInvite.id}`,
    {
      method: "PUT",
      body: JSON.stringify(companyOwnershipInvite),
    }
  );
  return response.json();
}

export async function getCompanyOwnershipInvitesByCompany(
  companyId: number
): Promise<CompanyOwnershipInviteRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/company-ownership-invites/by-company/${companyId}`,
    { method: "GET" }
  );
  return response.json();
}

export async function createCompanyOwnershipInvitesBatch(
  invites: CompanyOwnershipInviteCreate[]
): Promise<CompanyOwnershipInviteRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    "/api/company-ownership-invites/batch",
    {
      method: "POST",
      body: JSON.stringify(invites),
    }
  );
  return response.json();
}
