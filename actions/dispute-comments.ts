"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  DisputeCommentCreate,
  DisputeCommentRead,
  DisputeCommentUpdate,
} from "@/types/disputes";

export async function createDisputeComment(
  comment: DisputeCommentCreate
): Promise<DisputeCommentRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/dispute-comments", {
    method: "POST",
    body: JSON.stringify(comment),
  });
  return response.json();
}

export async function getDisputeComment(
  id: number
): Promise<DisputeCommentRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/dispute-comments/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function getAllDisputeComments(): Promise<DisputeCommentRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/dispute-comments", {
    method: "GET",
  });
  return response.json();
}

export async function updateDisputeComment(
  id: number,
  update: DisputeCommentUpdate
): Promise<DisputeCommentRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/dispute-comments/${id}`, {
    method: "PUT",
    body: JSON.stringify(update),
  });
  return response.json();
}

export async function deleteDisputeComment(
  id: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/dispute-comments/${id}`, {
    method: "DELETE",
  });
  return response.json();
}
