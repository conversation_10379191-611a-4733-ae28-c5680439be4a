"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  VerificationCreate,
  VerificationRead,
  VerificationBase,
} from "@/types/compliance";

const BASE_URL = "/api/verifications";

export async function createVerification(
  data: VerificationCreate
): Promise<VerificationRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/`, {
    method: "POST",
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function getVerificationById(
  id: number
): Promise<VerificationRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${id}`, {
    method: "GET",
  });
  return response.json();
}

export async function getAllVerifications(): Promise<VerificationRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/`, {
    method: "GET",
  });
  return response.json();
}

export async function updateVerification(
  id: number,
  data: VerificationBase
): Promise<VerificationRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${id}`, {
    method: "PUT",
    body: JSON.stringify(data),
  });
  return response.json();
}

export async function deleteVerification(
  id: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`${BASE_URL}/${id}`, {
    method: "DELETE",
  });
  return response.json();
}
