"use server";

import { revalidatePath } from "next/cache";
import type {
  VehicleMediaBase,
  VehicleMediaCreate,
  VehicleMediaRead,
} from "@/types/vehicles";
import { apiErrorFormater } from "@/lib/utils";
import { getAuthorizedFetch } from "@/lib/fetchClient";

export async function createVehicleMedia(
  vehicleMedia: VehicleMediaCreate
): Promise<VehicleMediaRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/vehicle-media", {
    method: "POST",
    body: JSON.stringify(vehicleMedia),
  });
  return response.json();
}

export async function getVehicleMediaById(
  mediaId: number
): Promise<VehicleMediaRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/vehicle-media/${mediaId}`, {
    method: "GET",
  });
  return response.json();
}

export async function getAllVehicleMedia(): Promise<VehicleMediaRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/vehicle-media", {
    method: "GET",
  });
  return response.json();
}

export async function updateVehicleMedia(
  mediaId: number,
  vehicleMedia: VehicleMediaBase
): Promise<VehicleMediaRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/vehicle-media/${mediaId}`, {
    method: "PUT",
    body: JSON.stringify(vehicleMedia),
  });
  return response.json();
}

export async function deleteVehicleMedia(
  mediaId: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(`/api/vehicle-media/${mediaId}`, {
    method: "DELETE",
  });
  return response.json();
}

export async function deleteVehicleDocument(id: number): Promise<void> {
  const authorizedFetch = await getAuthorizedFetch();
  await authorizedFetch(`/api/vehicle-documents/${id}`, {
    method: "DELETE",
  });
}

export async function addVehicleMedia(_: any, formData: FormData) {
  const vehicleID = Number(formData.get("vehicleID"));
  const documentImageUrl = (formData.get("documentUrl") as string)?.trim();

  const errors = [];

  if (!vehicleID) errors.push("Select a vehicle");
  if (!documentImageUrl) errors.push("File required");

  const vehicleDocument = {
    vehicle_id: vehicleID,
    media_path: documentImageUrl,
  } as VehicleMediaCreate;
  try {
    await createVehicleMedia(vehicleDocument);
    revalidatePath("/vehicle-dashboard");

    return { success: true, message: "Image uploaded successfully." };
  } catch (error: any) {
    return apiErrorFormater(error);
  }
}
