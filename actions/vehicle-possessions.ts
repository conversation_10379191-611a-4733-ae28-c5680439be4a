"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import type {
  VehiclePossessionCreate,
  VehiclePossessionRead,
  VehiclePossessionBase,
  VehiclePossessionWithContactRead,
} from "@/types/vehicle-possessions";

export async function createVehiclePossession(
  possession: VehiclePossessionCreate
): Promise<VehiclePossessionRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/vehicle-possessions", {
    method: "POST",
    body: JSON.stringify(possession),
  });
  return response.json();
}

export async function getVehiclePossession(
  possessionId: number
): Promise<VehiclePossessionWithContactRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/vehicle-possessions/${possessionId}`,
    {
      method: "GET",
    }
  );
  return response.json();
}

export async function getAllVehiclePossessions(): Promise<
  VehiclePossessionRead[]
> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch("/api/vehicle-possessions", {
    method: "GET",
  });
  return response.json();
}

export async function updateVehiclePossession(
  possessionId: number,
  possessionUpdate: VehiclePossessionBase
): Promise<VehiclePossessionRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/vehicle-possessions/${possessionId}`,
    {
      method: "PUT",
      body: JSON.stringify(possessionUpdate),
    }
  );
  return response.json();
}

export async function deleteVehiclePossession(
  possessionId: number
): Promise<{ message: string }> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/vehicle-possessions/${possessionId}`,
    {
      method: "DELETE",
    }
  );
  return response.json();
}

export async function getPossessionsByVehicle(
  vehicleId: number
): Promise<VehiclePossessionWithContactRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/vehicle-possessions/vehicle/by-vehicle/${vehicleId}`,
    { method: "GET" }
  );
  return response.json();
}

export async function getPossessionByVehicle(
  vehicleId: number,
  possessionId: number
): Promise<VehiclePossessionWithContactRead> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/vehicle-possessions/vehicle/by-vehicle/${vehicleId}/${possessionId}`,
    { method: "GET" }
  );
  return response.json();
}

export async function getPossessionsByParty(
  partyId: number
): Promise<VehiclePossessionWithContactRead[]> {
  const authorizedFetch = await getAuthorizedFetch();
  const response = await authorizedFetch(
    `/api/vehicle-possessions/party/by-party/${partyId}`,
    { method: "GET" }
  );
  return response.json();
}
