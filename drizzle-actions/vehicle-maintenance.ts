"use server";

/**
 * DRIZZLE ACTIONS - VEHICLE MAINTENANCE
 * 
 * This file contains all direct database operations for vehicle maintenance using Drizzle ORM.
 * 
 * Architecture:
 * - drizzle-actions/* = Direct database queries using Drizzle ORM
 * - actions/* = Legacy API calls using axios (maintained for backward compatibility)
 * 
 * Schema Tables Used:
 * - vehicleMaintenance: Core maintenance records
 * - vehicles: Vehicle information
 * - vehicleModel: Vehicle model details
 * - vehicleMake: Vehicle manufacturer
 * - party: Vehicle owners
 * - individual: Owner details
 */

import { db } from "../db";
import { eq, and, desc, asc, gte, lte, between, sql, isNull, isNotNull } from "drizzle-orm";
import {
  vehicleMaintenance,
  vehicles,
  vehicleModel,
  vehicleMake,
  party,
  individual,
} from "../drizzle/schema";

// ==================== TYPES ====================

export interface VehicleMaintenanceRead {
  id: number;
  vehicleId: number;
  name: string;
  description?: string;
  dueDate: string;
  dueOdometer: number;
  status: "SCHEDULED" | "PENDING" | "COMPLETED";
  expectedCost: number;
  completedDate?: string;
  completedOdometer?: number;
  actualCost?: number;
  technicianNotes?: string;
  serviceProvider?: string;
  isScheduled?: boolean;
  createdAt?: string;
  updatedAt?: string;
  // Related data
  vehicle?: {
    id: number;
    vinNumber: string | null;
    vehicleRegistration?: string | null;
    model?: {
      model: string;
      yearModel: number | null;
      make?: {
        name: string;
      };
    };
  };
  owner?: {
    firstName: string;
    lastName: string;
  };
}

export interface VehicleMaintenanceCreate {
  vehicleId: number;
  name: string;
  description?: string;
  dueDate: string;
  dueOdometer: number;
  status: "SCHEDULED" | "PENDING" | "COMPLETED";
  expectedCost: number;
  completedDate?: string;
  completedOdometer?: number;
  actualCost?: number;
  technicianNotes?: string;
  serviceProvider?: string;
  isScheduled?: boolean;
}

export interface VehicleMaintenanceUpdate {
  name?: string;
  description?: string;
  dueDate?: string;
  dueOdometer?: number;
  status?: "SCHEDULED" | "PENDING" | "COMPLETED";
  expectedCost?: number;
  completedDate?: string;
  completedOdometer?: number;
  actualCost?: number;
  technicianNotes?: string;
  serviceProvider?: string;
  isScheduled?: boolean;
}

// ==================== CRUD OPERATIONS ====================

// Create a new maintenance record
export async function createVehicleMaintenanceDrizzle(
  maintenanceData: VehicleMaintenanceCreate
): Promise<VehicleMaintenanceRead> {
  try {
    const newMaintenance = await db
      .insert(vehicleMaintenance)
      .values({
        vehicleId: maintenanceData.vehicleId,
        name: maintenanceData.name,
        description: maintenanceData.description,
        dueDate: maintenanceData.dueDate,
        dueOdometer: maintenanceData.dueOdometer,
        status: maintenanceData.status,
        expectedCost: maintenanceData.expectedCost,
        completedDate: maintenanceData.completedDate,
        completedOdometer: maintenanceData.completedOdometer,
        actualCost: maintenanceData.actualCost,
        technicianNotes: maintenanceData.technicianNotes,
        serviceProvider: maintenanceData.serviceProvider,
        isScheduled: maintenanceData.isScheduled,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    return {
      id: newMaintenance[0].id,
      vehicleId: newMaintenance[0].vehicleId,
      name: newMaintenance[0].name,
      description: newMaintenance[0].description || undefined,
      dueDate: newMaintenance[0].dueDate,
      dueOdometer: newMaintenance[0].dueOdometer,
      status: newMaintenance[0].status as "SCHEDULED" | "PENDING" | "COMPLETED",
      expectedCost: newMaintenance[0].expectedCost,
      completedDate: newMaintenance[0].completedDate || undefined,
      completedOdometer: newMaintenance[0].completedOdometer || undefined,
      actualCost: newMaintenance[0].actualCost || undefined,
      technicianNotes: newMaintenance[0].technicianNotes || undefined,
      serviceProvider: newMaintenance[0].serviceProvider || undefined,
      isScheduled: newMaintenance[0].isScheduled || undefined,
      createdAt: newMaintenance[0].createdAt || undefined,
      updatedAt: newMaintenance[0].updatedAt || undefined,
    };
  } catch (error) {
    console.error("Error creating vehicle maintenance:", error);
    throw error;
  }
}

// Get maintenance record by ID with related data
export async function getVehicleMaintenanceByIdDrizzle(
  maintenanceId: number
): Promise<VehicleMaintenanceRead | null> {
  try {
    const maintenance = await db
      .select({
        id: vehicleMaintenance.id,
        vehicleId: vehicleMaintenance.vehicleId,
        name: vehicleMaintenance.name,
        description: vehicleMaintenance.description,
        dueDate: vehicleMaintenance.dueDate,
        dueOdometer: vehicleMaintenance.dueOdometer,
        status: vehicleMaintenance.status,
        expectedCost: vehicleMaintenance.expectedCost,
        completedDate: vehicleMaintenance.completedDate,
        completedOdometer: vehicleMaintenance.completedOdometer,
        actualCost: vehicleMaintenance.actualCost,
        technicianNotes: vehicleMaintenance.technicianNotes,
        serviceProvider: vehicleMaintenance.serviceProvider,
        isScheduled: vehicleMaintenance.isScheduled,
        createdAt: vehicleMaintenance.createdAt,
        updatedAt: vehicleMaintenance.updatedAt,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleId_rel: vehicles.id,
        // Model details
        modelName: vehicleModel.model,
        modelYear: vehicleModel.yearModel,
        // Make details
        makeName: vehicleMake.name,
        // Owner details
        ownerFirstName: individual.firstName,
        ownerLastName: individual.lastName,
      })
      .from(vehicleMaintenance)
      .leftJoin(vehicles, eq(vehicleMaintenance.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(vehicles.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(vehicleMaintenance.id, maintenanceId))
      .limit(1);

    if (maintenance.length === 0) return null;

    const record = maintenance[0];
    
    return {
      id: record.id,
      vehicleId: record.vehicleId,
      name: record.name,
      description: record.description || undefined,
      dueDate: record.dueDate,
      dueOdometer: record.dueOdometer,
      status: record.status as "SCHEDULED" | "PENDING" | "COMPLETED",
      expectedCost: record.expectedCost,
      completedDate: record.completedDate || undefined,
      completedOdometer: record.completedOdometer || undefined,
      actualCost: record.actualCost || undefined,
      technicianNotes: record.technicianNotes || undefined,
      serviceProvider: record.serviceProvider || undefined,
      isScheduled: record.isScheduled || undefined,
      createdAt: record.createdAt || undefined,
      updatedAt: record.updatedAt || undefined,
      vehicle: record.vehicleId_rel ? {
        id: record.vehicleId_rel,
        vinNumber: record.vehicleVin,
        vehicleRegistration: record.vehicleRegistration || undefined,
        model: record.modelName ? {
          model: record.modelName,
          yearModel: record.modelYear,
          make: record.makeName ? {
            name: record.makeName,
          } : undefined,
        } : undefined,
      } : undefined,
      owner: record.ownerFirstName && record.ownerLastName ? {
        firstName: record.ownerFirstName,
        lastName: record.ownerLastName,
      } : undefined,
    };
  } catch (error) {
    console.error("Error fetching vehicle maintenance:", error);
    throw error;
  }
}

// Update maintenance record
export async function updateVehicleMaintenanceDrizzle(
  maintenanceId: number,
  updateData: VehicleMaintenanceUpdate
): Promise<VehicleMaintenanceRead | null> {
  try {
    const updateValues: any = {
      updatedAt: new Date().toISOString(),
    };

    // Add only provided fields to update
    if (updateData.name !== undefined) updateValues.name = updateData.name;
    if (updateData.description !== undefined) updateValues.description = updateData.description;
    if (updateData.dueDate !== undefined) updateValues.dueDate = updateData.dueDate;
    if (updateData.dueOdometer !== undefined) updateValues.dueOdometer = updateData.dueOdometer;
    if (updateData.status !== undefined) updateValues.status = updateData.status;
    if (updateData.expectedCost !== undefined) updateValues.expectedCost = updateData.expectedCost;
    if (updateData.completedDate !== undefined) updateValues.completedDate = updateData.completedDate;
    if (updateData.completedOdometer !== undefined) updateValues.completedOdometer = updateData.completedOdometer;
    if (updateData.actualCost !== undefined) updateValues.actualCost = updateData.actualCost;
    if (updateData.technicianNotes !== undefined) updateValues.technicianNotes = updateData.technicianNotes;
    if (updateData.serviceProvider !== undefined) updateValues.serviceProvider = updateData.serviceProvider;
    if (updateData.isScheduled !== undefined) updateValues.isScheduled = updateData.isScheduled;

    const updated = await db
      .update(vehicleMaintenance)
      .set(updateValues)
      .where(eq(vehicleMaintenance.id, maintenanceId))
      .returning();

    if (updated.length === 0) return null;

    // Return the updated record with related data
    return await getVehicleMaintenanceByIdDrizzle(maintenanceId);
  } catch (error) {
    console.error("Error updating vehicle maintenance:", error);
    throw error;
  }
}

// Delete maintenance record
export async function deleteVehicleMaintenanceDrizzle(maintenanceId: number): Promise<boolean> {
  try {
    const deleted = await db
      .delete(vehicleMaintenance)
      .where(eq(vehicleMaintenance.id, maintenanceId))
      .returning();

    return deleted.length > 0;
  } catch (error) {
    console.error("Error deleting vehicle maintenance:", error);
    throw error;
  }
}

// ==================== QUERY OPERATIONS ====================

// Get all maintenance records with pagination and filtering
export async function getAllVehicleMaintenanceDrizzle(options: {
  page?: number;
  limit?: number;
  vehicleId?: number;
  status?: "SCHEDULED" | "PENDING" | "COMPLETED";
  dueSoon?: boolean; // Within next 30 days
  overdue?: boolean;
  sortBy?: "dueDate" | "createdAt" | "expectedCost";
  sortOrder?: "asc" | "desc";
} = {}): Promise<{
  records: VehicleMaintenanceRead[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  try {
    const {
      page = 1,
      limit = 20,
      vehicleId,
      status,
      dueSoon,
      overdue,
      sortBy = "dueDate",
      sortOrder = "asc",
    } = options;

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [];
    
    if (vehicleId) {
      whereConditions.push(eq(vehicleMaintenance.vehicleId, vehicleId));
    }
    
    if (status) {
      whereConditions.push(eq(vehicleMaintenance.status, status));
    }
    
    if (dueSoon) {
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      whereConditions.push(
        and(
          lte(vehicleMaintenance.dueDate, thirtyDaysFromNow.toISOString()),
          gte(vehicleMaintenance.dueDate, new Date().toISOString())
        )
      );
    }
    
    if (overdue) {
      whereConditions.push(
        and(
          lte(vehicleMaintenance.dueDate, new Date().toISOString()),
          eq(vehicleMaintenance.status, "PENDING")
        )
      );
    }

    const whereClause = whereConditions.length > 0 
      ? whereConditions.reduce((acc, condition) => and(acc, condition))
      : undefined;

    // Build sort order
    const orderByClause = (() => {
      const column = sortBy === "dueDate" ? vehicleMaintenance.dueDate
                   : sortBy === "createdAt" ? vehicleMaintenance.createdAt
                   : vehicleMaintenance.expectedCost;
      
      return sortOrder === "desc" ? desc(column) : asc(column);
    })();

    // Get total count
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(vehicleMaintenance)
      .where(whereClause);
    
    const total = totalResult[0].count;

    // Get records with related data
    const records = await db
      .select({
        id: vehicleMaintenance.id,
        vehicleId: vehicleMaintenance.vehicleId,
        name: vehicleMaintenance.name,
        description: vehicleMaintenance.description,
        dueDate: vehicleMaintenance.dueDate,
        dueOdometer: vehicleMaintenance.dueOdometer,
        status: vehicleMaintenance.status,
        expectedCost: vehicleMaintenance.expectedCost,
        completedDate: vehicleMaintenance.completedDate,
        completedOdometer: vehicleMaintenance.completedOdometer,
        actualCost: vehicleMaintenance.actualCost,
        technicianNotes: vehicleMaintenance.technicianNotes,
        serviceProvider: vehicleMaintenance.serviceProvider,
        isScheduled: vehicleMaintenance.isScheduled,
        createdAt: vehicleMaintenance.createdAt,
        updatedAt: vehicleMaintenance.updatedAt,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleId_rel: vehicles.id,
        // Model details
        modelName: vehicleModel.model,
        modelYear: vehicleModel.yearModel,
        // Make details
        makeName: vehicleMake.name,
        // Owner details
        ownerFirstName: individual.firstName,
        ownerLastName: individual.lastName,
      })
      .from(vehicleMaintenance)
      .leftJoin(vehicles, eq(vehicleMaintenance.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(vehicles.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    const formattedRecords: VehicleMaintenanceRead[] = records.map(record => ({
      id: record.id,
      vehicleId: record.vehicleId,
      name: record.name,
      description: record.description || undefined,
      dueDate: record.dueDate,
      dueOdometer: record.dueOdometer,
      status: record.status as "SCHEDULED" | "PENDING" | "COMPLETED",
      expectedCost: record.expectedCost,
      completedDate: record.completedDate || undefined,
      completedOdometer: record.completedOdometer || undefined,
      actualCost: record.actualCost || undefined,
      technicianNotes: record.technicianNotes || undefined,
      serviceProvider: record.serviceProvider || undefined,
      isScheduled: record.isScheduled || undefined,
      createdAt: record.createdAt || undefined,
      updatedAt: record.updatedAt || undefined,
      vehicle: record.vehicleId_rel ? {
        id: record.vehicleId_rel,
        vinNumber: record.vehicleVin,
        vehicleRegistration: record.vehicleRegistration || undefined,
        model: record.modelName ? {
          model: record.modelName,
          yearModel: record.modelYear,
          make: record.makeName ? {
            name: record.makeName,
          } : undefined,
        } : undefined,
      } : undefined,
      owner: record.ownerFirstName && record.ownerLastName ? {
        firstName: record.ownerFirstName,
        lastName: record.ownerLastName,
      } : undefined,
    }));

    return {
      records: formattedRecords,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  } catch (error) {
    console.error("Error fetching vehicle maintenance records:", error);
    throw error;
  }
}

// Get maintenance statistics
export async function getVehicleMaintenanceStatsDrizzle(): Promise<{
  totalMaintenance: number;
  scheduled: number;
  pending: number;
  completed: number;
  overdue: number;
  dueSoon: number;
  totalCostEstimated: number;
  totalCostActual: number;
}> {
  try {
    const now = new Date().toISOString();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    const stats = await db
      .select({
        totalMaintenance: sql<number>`count(*)`,
        scheduled: sql<number>`count(*) filter (where status = 'SCHEDULED')`,
        pending: sql<number>`count(*) filter (where status = 'PENDING')`,
        completed: sql<number>`count(*) filter (where status = 'COMPLETED')`,
        overdue: sql<number>`count(*) filter (where due_date < ${now} and status = 'PENDING')`,
        dueSoon: sql<number>`count(*) filter (where due_date <= ${thirtyDaysFromNow.toISOString()} and due_date >= ${now} and status != 'COMPLETED')`,
        totalCostEstimated: sql<number>`coalesce(sum(expected_cost), 0)`,
        totalCostActual: sql<number>`coalesce(sum(actual_cost), 0)`,
      })
      .from(vehicleMaintenance);

    return stats[0];
  } catch (error) {
    console.error("Error fetching maintenance statistics:", error);
    throw error;
  }
}

// Mark maintenance as completed
export async function completeVehicleMaintenanceDrizzle(
  maintenanceId: number,
  completionData: {
    completedDate: string;
    completedOdometer?: number;
    actualCost?: number;
    technicianNotes?: string;
  }
): Promise<VehicleMaintenanceRead | null> {
  try {
    return await updateVehicleMaintenanceDrizzle(maintenanceId, {
      status: "COMPLETED",
      ...completionData,
    });
  } catch (error) {
    console.error("Error completing vehicle maintenance:", error);
    throw error;
  }
}

// Get maintenance for a specific vehicle
export async function getVehicleMaintenanceByVehicleIdDrizzle(
  vehicleId: number,
  options: {
    status?: "SCHEDULED" | "PENDING" | "COMPLETED";
    limit?: number;
  } = {}
): Promise<VehicleMaintenanceRead[]> {
  try {
    const result = await getAllVehicleMaintenanceDrizzle({
      vehicleId,
      status: options.status,
      limit: options.limit || 50,
      sortBy: "dueDate",
      sortOrder: "asc",
    });

    return result.records;
  } catch (error) {
    console.error("Error fetching vehicle maintenance by vehicle ID:", error);
    throw error;
  }
} 