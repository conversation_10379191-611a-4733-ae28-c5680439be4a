"use server";

/**
 * DRIZZLE ACTIONS - LISTING INTEREST EXPRESSIONS
 * 
 * This file contains all direct database operations for listing interest expressions using Drizzle ORM.
 */

import { db } from "../db";
import { eq, and, desc, sql, inArray } from "drizzle-orm";
import {
  listingInterestExpressions,
  party,
  individual,
} from "../drizzle/schema";

// ==================== TYPES ====================

export interface ListingInterestExpressionCreate {
  listingId: number;
  partyId: number;
}

export interface ListingInterestExpressionRead {
  id: number;
  listingId: number;
  partyId: number;
  createdAt?: string;
  updatedAt?: string;
  // Related data
  person?: {
    firstName: string;
    lastName: string;
  };
}

// ==================== INTEREST EXPRESSION CRUD OPERATIONS ====================

// Express interest in a listing
export async function createListingInterestExpressionDrizzle(
  interestData: ListingInterestExpressionCreate
): Promise<ListingInterestExpressionRead> {
  try {
    // Check if the user has already expressed interest
    const existingInterest = await db
      .select()
      .from(listingInterestExpressions)
      .where(
        and(
          eq(listingInterestExpressions.listingId, interestData.listingId),
          eq(listingInterestExpressions.partyId, interestData.partyId)
        )
      )
      .limit(1);

    if (existingInterest.length > 0) {
      throw new Error("You have already expressed interest in this listing");
    }

    const newInterest = await db
      .insert(listingInterestExpressions)
      .values({
        listingId: interestData.listingId,
        partyId: interestData.partyId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    return {
      id: newInterest[0].id,
      listingId: newInterest[0].listingId,
      partyId: newInterest[0].partyId,
      createdAt: newInterest[0].createdAt || undefined,
      updatedAt: newInterest[0].updatedAt || undefined,
    };
  } catch (error) {
    console.error("Error creating listing interest expression:", error);
    throw error;
  }
}

// Remove interest expression
export async function deleteListingInterestExpressionDrizzle(
  listingId: number,
  partyId: number
): Promise<void> {
  try {
    await db
      .delete(listingInterestExpressions)
      .where(
        and(
          eq(listingInterestExpressions.listingId, listingId),
          eq(listingInterestExpressions.partyId, partyId)
        )
      );
  } catch (error) {
    console.error("Error deleting listing interest expression:", error);
    throw error;
  }
}

// Check if user has already expressed interest
export async function checkUserInterestDrizzle(
  listingId: number,
  partyId: number
): Promise<boolean> {
  try {
    const result = await db
      .select({ count: sql<number>`count(*)` })
      .from(listingInterestExpressions)
      .where(
        and(
          eq(listingInterestExpressions.listingId, listingId),
          eq(listingInterestExpressions.partyId, partyId)
        )
      );

    return result[0].count > 0;
  } catch (error) {
    console.error("Error checking user interest:", error);
    throw error;
  }
}

// Get all interest expressions for a listing
export async function getListingInterestExpressionsDrizzle(
  listingId: number
): Promise<ListingInterestExpressionRead[]> {
  try {
    const interests = await db
      .select({
        id: listingInterestExpressions.id,
        listingId: listingInterestExpressions.listingId,
        partyId: listingInterestExpressions.partyId,
        createdAt: listingInterestExpressions.createdAt,
        updatedAt: listingInterestExpressions.updatedAt,
        // Person details
        firstName: individual.firstName,
        lastName: individual.lastName,
      })
      .from(listingInterestExpressions)
      .leftJoin(party, eq(listingInterestExpressions.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(listingInterestExpressions.listingId, listingId))
      .orderBy(desc(listingInterestExpressions.createdAt));

    return interests.map(interest => ({
      id: interest.id,
      listingId: interest.listingId,
      partyId: interest.partyId,
      createdAt: interest.createdAt || undefined,
      updatedAt: interest.updatedAt || undefined,
      person: interest.firstName && interest.lastName ? {
        firstName: interest.firstName,
        lastName: interest.lastName,
      } : undefined,
    }));
  } catch (error) {
    console.error("Error fetching listing interest expressions:", error);
    throw error;
  }
}

// Get interest count for multiple listings
export async function getListingInterestCountsDrizzle(
  listingIds: number[]
): Promise<Record<number, number>> {
  try {
    if (listingIds.length === 0) return {};

    const counts = await db
      .select({
        listingId: listingInterestExpressions.listingId,
        count: sql<number>`count(*)`,
      })
      .from(listingInterestExpressions)
      .where(inArray(listingInterestExpressions.listingId, listingIds))
      .groupBy(listingInterestExpressions.listingId);

    const countMap: Record<number, number> = {};
    listingIds.forEach(id => {
      countMap[id] = 0;
    });
    
    counts.forEach(({ listingId, count }) => {
      countMap[listingId] = count;
    });

    return countMap;
  } catch (error) {
    console.error("Error fetching listing interest counts:", error);
    throw error;
  }
}

// Get user's interest status for multiple listings
export async function getUserInterestStatusDrizzle(
  listingIds: number[],
  partyId: number
): Promise<Record<number, boolean>> {
  try {
    if (listingIds.length === 0) return {};

    const userInterests = await db
      .select({
        listingId: listingInterestExpressions.listingId,
      })
      .from(listingInterestExpressions)
      .where(
        and(
          inArray(listingInterestExpressions.listingId, listingIds),
          eq(listingInterestExpressions.partyId, partyId)
        )
      );

    const statusMap: Record<number, boolean> = {};
    listingIds.forEach(id => {
      statusMap[id] = false;
    });
    
    userInterests.forEach(({ listingId }) => {
      statusMap[listingId] = true;
    });

    return statusMap;
  } catch (error) {
    console.error("Error fetching user interest status:", error);
    throw error;
  }
} 