"use server";

/**
 * DRIZZLE ACTIONS - COMPLIANCE MANAGEMENT
 * 
 * This file contains all direct database operations for compliance management using Drizzle ORM.
 * 
 * Architecture:
 * - drizzle-actions/* = Direct database queries using Drizzle ORM
 * - actions/* = Legacy API calls using axios (maintained for backward compatibility)
 * 
 * Schema Tables Used:
 * - complianceRequirement: Core compliance requirements
 * - complianceSet: Groupings of compliance requirements
 * - complianceRequirementType: Types of compliance requirements
 * - complianceSetRequirementTypeMapping: Mapping between sets and types
 * - verification: Verification records
 * - issuingAuthority: Document issuing authorities
 * - party: Entities with compliance requirements
 * - individual: Individual information
 */

import { db } from "../db";
import { eq, and, desc, asc, gte, lte, between, sql, isNull, isNotNull, or } from "drizzle-orm";
import {
  complianceRequirement,
  complianceSet,
  complianceRequirementType,
  complianceSetRequirementTypeMapping,
  verification,
  issuingAuthority,
  party,
  individual,
} from "../drizzle/schema";

// ==================== TYPES ====================

export interface ComplianceRequirementRead {
  id: number;
  partyId: number;
  complianceSetId: number;
  requirementTypeId: number;
  reviewedById?: number;
  status?: "ACCEPTED" | "REJECTED" | "INCOMPLETE" | "PENDING";
  referenceType?: "TAX_PIN" | "URL";
  reference?: string;
  submittedAt?: string;
  reviewedAt?: string;
  notes?: string;
  issueDate?: string;
  expiryDate?: string;
  issuingAuthorityId?: number;
  uploadedIndividualId?: number;
  verificationId?: number;
  createdAt?: string;
  updatedAt?: string;
  // Related data
  complianceSet?: {
    id: number;
    name: string;
    description?: string;
  };
  requirementType?: {
    id: number;
    name: string;
    description?: string;
    defaultValidityInDays?: number;
  };
  issuingAuthority?: {
    id: number;
    name: string;
    country?: string;
  };
  verification?: {
    id: number;
    verificationType?: "AI" | "MANUAL" | "API";
    verificationOutcome?: string;
    cost?: number;
  };
  party?: {
    id: number;
    individual?: {
      firstName: string;
      lastName: string;
    };
  };
  reviewer?: {
    firstName: string;
    lastName: string;
  };
}

export interface ComplianceRequirementCreate {
  partyId: number;
  complianceSetId: number;
  requirementTypeId: number;
  status?: "ACCEPTED" | "REJECTED" | "INCOMPLETE" | "PENDING";
  referenceType?: "TAX_PIN" | "URL";
  reference?: string;
  submittedAt?: string;
  notes?: string;
  issueDate?: string;
  expiryDate?: string;
  issuingAuthorityId?: number;
  uploadedIndividualId?: number;
}

export interface ComplianceRequirementUpdate {
  status?: "ACCEPTED" | "REJECTED" | "INCOMPLETE" | "PENDING";
  reviewedById?: number;
  reviewedAt?: string;
  referenceType?: "TAX_PIN" | "URL";
  reference?: string;
  notes?: string;
  issueDate?: string;
  expiryDate?: string;
  issuingAuthorityId?: number;
  verificationId?: number;
}

export interface ComplianceSetRead {
  id: number;
  name: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
  requirementTypes?: ComplianceRequirementTypeRead[];
}

export interface ComplianceRequirementTypeRead {
  id: number;
  name: string;
  description?: string;
  defaultValidityInDays?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface IssuingAuthorityRead {
  id: number;
  partyId: number;
  name: string;
  description?: string;
  country?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ComplianceStats {
  totalRequirements: number;
  pending: number;
  accepted: number;
  rejected: number;
  incomplete: number;
  expiringSoon: number; // Within 30 days
  expired: number;
  totalPartiesWithCompliance: number;
  averageCompletionTime?: number; // Days from submission to approval
}

// ==================== COMPLIANCE REQUIREMENT CRUD ====================

// Create a new compliance requirement
export async function createComplianceRequirementDrizzle(
  requirementData: ComplianceRequirementCreate
): Promise<ComplianceRequirementRead> {
  try {
    const newRequirement = await db
      .insert(complianceRequirement)
      .values({
        partyId: requirementData.partyId,
        complianceSetId: requirementData.complianceSetId,
        requirementTypeId: requirementData.requirementTypeId,
        status: requirementData.status || "PENDING",
        referenceType: requirementData.referenceType,
        reference: requirementData.reference,
        submittedAt: requirementData.submittedAt || new Date().toISOString(),
        notes: requirementData.notes,
        issueDate: requirementData.issueDate,
        expiryDate: requirementData.expiryDate,
        issuingAuthorityId: requirementData.issuingAuthorityId,
        uploadedIndividualId: requirementData.uploadedIndividualId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    const result = await getComplianceRequirementByIdDrizzle(newRequirement[0].id);
    if (!result) {
      throw new Error("Failed to create compliance requirement");
    }
    return result;
  } catch (error) {
    console.error("Error creating compliance requirement:", error);
    throw error;
  }
}

// Get compliance requirement by ID with related data
export async function getComplianceRequirementByIdDrizzle(
  requirementId: number
): Promise<ComplianceRequirementRead | null> {
  try {
    const requirement = await db
      .select({
        id: complianceRequirement.id,
        partyId: complianceRequirement.partyId,
        complianceSetId: complianceRequirement.complianceSetId,
        requirementTypeId: complianceRequirement.requirementTypeId,
        reviewedById: complianceRequirement.reviewedById,
        status: complianceRequirement.status,
        referenceType: complianceRequirement.referenceType,
        reference: complianceRequirement.reference,
        submittedAt: complianceRequirement.submittedAt,
        reviewedAt: complianceRequirement.reviewedAt,
        notes: complianceRequirement.notes,
        issueDate: complianceRequirement.issueDate,
        expiryDate: complianceRequirement.expiryDate,
        issuingAuthorityId: complianceRequirement.issuingAuthorityId,
        uploadedIndividualId: complianceRequirement.uploadedIndividualId,
        verificationId: complianceRequirement.verificationId,
        createdAt: complianceRequirement.createdAt,
        updatedAt: complianceRequirement.updatedAt,
        // Compliance Set details
        complianceSetName: complianceSet.name,
        complianceSetDescription: complianceSet.description,
        // Requirement Type details
        requirementTypeName: complianceRequirementType.name,
        requirementTypeDescription: complianceRequirementType.description,
        requirementTypeValidityDays: complianceRequirementType.defaultValidityInDays,
        // Issuing Authority details
        issuingAuthorityName: issuingAuthority.name,
        issuingAuthorityCountry: issuingAuthority.country,
        // Verification details
        verificationType: verification.verificationType,
        verificationOutcome: verification.verificationOutcome,
        verificationCost: verification.cost,
        // Party details
        partyIndividualFirstName: individual.firstName,
        partyIndividualLastName: individual.lastName,
      })
      .from(complianceRequirement)
      .leftJoin(complianceSet, eq(complianceRequirement.complianceSetId, complianceSet.id))
      .leftJoin(complianceRequirementType, eq(complianceRequirement.requirementTypeId, complianceRequirementType.id))
      .leftJoin(issuingAuthority, eq(complianceRequirement.issuingAuthorityId, issuingAuthority.id))
      .leftJoin(verification, eq(complianceRequirement.verificationId, verification.id))
      .leftJoin(party, eq(complianceRequirement.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(complianceRequirement.id, requirementId))
      .limit(1);

    if (requirement.length === 0) return null;

    const record = requirement[0];
    
    return {
      id: record.id,
      partyId: record.partyId,
      complianceSetId: record.complianceSetId,
      requirementTypeId: record.requirementTypeId,
      reviewedById: record.reviewedById || undefined,
      status: record.status as "ACCEPTED" | "REJECTED" | "INCOMPLETE" | "PENDING" | undefined,
      referenceType: record.referenceType as "TAX_PIN" | "URL" | undefined,
      reference: record.reference || undefined,
      submittedAt: record.submittedAt || undefined,
      reviewedAt: record.reviewedAt || undefined,
      notes: record.notes || undefined,
      issueDate: record.issueDate || undefined,
      expiryDate: record.expiryDate || undefined,
      issuingAuthorityId: record.issuingAuthorityId || undefined,
      uploadedIndividualId: record.uploadedIndividualId || undefined,
      verificationId: record.verificationId || undefined,
      createdAt: record.createdAt || undefined,
      updatedAt: record.updatedAt || undefined,
      complianceSet: record.complianceSetName ? {
        id: record.complianceSetId,
        name: record.complianceSetName,
        description: record.complianceSetDescription || undefined,
      } : undefined,
      requirementType: record.requirementTypeName ? {
        id: record.requirementTypeId,
        name: record.requirementTypeName,
        description: record.requirementTypeDescription || undefined,
        defaultValidityInDays: record.requirementTypeValidityDays || undefined,
      } : undefined,
      issuingAuthority: record.issuingAuthorityName ? {
        id: record.issuingAuthorityId!,
        name: record.issuingAuthorityName,
        country: record.issuingAuthorityCountry || undefined,
      } : undefined,
      verification: record.verificationId ? {
        id: record.verificationId,
        verificationType: record.verificationType as "AI" | "MANUAL" | "API" | undefined,
        verificationOutcome: record.verificationOutcome || undefined,
        cost: record.verificationCost || undefined,
      } : undefined,
      party: record.partyIndividualFirstName && record.partyIndividualLastName ? {
        id: record.partyId,
        individual: {
          firstName: record.partyIndividualFirstName,
          lastName: record.partyIndividualLastName,
        },
      } : undefined,
    };
  } catch (error) {
    console.error("Error fetching compliance requirement:", error);
    throw error;
  }
}

// Update compliance requirement
export async function updateComplianceRequirementDrizzle(
  requirementId: number,
  updateData: ComplianceRequirementUpdate
): Promise<ComplianceRequirementRead | null> {
  try {
    const updateValues: any = {
      updatedAt: new Date().toISOString(),
    };

    // Add only provided fields to update
    if (updateData.status !== undefined) updateValues.status = updateData.status;
    if (updateData.reviewedById !== undefined) updateValues.reviewedById = updateData.reviewedById;
    if (updateData.reviewedAt !== undefined) updateValues.reviewedAt = updateData.reviewedAt;
    if (updateData.referenceType !== undefined) updateValues.referenceType = updateData.referenceType;
    if (updateData.reference !== undefined) updateValues.reference = updateData.reference;
    if (updateData.notes !== undefined) updateValues.notes = updateData.notes;
    if (updateData.issueDate !== undefined) updateValues.issueDate = updateData.issueDate;
    if (updateData.expiryDate !== undefined) updateValues.expiryDate = updateData.expiryDate;
    if (updateData.issuingAuthorityId !== undefined) updateValues.issuingAuthorityId = updateData.issuingAuthorityId;
    if (updateData.verificationId !== undefined) updateValues.verificationId = updateData.verificationId;

    const updated = await db
      .update(complianceRequirement)
      .set(updateValues)
      .where(eq(complianceRequirement.id, requirementId))
      .returning();

    if (updated.length === 0) return null;

    return await getComplianceRequirementByIdDrizzle(requirementId);
  } catch (error) {
    console.error("Error updating compliance requirement:", error);
    throw error;
  }
}

// Delete compliance requirement
export async function deleteComplianceRequirementDrizzle(requirementId: number): Promise<boolean> {
  try {
    const deleted = await db
      .delete(complianceRequirement)
      .where(eq(complianceRequirement.id, requirementId))
      .returning();

    return deleted.length > 0;
  } catch (error) {
    console.error("Error deleting compliance requirement:", error);
    throw error;
  }
}

// ==================== QUERY OPERATIONS ====================

// Get all compliance requirements with pagination and filtering
export async function getAllComplianceRequirementsDrizzle(options: {
  page?: number;
  limit?: number;
  partyId?: number;
  complianceSetId?: number;
  status?: "ACCEPTED" | "REJECTED" | "INCOMPLETE" | "PENDING";
  expiringSoon?: boolean; // Within 30 days
  expired?: boolean;
  sortBy?: "submittedAt" | "reviewedAt" | "expiryDate" | "createdAt";
  sortOrder?: "asc" | "desc";
} = {}): Promise<{
  records: ComplianceRequirementRead[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  try {
    const {
      page = 1,
      limit = 20,
      partyId,
      complianceSetId,
      status,
      expiringSoon,
      expired,
      sortBy = "submittedAt",
      sortOrder = "desc",
    } = options;

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [];
    
    if (partyId) {
      whereConditions.push(eq(complianceRequirement.partyId, partyId));
    }
    
    if (complianceSetId) {
      whereConditions.push(eq(complianceRequirement.complianceSetId, complianceSetId));
    }
    
    if (status) {
      whereConditions.push(eq(complianceRequirement.status, status));
    }
    
    if (expiringSoon) {
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      whereConditions.push(
        and(
          isNotNull(complianceRequirement.expiryDate),
          lte(complianceRequirement.expiryDate, thirtyDaysFromNow.toISOString()),
          gte(complianceRequirement.expiryDate, new Date().toISOString())
        )
      );
    }
    
    if (expired) {
      whereConditions.push(
        and(
          isNotNull(complianceRequirement.expiryDate),
          lte(complianceRequirement.expiryDate, new Date().toISOString())
        )
      );
    }

    const whereClause = whereConditions.length > 0 
      ? whereConditions.reduce((acc, condition) => and(acc, condition))
      : undefined;

    // Build sort order
    const orderByClause = (() => {
      const column = sortBy === "submittedAt" ? complianceRequirement.submittedAt
                   : sortBy === "reviewedAt" ? complianceRequirement.reviewedAt
                   : sortBy === "expiryDate" ? complianceRequirement.expiryDate
                   : complianceRequirement.createdAt;
      
      return sortOrder === "desc" ? desc(column) : asc(column);
    })();

    // Get total count
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(complianceRequirement)
      .where(whereClause);
    
    const total = totalResult[0].count;

    // Get records with related data
    const records = await db
      .select({
        id: complianceRequirement.id,
        partyId: complianceRequirement.partyId,
        complianceSetId: complianceRequirement.complianceSetId,
        requirementTypeId: complianceRequirement.requirementTypeId,
        reviewedById: complianceRequirement.reviewedById,
        status: complianceRequirement.status,
        referenceType: complianceRequirement.referenceType,
        reference: complianceRequirement.reference,
        submittedAt: complianceRequirement.submittedAt,
        reviewedAt: complianceRequirement.reviewedAt,
        notes: complianceRequirement.notes,
        issueDate: complianceRequirement.issueDate,
        expiryDate: complianceRequirement.expiryDate,
        issuingAuthorityId: complianceRequirement.issuingAuthorityId,
        uploadedIndividualId: complianceRequirement.uploadedIndividualId,
        verificationId: complianceRequirement.verificationId,
        createdAt: complianceRequirement.createdAt,
        updatedAt: complianceRequirement.updatedAt,
        // Related data
        complianceSetName: complianceSet.name,
        complianceSetDescription: complianceSet.description,
        requirementTypeName: complianceRequirementType.name,
        requirementTypeDescription: complianceRequirementType.description,
        issuingAuthorityName: issuingAuthority.name,
        issuingAuthorityCountry: issuingAuthority.country,
        partyIndividualFirstName: individual.firstName,
        partyIndividualLastName: individual.lastName,
      })
      .from(complianceRequirement)
      .leftJoin(complianceSet, eq(complianceRequirement.complianceSetId, complianceSet.id))
      .leftJoin(complianceRequirementType, eq(complianceRequirement.requirementTypeId, complianceRequirementType.id))
      .leftJoin(issuingAuthority, eq(complianceRequirement.issuingAuthorityId, issuingAuthority.id))
      .leftJoin(party, eq(complianceRequirement.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    const formattedRecords: ComplianceRequirementRead[] = records.map(record => ({
      id: record.id,
      partyId: record.partyId,
      complianceSetId: record.complianceSetId,
      requirementTypeId: record.requirementTypeId,
      reviewedById: record.reviewedById || undefined,
      status: record.status as "ACCEPTED" | "REJECTED" | "INCOMPLETE" | "PENDING" | undefined,
      referenceType: record.referenceType as "TAX_PIN" | "URL" | undefined,
      reference: record.reference || undefined,
      submittedAt: record.submittedAt || undefined,
      reviewedAt: record.reviewedAt || undefined,
      notes: record.notes || undefined,
      issueDate: record.issueDate || undefined,
      expiryDate: record.expiryDate || undefined,
      issuingAuthorityId: record.issuingAuthorityId || undefined,
      uploadedIndividualId: record.uploadedIndividualId || undefined,
      verificationId: record.verificationId || undefined,
      createdAt: record.createdAt || undefined,
      updatedAt: record.updatedAt || undefined,
      complianceSet: record.complianceSetName ? {
        id: record.complianceSetId,
        name: record.complianceSetName,
        description: record.complianceSetDescription || undefined,
      } : undefined,
      requirementType: record.requirementTypeName ? {
        id: record.requirementTypeId,
        name: record.requirementTypeName,
        description: record.requirementTypeDescription || undefined,
      } : undefined,
      issuingAuthority: record.issuingAuthorityName ? {
        id: record.issuingAuthorityId!,
        name: record.issuingAuthorityName,
        country: record.issuingAuthorityCountry || undefined,
      } : undefined,
      party: record.partyIndividualFirstName && record.partyIndividualLastName ? {
        id: record.partyId,
        individual: {
          firstName: record.partyIndividualFirstName,
          lastName: record.partyIndividualLastName,
        },
      } : undefined,
    }));

    return {
      records: formattedRecords,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  } catch (error) {
    console.error("Error fetching compliance requirements:", error);
    throw error;
  }
}

// Get compliance statistics
export async function getComplianceStatsDrizzle(): Promise<ComplianceStats> {
  try {
    const now = new Date().toISOString();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    const stats = await db
      .select({
        totalRequirements: sql<number>`count(*)`,
        pending: sql<number>`count(*) filter (where status = 'PENDING')`,
        accepted: sql<number>`count(*) filter (where status = 'ACCEPTED')`,
        rejected: sql<number>`count(*) filter (where status = 'REJECTED')`,
        incomplete: sql<number>`count(*) filter (where status = 'INCOMPLETE')`,
        expiringSoon: sql<number>`count(*) filter (where expiry_date is not null and expiry_date <= ${thirtyDaysFromNow.toISOString()} and expiry_date >= ${now})`,
        expired: sql<number>`count(*) filter (where expiry_date is not null and expiry_date < ${now})`,
        totalPartiesWithCompliance: sql<number>`count(distinct party_id)`,
      })
      .from(complianceRequirement);

    return stats[0];
  } catch (error) {
    console.error("Error fetching compliance statistics:", error);
    throw error;
  }
}

// ==================== COMPLIANCE SETS ====================

// Get all compliance sets with their requirement types
export async function getAllComplianceSetsDrizzle(): Promise<ComplianceSetRead[]> {
  try {
    const sets = await db
      .select({
        id: complianceSet.id,
        name: complianceSet.name,
        description: complianceSet.description,
        createdAt: complianceSet.createdAt,
        updatedAt: complianceSet.updatedAt,
      })
      .from(complianceSet)
      .orderBy(asc(complianceSet.name));

    // Get requirement types for each set
    const setsWithTypes = await Promise.all(
      sets.map(async (set) => {
        const types = await db
          .select({
            id: complianceRequirementType.id,
            name: complianceRequirementType.name,
            description: complianceRequirementType.description,
            defaultValidityInDays: complianceRequirementType.defaultValidityInDays,
            createdAt: complianceRequirementType.createdAt,
            updatedAt: complianceRequirementType.updatedAt,
          })
          .from(complianceRequirementType)
          .innerJoin(
            complianceSetRequirementTypeMapping,
            eq(complianceRequirementType.id, complianceSetRequirementTypeMapping.complianceRequirementTypeId)
          )
          .where(eq(complianceSetRequirementTypeMapping.complianceSetId, set.id))
          .orderBy(asc(complianceRequirementType.name));

        return {
          id: set.id,
          name: set.name,
          description: set.description || undefined,
          createdAt: set.createdAt || undefined,
          updatedAt: set.updatedAt || undefined,
          requirementTypes: types.map(type => ({
            id: type.id,
            name: type.name,
            description: type.description || undefined,
            defaultValidityInDays: type.defaultValidityInDays || undefined,
            createdAt: type.createdAt || undefined,
            updatedAt: type.updatedAt || undefined,
          })),
        };
      })
    );

    return setsWithTypes;
  } catch (error) {
    console.error("Error fetching compliance sets:", error);
    throw error;
  }
}

// ==================== REQUIREMENT TYPES ====================

// Get all compliance requirement types
export async function getAllComplianceRequirementTypesDrizzle(): Promise<ComplianceRequirementTypeRead[]> {
  try {
    const types = await db
      .select({
        id: complianceRequirementType.id,
        name: complianceRequirementType.name,
        description: complianceRequirementType.description,
        defaultValidityInDays: complianceRequirementType.defaultValidityInDays,
        createdAt: complianceRequirementType.createdAt,
        updatedAt: complianceRequirementType.updatedAt,
      })
      .from(complianceRequirementType)
      .orderBy(asc(complianceRequirementType.name));

    return types.map(type => ({
      id: type.id,
      name: type.name,
      description: type.description || undefined,
      defaultValidityInDays: type.defaultValidityInDays || undefined,
      createdAt: type.createdAt || undefined,
      updatedAt: type.updatedAt || undefined,
    }));
  } catch (error) {
    console.error("Error fetching compliance requirement types:", error);
    throw error;
  }
}

// ==================== ISSUING AUTHORITIES ====================

// Get all issuing authorities
export async function getAllIssuingAuthoritiesDrizzle(): Promise<IssuingAuthorityRead[]> {
  try {
    const authorities = await db
      .select({
        id: issuingAuthority.id,
        partyId: issuingAuthority.partyId,
        name: issuingAuthority.name,
        description: issuingAuthority.description,
        country: issuingAuthority.country,
        createdAt: issuingAuthority.createdAt,
        updatedAt: issuingAuthority.updatedAt,
      })
      .from(issuingAuthority)
      .orderBy(asc(issuingAuthority.name));

    return authorities.map(authority => ({
      id: authority.id,
      partyId: authority.partyId,
      name: authority.name,
      description: authority.description || undefined,
      country: authority.country || undefined,
      createdAt: authority.createdAt || undefined,
      updatedAt: authority.updatedAt || undefined,
    }));
  } catch (error) {
    console.error("Error fetching issuing authorities:", error);
    throw error;
  }
}

// ==================== APPROVAL WORKFLOW ====================

// Approve compliance requirement
export async function approveComplianceRequirementDrizzle(
  requirementId: number,
  reviewerId: number,
  notes?: string
): Promise<ComplianceRequirementRead | null> {
  try {
    return await updateComplianceRequirementDrizzle(requirementId, {
      status: "ACCEPTED",
      reviewedById: reviewerId,
      reviewedAt: new Date().toISOString(),
      notes: notes,
    });
  } catch (error) {
    console.error("Error approving compliance requirement:", error);
    throw error;
  }
}

// Reject compliance requirement
export async function rejectComplianceRequirementDrizzle(
  requirementId: number,
  reviewerId: number,
  notes?: string
): Promise<ComplianceRequirementRead | null> {
  try {
    return await updateComplianceRequirementDrizzle(requirementId, {
      status: "REJECTED",
      reviewedById: reviewerId,
      reviewedAt: new Date().toISOString(),
      notes: notes,
    });
  } catch (error) {
    console.error("Error rejecting compliance requirement:", error);
    throw error;
  }
}

// Get compliance requirements for a specific party
export async function getComplianceRequirementsByPartyIdDrizzle(
  partyId: number,
  options: {
    status?: "ACCEPTED" | "REJECTED" | "INCOMPLETE" | "PENDING";
    limit?: number;
  } = {}
): Promise<ComplianceRequirementRead[]> {
  try {
    const result = await getAllComplianceRequirementsDrizzle({
      partyId,
      status: options.status,
      limit: options.limit || 50,
      sortBy: "submittedAt",
      sortOrder: "desc",
    });

    return result.records;
  } catch (error) {
    console.error("Error fetching compliance requirements by party ID:", error);
    throw error;
  }
} 