"use server";

/**
 * DRIZZLE ACTIONS - BOOKINGS
 * 
 * This file contains all direct database operations for bookings using Drizzle ORM.
 * 
 * Architecture:
 * - drizzle-actions/* = Direct database queries using Drizzle ORM
 * - actions/* = Legacy API calls using axios (maintained for backward compatibility)
 * 
 * Use these functions when you need direct database access for better performance
 * and type safety. For API compatibility, use the functions in actions/bookings.ts
 */

import { db } from "../db";
import { eq, and, or, sql, desc, asc, gte, lte, between } from "drizzle-orm";
import {
  bookings,
  vehicles,
  vehiclePossessions,
  party,
  individual,
  vehicleModel,
  vehicleMake,
  contactPoint,
  contactPointType,
} from "../drizzle/schema";
import type { BookingCreate, BookingRead, BookingUpdate } from "@/types/bookings";

// ==================== BOOKING CRUD OPERATIONS ====================

// Create a new booking
export async function createBookingDrizzle(
  bookingData: BookingCreate
): Promise<BookingRead> {
  try {
    // Map status to correct enum value
    let statusEnum: "PENDING" | "CONFIRMED" | "CANCELLED" | "COMPLETED";
    
    if (typeof bookingData.status === 'string') {
      statusEnum = bookingData.status.toUpperCase() as "PENDING" | "CONFIRMED" | "CANCELLED" | "COMPLETED";
    } else {
      // Handle numeric enum values from BookingStatus
      switch (bookingData.status) {
        case 0:
          statusEnum = "PENDING";
          break;
        case 1:
          statusEnum = "CONFIRMED";
          break;
        case 2:
          statusEnum = "CANCELLED";
          break;
        case 3:
          statusEnum = "COMPLETED";
          break;
        default:
          statusEnum = "PENDING";
      }
    }

    const newBooking = await db
      .insert(bookings)
      .values({
        vehicleId: bookingData.vehicle_id,
        reference: bookingData.reference,
        startDatetime: bookingData.start_datetime,
        endDatetime: bookingData.end_datetime,
        status: statusEnum,
        totalPrice: bookingData.total_price,
        notes: bookingData.notes,
        partyId: bookingData.party_id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    return {
      id: newBooking[0].id,
      vehicle_id: newBooking[0].vehicleId,
      reference: newBooking[0].reference,
      start_datetime: newBooking[0].startDatetime,
      end_datetime: newBooking[0].endDatetime,
      status: newBooking[0].status as any,
      total_price: newBooking[0].totalPrice,
      notes: newBooking[0].notes,
      party_id: newBooking[0].partyId,
      created_at: newBooking[0].createdAt || new Date().toISOString(),
      updated_at: newBooking[0].updatedAt || new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error creating booking:", error);
    throw error;
  }
}

// Get a single booking by ID with all related data
export async function getBookingByIdDrizzle(bookingId: number): Promise<BookingRead | null> {
  try {
    const booking = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(eq(bookings.id, bookingId))
      .limit(1);

    if (booking.length === 0) return null;

    return {
      ...booking[0],
      status: booking[0].status as any,
      created_at: booking[0].created_at || new Date().toISOString(),
      updated_at: booking[0].updated_at || new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error fetching booking:", error);
    throw error;
  }
}

// Update a booking
export async function updateBookingDrizzle(
  bookingId: number,
  updateData: Partial<BookingUpdate>
): Promise<BookingRead | null> {
  try {
    // Prepare update object with proper enum handling
    const updateValues: any = {
      updatedAt: new Date().toISOString(),
    };

    // Handle each field individually
    if (updateData.vehicle_id) updateValues.vehicleId = updateData.vehicle_id;
    if (updateData.reference) updateValues.reference = updateData.reference;
    if (updateData.start_datetime) updateValues.startDatetime = updateData.start_datetime;
    if (updateData.end_datetime) updateValues.endDatetime = updateData.end_datetime;
    if (updateData.total_price !== undefined) updateValues.totalPrice = updateData.total_price;
    if (updateData.notes !== undefined) updateValues.notes = updateData.notes;
    if (updateData.party_id) updateValues.partyId = updateData.party_id;
    
    // Handle status enum properly
    if (updateData.status) {
      if (typeof updateData.status === 'string') {
        updateValues.status = updateData.status.toUpperCase() as "PENDING" | "CONFIRMED" | "CANCELLED" | "COMPLETED";
      } else {
        // Handle numeric enum values
        switch (updateData.status) {
          case 0:
            updateValues.status = "PENDING";
            break;
          case 1:
            updateValues.status = "CONFIRMED";
            break;
          case 2:
            updateValues.status = "CANCELLED";
            break;
          case 3:
            updateValues.status = "COMPLETED";
            break;
          default:
            updateValues.status = "PENDING";
        }
      }
    }

    const updated = await db
      .update(bookings)
      .set(updateValues)
      .where(eq(bookings.id, bookingId))
      .returning();

    if (updated.length === 0) return null;

    return {
      id: updated[0].id,
      vehicle_id: updated[0].vehicleId,
      reference: updated[0].reference,
      start_datetime: updated[0].startDatetime,
      end_datetime: updated[0].endDatetime,
      status: updated[0].status as any,
      total_price: updated[0].totalPrice,
      notes: updated[0].notes,
      party_id: updated[0].partyId,
      created_at: updated[0].createdAt || new Date().toISOString(),
      updated_at: updated[0].updatedAt || new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error updating booking:", error);
    throw error;
  }
}

// Delete a booking
export async function deleteBookingDrizzle(bookingId: number): Promise<boolean> {
  try {
    const deleted = await db
      .delete(bookings)
      .where(eq(bookings.id, bookingId))
      .returning({ id: bookings.id });

    return deleted.length > 0;
  } catch (error) {
    console.error("Error deleting booking:", error);
    throw error;
  }
}

// ==================== BOOKING QUERIES ====================

// Check for booking overlaps using Drizzle
export async function checkBookingOverlapDrizzle(
  vehicleId: number,
  startDatetime: string,
  endDatetime: string,
  excludeBookingId?: number
): Promise<boolean> {
  try {
    const conditions = [
      eq(bookings.vehicleId, vehicleId),
      or(
        eq(bookings.status, "CONFIRMED"),
        eq(bookings.status, "PENDING")
      ),
      // Check for overlap: new booking overlaps if it starts before existing ends and ends after existing starts
      and(
        sql`${bookings.startDatetime} < ${endDatetime}`,
        sql`${bookings.endDatetime} > ${startDatetime}`
      )
    ];

    // Exclude specific booking if updating
    if (excludeBookingId) {
      conditions.push(sql`${bookings.id} != ${excludeBookingId}`);
    }

    const overlappingBookings = await db
      .select({ id: bookings.id })
      .from(bookings)
      .where(and(...conditions))
      .limit(1);

    return overlappingBookings.length > 0;
  } catch (error) {
    console.error("Error checking booking overlap:", error);
    throw error;
  }
}

// Get bookings for a vehicle with related data
export async function getVehicleBookingsWithDetails(vehicleId: number): Promise<BookingRead[]> {
  try {
    const vehicleBookings = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(eq(bookings.vehicleId, vehicleId))
      .orderBy(desc(bookings.startDatetime));

    return vehicleBookings.map(booking => ({
      ...booking,
      status: booking.status as any // Type assertion to handle enum mapping
    })) as BookingRead[];
  } catch (error) {
    console.error("Error fetching vehicle bookings:", error);
    throw error;
  }
}

// Get bookings for a party (user)
export async function getPartyBookingsWithDetails(partyId: number): Promise<BookingRead[]> {
  try {
    const partyBookings = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(eq(bookings.partyId, partyId))
      .orderBy(desc(bookings.startDatetime));

    return partyBookings.map(booking => ({
      ...booking,
      status: booking.status as any
    })) as BookingRead[];
  } catch (error) {
    console.error("Error fetching party bookings:", error);
    throw error;
  }
}

// Get active bookings for a vehicle (confirmed or pending)
export async function getActiveVehicleBookings(vehicleId: number): Promise<BookingRead[]> {
  try {
    const activeBookings = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(
        and(
          eq(bookings.vehicleId, vehicleId),
          or(
            eq(bookings.status, "CONFIRMED"),
            eq(bookings.status, "PENDING")
          )
        )
      )
      .orderBy(asc(bookings.startDatetime));

    return activeBookings.map(booking => ({
      ...booking,
      status: booking.status as any
    })) as BookingRead[];
  } catch (error) {
    console.error("Error fetching active vehicle bookings:", error);
    throw error;
  }
}

// Get bookings within a date range
export async function getBookingsInDateRange(
  startDate: string,
  endDate: string,
  vehicleId?: number
): Promise<BookingRead[]> {
  try {
    const conditions = [
      and(
        gte(bookings.startDatetime, startDate),
        lte(bookings.endDatetime, endDate)
      )
    ];

    if (vehicleId) {
      conditions.push(eq(bookings.vehicleId, vehicleId));
    }

    const rangeBookings = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(and(...conditions))
      .orderBy(asc(bookings.startDatetime));

    return rangeBookings.map(booking => ({
      ...booking,
      status: booking.status as any
    })) as BookingRead[];
  } catch (error) {
    console.error("Error fetching bookings in date range:", error);
    throw error;
  }
}

// ==================== VEHICLE POSSESSION INTEGRATION ====================

// Get current vehicle possession status - who actually has the car
export async function getCurrentVehiclePossession(vehicleId: number) {
  try {
    const currentPossession = await db
      .select({
        id: vehiclePossessions.id,
        fromPartyId: vehiclePossessions.fromPartyId,
        toPartyId: vehiclePossessions.toPartyId,
        vehicleId: vehiclePossessions.vehicleId,
        handoverExpectedDatetime: vehiclePossessions.handoverExpectedDatetime,
        handoverActualDatetime: vehiclePossessions.handoverActualDatetime,
        status: vehiclePossessions.status,
      })
      .from(vehiclePossessions)
      .where(
        and(
          eq(vehiclePossessions.vehicleId, vehicleId),
          eq(vehiclePossessions.status, "COMPLETED")
        )
      )
      .orderBy(desc(vehiclePossessions.handoverActualDatetime))
      .limit(1);

    return currentPossession[0] || null;
  } catch (error) {
    console.error("Error fetching current vehicle possession:", error);
    throw error;
  }
}

// Get the party that currently has possession of the vehicle
export async function getCurrentVehicleHolder(vehicleId: number): Promise<number | null> {
  try {
    const latestPossession = await getCurrentVehiclePossession(vehicleId);
    
    // If there's a completed possession, the 'toPartyId' is who currently has the car
    // If no possession records exist, the vehicle owner (from vehicles table) has it
    if (latestPossession) {
      return latestPossession.toPartyId;
    }
    
    // Get the vehicle owner as fallback
    const vehicle = await db
      .select({ partyId: vehicles.partyId })
      .from(vehicles)
      .where(eq(vehicles.id, vehicleId))
      .limit(1);
    
    return vehicle[0]?.partyId || null;
  } catch (error) {
    console.error("Error getting current vehicle holder:", error);
    throw error;
  }
}

// ==================== VEHICLE AVAILABILITY ====================

// Get vehicle availability status considering both bookings and current possession
export async function getVehicleAvailabilityStatus(vehicleId: number) {
  try {
    const now = new Date().toISOString();
    
    // Check for active bookings
    const activeBooking = await db
      .select({ 
        id: bookings.id, 
        status: bookings.status,
        partyId: bookings.partyId,
        startDatetime: bookings.startDatetime,
        endDatetime: bookings.endDatetime
      })
      .from(bookings)
      .where(
        and(
          eq(bookings.vehicleId, vehicleId),
          or(
            eq(bookings.status, "CONFIRMED"),
            eq(bookings.status, "PENDING")
          ),
          sql`${bookings.startDatetime} <= ${now}`,
          sql`${bookings.endDatetime} >= ${now}`
        )
      )
      .limit(1);

    if (activeBooking.length > 0) {
      return {
        status: "in-use",
        reason: "Active booking",
        bookingId: activeBooking[0].id,
        currentHolder: activeBooking[0].partyId,
      };
    }

    // Check who currently has the vehicle (based on completed possessions)
    const currentHolder = await getCurrentVehicleHolder(vehicleId);

    // Check vehicle maintenance status
    const vehicle = await db
      .select({ 
        isActive: vehicles.isActive,
        partyId: vehicles.partyId
      })
      .from(vehicles)
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    if (vehicle.length === 0) {
      return {
        status: "not-found",
        reason: "Vehicle not found",
      };
    }

    if (!vehicle[0].isActive) {
      return {
        status: "maintenance",
        reason: "Vehicle under maintenance",
        currentHolder,
      };
    }

    return {
      status: "available",
      reason: "Vehicle is available for booking",
      currentHolder,
      vehicleOwner: vehicle[0].partyId,
    };
  } catch (error) {
    console.error("Error checking vehicle availability:", error);
    throw error;
  }
}

// ==================== BOOKING ANALYTICS ====================

// Get booking statistics for a vehicle
export async function getVehicleBookingStats(vehicleId: number) {
  try {
    const stats = await db
      .select({
        total_bookings: sql<number>`count(*)`,
        confirmed_bookings: sql<number>`sum(case when status = 'CONFIRMED' then 1 else 0 end)`,
        pending_bookings: sql<number>`sum(case when status = 'PENDING' then 1 else 0 end)`,
        completed_bookings: sql<number>`sum(case when status = 'COMPLETED' then 1 else 0 end)`,
        cancelled_bookings: sql<number>`sum(case when status = 'CANCELLED' then 1 else 0 end)`,
        total_revenue: sql<number>`sum(total_price)`,
      })
      .from(bookings)
      .where(eq(bookings.vehicleId, vehicleId));

    return stats[0];
  } catch (error) {
    console.error("Error fetching vehicle booking stats:", error);
    throw error;
  }
}

// Get upcoming bookings for a vehicle
export async function getUpcomingVehicleBookings(vehicleId: number, limit: number = 10): Promise<BookingRead[]> {
  try {
    const now = new Date().toISOString();
    
    const upcomingBookings = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(
        and(
          eq(bookings.vehicleId, vehicleId),
          or(
            eq(bookings.status, "CONFIRMED"),
            eq(bookings.status, "PENDING")
          ),
          gte(bookings.startDatetime, now)
        )
      )
      .orderBy(asc(bookings.startDatetime))
      .limit(limit);

    return upcomingBookings.map(booking => ({
      ...booking,
      status: booking.status as any
    })) as BookingRead[];
  } catch (error) {
    console.error("Error fetching upcoming vehicle bookings:", error);
    throw error;
  }
}

// ==================== FORM SUBMISSION ACTIONS ====================

// Server action for form-based booking creation (replaces legacy API action)
export async function addBookingDrizzle(_: any, formData: FormData) {
  try {
    const start_datetime = (formData.get("start_datetime") as string)?.trim();
    const end_datetime = (formData.get("end_datetime") as string)?.trim();
    const status = (formData.get("status") as string)?.trim();
    const reference = (formData.get("reference") as string)?.trim();
    const vehicle_id = Number(formData.get("vehicle_id"));
    const party_id = Number(formData.get("party_id"));

    // Validation
    if (!start_datetime || !end_datetime || !vehicle_id || !party_id) {
      return {
        success: false,
        errors: {
          general: ["Missing required fields"]
        }
      };
    }

    // Enhanced availability and permission check
    const bookingCheck = await canPartyBookVehicle(vehicle_id, party_id, start_datetime, end_datetime);
    if (!bookingCheck.canBook) {
      return {
        success: false,
        errors: {
          dates: [bookingCheck.reason]
        }
      };
    }

    const bookingData: BookingCreate = {
      vehicle_id,
      reference,
      start_datetime,
      end_datetime,
      party_id,
      status: status as any, // Will be cast to proper enum in createBookingDrizzle
      total_price: null,
      notes: null,
    };
    
    // Create booking using direct DB access
    const booking = await createBookingDrizzle(bookingData);
    
    return {
      success: true,
      message: "Booking created successfully.",
      bookingID: booking.id,
    };
  } catch (error: any) {
    console.error("Error creating booking:", error);
    return {
      success: false,
      errors: {
        general: [error.message || "Failed to create booking"]
      }
    };
  }
}

// Check if a party can book a vehicle for specific dates
export async function canPartyBookVehicle(
  vehicleId: number, 
  partyId: number, 
  startDatetime: string, 
  endDatetime: string
): Promise<{ canBook: boolean; reason: string; currentHolder?: number }> {
  try {
    // Check basic availability
    const availability = await getVehicleAvailabilityStatus(vehicleId);
    
    if (availability.status === "not-found") {
      return { canBook: false, reason: "Vehicle not found" };
    }
    
    if (availability.status === "maintenance") {
      return { 
        canBook: false, 
        reason: "Vehicle is under maintenance", 
        currentHolder: availability.currentHolder || undefined
      };
    }
    
    // Check for booking conflicts
    const hasConflict = await checkBookingOverlapDrizzle(vehicleId, startDatetime, endDatetime);
    if (hasConflict) {
      return { canBook: false, reason: "Date conflict with existing booking" };
    }
    
    // All checks passed
    return { 
      canBook: true, 
      reason: "Vehicle is available for booking",
      currentHolder: availability.currentHolder || undefined
    };
  } catch (error) {
    console.error("Error checking if party can book vehicle:", error);
    return { canBook: false, reason: "Error checking availability" };
  }
} 