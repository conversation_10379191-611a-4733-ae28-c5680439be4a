"use server";

/**
 * DRIZZLE ACTIONS - LISTINGS & VEHICLES
 * 
 * This file contains all direct database operations for listings and vehicles using Drizzle ORM.
 * 
 * Architecture:
 * - drizzle-actions/* = Direct database queries using Drizzle ORM
 * - actions/* = Legacy API calls using axios (maintained for backward compatibility)
 * 
 * Schema Tables Used:
 * - listings: Vehicle listings for co-ownership
 * - vehicles: Vehicle records
 * - vehicleModel: Vehicle model information
 * - vehicleMake: Vehicle manufacturer information
 * - party: Vehicle owners
 * - individual: Owner details
 */

import { db } from "../db";
import { eq, and, desc, asc, gte, lte, between, sql, inArray } from "drizzle-orm";
import {
  listings,
  vehicles,
  vehicleModel,
  vehicleMake,
  party,
  individual,
  listingMedia,
  vehicleMedia,
  listingInterestExpressions,
} from "../drizzle/schema";

// ==================== TYPES ====================

export interface VehicleCreate {
  partyId: number;
  modelId: number;
  vinNumber: string;
  vehicleRegistration?: string;
  countryOfRegistration?: string;
  manufacturingYear?: number;
  purchaseDate?: string;
  color?: string;
}

export interface VehicleRead {
  id: number;
  partyId: number;
  modelId: number;
  vinNumber: string;
  vehicleRegistration?: string;
  countryOfRegistration?: string;
  manufacturingYear?: number;
  purchaseDate?: string;
  color?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  // Related data
  model?: {
    id: number;
    model: string;
    yearModel: number;
    transmission?: string;
    make?: {
      id: number;
      name: string;
    };
  };
  owner?: {
    firstName: string;
    lastName: string;
  };
}

export interface ListingMediaRead {
  id: number;
  listingId: number;
  mediaPath: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ListingCreate {
  partyId: number;
  vehicleId: number;
  effectiveFrom: string;
  effectiveTo: string;
  fractionOffer: number;
  askingPrice: number;
  condition: "new" | "used";
  mileage?: number;
  listingType: "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE";
  audience: "BUSINESS" | "E_HAILING" | "CONSUMER";
}

export interface ListingRead {
  id: number;
  partyId: number;
  vehicleId: number;
  effectiveFrom: string;
  effectiveTo: string;
  fractionOffer: number;
  askingPrice: number;
  condition: "new" | "used";
  mileage?: number;
  listingType: "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE";
  audience: "BUSINESS" | "E_HAILING" | "CONSUMER";
  createdAt?: string;
  updatedAt?: string;
  // Related data
  vehicle?: VehicleRead;
  owner?: {
    firstName: string;
    lastName: string;
  };
  media?: ListingMediaRead[];
  // Interest expressions
  interestCount?: number;
  userHasExpressedInterest?: boolean;
}

export interface ListingFormData {
  // Vehicle details
  make: string;
  model: string;
  year: string;
  color: string;
  mileage: string;
  condition: string;
  location: string;
  description: string;
  vinNumber: string;
  vehicleRegistration?: string;
  
  // Listing details
  fractionSize: string;
  fractions: number;
  pricePerFraction: string;
  listingType: string;
  audience: string;
  effectiveFrom: string;
  effectiveTo: string;
  
  // Images
  vehicleImages?: string[];
  
  // New field for fraction offer
  fractionOffer: string;
}

// ==================== VEHICLE CRUD OPERATIONS ====================

// Create a new vehicle
export async function createVehicleDrizzle(
  vehicleData: VehicleCreate
): Promise<VehicleRead> {
  try {
    const newVehicle = await db
      .insert(vehicles)
      .values({
        partyId: vehicleData.partyId,
        modelId: vehicleData.modelId,
        vinNumber: vehicleData.vinNumber,
        vehicleRegistration: vehicleData.vehicleRegistration,
        countryOfRegistration: vehicleData.countryOfRegistration,
        manufacturingYear: vehicleData.manufacturingYear,
        purchaseDate: vehicleData.purchaseDate,
        color: vehicleData.color,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    return {
      id: newVehicle[0].id,
      partyId: newVehicle[0].partyId,
      modelId: newVehicle[0].modelId,
      vinNumber: newVehicle[0].vinNumber,
      vehicleRegistration: newVehicle[0].vehicleRegistration || undefined,
      countryOfRegistration: newVehicle[0].countryOfRegistration || undefined,
      manufacturingYear: newVehicle[0].manufacturingYear || undefined,
      purchaseDate: newVehicle[0].purchaseDate || undefined,
      color: newVehicle[0].color || undefined,
      isActive: newVehicle[0].isActive,
      createdAt: newVehicle[0].createdAt || undefined,
      updatedAt: newVehicle[0].updatedAt || undefined,
    };
  } catch (error) {
    console.error("Error creating vehicle:", error);
    throw error;
  }
}

// Get vehicle by ID with related data
export async function getVehicleByIdDrizzle(
  vehicleId: number
): Promise<VehicleRead | null> {
  try {
    const vehicle = await db
      .select({
        id: vehicles.id,
        partyId: vehicles.partyId,
        modelId: vehicles.modelId,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        countryOfRegistration: vehicles.countryOfRegistration,
        manufacturingYear: vehicles.manufacturingYear,
        purchaseDate: vehicles.purchaseDate,
        color: vehicles.color,
        isActive: vehicles.isActive,
        createdAt: vehicles.createdAt,
        updatedAt: vehicles.updatedAt,
        // Model details
        modelName: vehicleModel.model,
        modelYear: vehicleModel.yearModel,
        transmission: vehicleModel.transmission,
        // Make details
        makeId: vehicleMake.id,
        makeName: vehicleMake.name,
        // Owner details
        ownerFirstName: individual.firstName,
        ownerLastName: individual.lastName,
      })
      .from(vehicles)
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(vehicles.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    if (vehicle.length === 0) return null;

    const record = vehicle[0];
    
    return {
      id: record.id,
      partyId: record.partyId,
      modelId: record.modelId,
      vinNumber: record.vinNumber,
      vehicleRegistration: record.vehicleRegistration || undefined,
      countryOfRegistration: record.countryOfRegistration || undefined,
      manufacturingYear: record.manufacturingYear || undefined,
      purchaseDate: record.purchaseDate || undefined,
      color: record.color || undefined,
      isActive: record.isActive,
      createdAt: record.createdAt || undefined,
      updatedAt: record.updatedAt || undefined,
      model: record.modelName ? {
        id: record.modelId,
        model: record.modelName,
        yearModel: record.modelYear || 0,
        transmission: record.transmission || undefined,
        make: record.makeName ? {
          id: record.makeId || 0,
          name: record.makeName,
        } : undefined,
      } : undefined,
      owner: record.ownerFirstName && record.ownerLastName ? {
        firstName: record.ownerFirstName,
        lastName: record.ownerLastName,
      } : undefined,
    };
  } catch (error) {
    console.error("Error fetching vehicle:", error);
    throw error;
  }
}

// ==================== LISTING CRUD OPERATIONS ====================

// Create a new listing
export async function createListingDrizzle(
  listingData: ListingCreate
): Promise<ListingRead> {
  try {
    const newListing = await db
      .insert(listings)
      .values({
        partyId: listingData.partyId,
        vehicleId: listingData.vehicleId,
        effectiveFrom: listingData.effectiveFrom,
        effectiveTo: listingData.effectiveTo,
        fractionOffer: listingData.fractionOffer,
        askingPrice: listingData.askingPrice,
        condition: listingData.condition,
        mileage: listingData.mileage,
        listingType: listingData.listingType,
        audience: listingData.audience,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    return {
      id: newListing[0].id,
      partyId: newListing[0].partyId,
      vehicleId: newListing[0].vehicleId,
      effectiveFrom: newListing[0].effectiveFrom,
      effectiveTo: newListing[0].effectiveTo,
      fractionOffer: newListing[0].fractionOffer,
      askingPrice: newListing[0].askingPrice,
      condition: newListing[0].condition as "new" | "used",
      mileage: newListing[0].mileage || undefined,
      listingType: newListing[0].listingType as "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE",
      audience: newListing[0].audience as "BUSINESS" | "E_HAILING" | "CONSUMER",
      createdAt: newListing[0].createdAt || undefined,
      updatedAt: newListing[0].updatedAt || undefined,
    };
  } catch (error) {
    console.error("Error creating listing:", error);
    throw error;
  }
}

// Get listing by ID with related data
export async function getListingByIdDrizzle(
  listingId: number
): Promise<ListingRead | null> {
  try {
    const listing = await db
      .select({
        id: listings.id,
        partyId: listings.partyId,
        vehicleId: listings.vehicleId,
        effectiveFrom: listings.effectiveFrom,
        effectiveTo: listings.effectiveTo,
        fractionOffer: listings.fractionOffer,
        askingPrice: listings.askingPrice,
        condition: listings.condition,
        mileage: listings.mileage,
        listingType: listings.listingType,
        audience: listings.audience,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleColor: vehicles.color,
        vehicleYear: vehicles.manufacturingYear,
        // Model details
        modelName: vehicleModel.model,
        modelYear: vehicleModel.yearModel,
        // Make details
        makeName: vehicleMake.name,
        // Owner details
        ownerFirstName: individual.firstName,
        ownerLastName: individual.lastName,
      })
      .from(listings)
      .leftJoin(vehicles, eq(listings.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(listings.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(listings.id, listingId))
      .limit(1);

    if (listing.length === 0) return null;

    const record = listing[0];
    
    return {
      id: record.id,
      partyId: record.partyId,
      vehicleId: record.vehicleId,
      effectiveFrom: record.effectiveFrom,
      effectiveTo: record.effectiveTo,
      fractionOffer: record.fractionOffer,
      askingPrice: record.askingPrice,
      condition: record.condition as "new" | "used",
      mileage: record.mileage || undefined,
      listingType: record.listingType as "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE",
      audience: record.audience as "BUSINESS" | "E_HAILING" | "CONSUMER",
      createdAt: record.createdAt || undefined,
      updatedAt: record.updatedAt || undefined,
      vehicle: record.vehicleVin ? {
        id: record.vehicleId,
        partyId: record.partyId,
        modelId: 0, // We'd need to join more to get this
        vinNumber: record.vehicleVin,
        vehicleRegistration: record.vehicleRegistration || undefined,
        color: record.vehicleColor || undefined,
        manufacturingYear: record.vehicleYear || undefined,
        isActive: true,
        model: record.modelName ? {
          id: 0,
          model: record.modelName,
          yearModel: record.modelYear || 0,
          make: record.makeName ? {
            id: 0,
            name: record.makeName,
          } : undefined,
        } : undefined,
      } : undefined,
      owner: record.ownerFirstName && record.ownerLastName ? {
        firstName: record.ownerFirstName,
        lastName: record.ownerLastName,
      } : undefined,
    };
  } catch (error) {
    console.error("Error fetching listing:", error);
    throw error;
  }
}

// Get all listings with filters
export async function getAllListingsDrizzle(options: {
  page?: number;
  limit?: number;
  audience?: "BUSINESS" | "E_HAILING" | "CONSUMER";
  listingType?: "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE";
  condition?: "new" | "used";
  maxPrice?: number;
  minPrice?: number;
  sortBy?: "createdAt" | "askingPrice" | "fractionOffer";
  sortOrder?: "asc" | "desc";
} = {}): Promise<{
  records: ListingRead[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  try {
    const {
      page = 1,
      limit = 20,
      audience,
      listingType,
      condition,
      maxPrice,
      minPrice,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const offset = (page - 1) * limit;

    // Build where clause
    let whereConditions = [];
    
    if (audience) {
      whereConditions.push(eq(listings.audience, audience));
    }
    if (listingType) {
      whereConditions.push(eq(listings.listingType, listingType));
    }
    if (condition) {
      whereConditions.push(eq(listings.condition, condition));
    }
    if (maxPrice) {
      whereConditions.push(lte(listings.askingPrice, maxPrice));
    }
    if (minPrice) {
      whereConditions.push(gte(listings.askingPrice, minPrice));
    }
    
    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Build sort order
    const orderByClause = (() => {
      const column = sortBy === "askingPrice" ? listings.askingPrice
                   : sortBy === "fractionOffer" ? listings.fractionOffer
                   : listings.createdAt;
      
      return sortOrder === "desc" ? desc(column) : asc(column);
    })();

    // Get total count
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(listings)
      .where(whereClause);
    
    const total = totalResult[0].count;

    // Get records with related data
    const records = await db
      .select({
        id: listings.id,
        partyId: listings.partyId,
        vehicleId: listings.vehicleId,
        effectiveFrom: listings.effectiveFrom,
        effectiveTo: listings.effectiveTo,
        fractionOffer: listings.fractionOffer,
        askingPrice: listings.askingPrice,
        condition: listings.condition,
        mileage: listings.mileage,
        listingType: listings.listingType,
        audience: listings.audience,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleColor: vehicles.color,
        vehicleYear: vehicles.manufacturingYear,
        // Model details
        modelName: vehicleModel.model,
        modelYear: vehicleModel.yearModel,
        // Make details
        makeName: vehicleMake.name,
        // Owner details
        ownerFirstName: individual.firstName,
        ownerLastName: individual.lastName,
      })
      .from(listings)
      .leftJoin(vehicles, eq(listings.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(listings.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    // Get listing media for all listings
    const listingIds = records.map(record => record.id);
    const mediaRecords = listingIds.length > 0 ? await db
      .select({
        id: listingMedia.id,
        listingId: listingMedia.listingId,
        mediaPath: listingMedia.mediaPath,
        createdAt: listingMedia.createdAt,
        updatedAt: listingMedia.updatedAt,
      })
      .from(listingMedia)
      .where(inArray(listingMedia.listingId, listingIds)) : [];

    // Group media by listing ID
    const mediaByListingId = mediaRecords.reduce((acc, media) => {
      if (!acc[media.listingId]) {
        acc[media.listingId] = [];
      }
      acc[media.listingId].push({
        id: media.id,
        listingId: media.listingId,
        mediaPath: media.mediaPath,
        createdAt: media.createdAt || undefined,
        updatedAt: media.updatedAt || undefined,
      });
      return acc;
    }, {} as Record<number, ListingMediaRead[]>);

    const formattedRecords: ListingRead[] = records.map(record => ({
      id: record.id,
      partyId: record.partyId,
      vehicleId: record.vehicleId,
      effectiveFrom: record.effectiveFrom,
      effectiveTo: record.effectiveTo,
      fractionOffer: record.fractionOffer,
      askingPrice: record.askingPrice,
      condition: record.condition as "new" | "used",
      mileage: record.mileage || undefined,
      listingType: record.listingType as "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE",
      audience: record.audience as "BUSINESS" | "E_HAILING" | "CONSUMER",
      createdAt: record.createdAt || undefined,
      updatedAt: record.updatedAt || undefined,
      vehicle: record.vehicleVin ? {
        id: record.vehicleId,
        partyId: record.partyId,
        modelId: 0,
        vinNumber: record.vehicleVin,
        vehicleRegistration: record.vehicleRegistration || undefined,
        color: record.vehicleColor || undefined,
        manufacturingYear: record.vehicleYear || undefined,
        isActive: true,
        model: record.modelName ? {
          id: 0,
          model: record.modelName,
          yearModel: record.modelYear || 0,
          make: record.makeName ? {
            id: 0,
            name: record.makeName,
          } : undefined,
        } : undefined,
      } : undefined,
      owner: record.ownerFirstName && record.ownerLastName ? {
        firstName: record.ownerFirstName,
        lastName: record.ownerLastName,
      } : undefined,
      media: mediaByListingId[record.id] || [],
    }));

    return {
      records: formattedRecords,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  } catch (error) {
    console.error("Error fetching listings:", error);
    throw error;
  }
}

// ==================== COMBINED OPERATIONS ====================

// Create vehicle and listing together from form data
export async function createVehicleAndListingDrizzle(
  formData: ListingFormData,
  partyId: number
): Promise<{ vehicle: VehicleRead; listing: ListingRead }> {
  try {
    // Look up vehicle make by name
    const vehicleMakes = await db
      .select({ id: vehicleMake.id, name: vehicleMake.name })
      .from(vehicleMake)
      .where(eq(vehicleMake.name, formData.make))
      .limit(1);

    let makeId: number;
    if (vehicleMakes.length === 0) {
      // Create new make if it doesn't exist
      const newMake = await db
        .insert(vehicleMake)
        .values({
          name: formData.make,
          description: `${formData.make} vehicles`,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();
      makeId = newMake[0].id;
    } else {
      makeId = vehicleMakes[0].id;
    }

    // Look up vehicle model by name and make
    const vehicleModels = await db
      .select({ id: vehicleModel.id })
      .from(vehicleModel)
      .where(
        and(
          eq(vehicleModel.makeId, makeId),
          eq(vehicleModel.model, formData.model),
          eq(vehicleModel.yearModel, parseInt(formData.year))
        )
      )
      .limit(1);

    let modelId: number;
    if (vehicleModels.length === 0) {
      // Create new model if it doesn't exist
      const newModel = await db
        .insert(vehicleModel)
        .values({
          makeId: makeId,
          model: formData.model,
          yearModel: parseInt(formData.year),
          description: `${formData.make} ${formData.model} ${formData.year}`,
          transmission: undefined, // Could be added to form later
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();
      modelId = newModel[0].id;
    } else {
      modelId = vehicleModels[0].id;
    }
    
    // Use fraction offer from form data, fallback to calculated value
    const fractionOffer = formData.fractionOffer 
      ? parseFloat(formData.fractionOffer)
      : formData.fractionSize === "equal" 
        ? 1 / formData.fractions 
        : 0.25; // Default for custom fractions
    
    // Create vehicle first
    const vehicleData: VehicleCreate = {
      partyId,
      modelId,
      vinNumber: formData.vinNumber || `VIN${Date.now()}`, // Generate VIN if not provided
      vehicleRegistration: formData.vehicleRegistration,
      countryOfRegistration: "South Africa",
      manufacturingYear: parseInt(formData.year),
      color: formData.color,
    };
    
    const vehicle = await createVehicleDrizzle(vehicleData);
    
    // Create listing
    const listingData: ListingCreate = {
      partyId,
      vehicleId: vehicle.id,
      effectiveFrom: formData.effectiveFrom || new Date().toISOString().split('T')[0],
      effectiveTo: formData.effectiveTo || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year from now
      fractionOffer,
      askingPrice: parseFloat(formData.pricePerFraction.replace(/[^\d.]/g, '')),
      condition: formData.condition as "new" | "used",
      mileage: parseFloat(formData.mileage.replace(/[^\d.]/g, '')) || undefined,
      listingType: formData.listingType as "CO_OWNERSHIP_SALE",
      audience: formData.audience as "CONSUMER",
    };
    
    const listing = await createListingDrizzle(listingData);
    
    // Save vehicle images to listingMedia table instead of vehicleMedia
    if (formData.vehicleImages && formData.vehicleImages.length > 0) {
      for (const s3Path of formData.vehicleImages) {
        await db.insert(listingMedia).values({
          listingId: listing.id,
          mediaPath: s3Path, // This is now the S3 path from DocumentUpload
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      }
    }
    
    return { vehicle, listing };
  } catch (error) {
    console.error("Error creating vehicle and listing:", error);
    throw error;
  }
} 