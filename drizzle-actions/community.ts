"use server";

import { db } from "../db";
import { eq, sql, count, desc, and } from "drizzle-orm";
import {
  company,
  party,
  individual,
  vehicles,
  bookings,
  companyOwnership,
  vehicleModel,
  vehicleMake,
  cities,
  companyOwnershipInvite,
} from "../drizzle/schema";

// Get all companies (groups) with member and vehicle counts
export async function getPublicGroups() {
  try {
    const groups = await db
      .select({
        id: company.id,
        name: company.alias,
        description: company.description,
        location: cities.name,
        registrationDate: company.registrationDate,
        memberCount: sql<number>`cast(count(distinct ${companyOwnership.partyId}) as integer)`,
        vehicleCount: sql<number>`cast(count(distinct ${vehicles.id}) as integer)`,
      })
      .from(company)
      .leftJoin(cities, eq(company.cityId, cities.id))
      .leftJoin(companyOwnership, eq(company.id, companyOwnership.companyId))
      .leftJoin(vehicles, eq(company.partyId, vehicles.partyId))
      .groupBy(
        company.id,
        company.alias,
        company.description,
        cities.name,
        company.registrationDate
      )
      .orderBy(desc(company.registrationDate));

    return groups.map((group) => ({
      id: group.id,
      name: group.name || "Unnamed Group",
      description: group.description || "No description available",
      members: group.memberCount || 0,
      vehicles: group.vehicleCount || 0,
      location: group.location || "Unknown",
      image: "/placeholder.svg?height=60&width=60",
    }));
  } catch (error) {
    console.error("Error fetching public groups:", error);
    return [];
  }
}

// Get group details by ID
export async function getGroupDetails(groupId: number) {
  try {
    // Get company info
    const groupInfo = await db
      .select({
        id: company.id,
        name: company.alias,
        description: company.description,
        location: cities.name,
        registrationDate: company.registrationDate,
      })
      .from(company)
      .leftJoin(cities, eq(company.cityId, cities.id))
      .where(eq(company.id, groupId))
      .limit(1);

    if (groupInfo.length === 0) {
      return null;
    }

    const group = groupInfo[0];

    // Get members with their details
    const members = await db
      .select({
        id: individual.id,
        partyId: party.id,
        firstName: individual.firstName,
        lastName: individual.lastName,
        fraction: companyOwnership.fraction,
        isActive: companyOwnership.isActive,
      })
      .from(companyOwnership)
      .innerJoin(party, eq(companyOwnership.partyId, party.id))
      .innerJoin(individual, eq(party.id, individual.partyId))
      .where(eq(companyOwnership.companyId, groupId))
      .orderBy(desc(companyOwnership.fraction));

    // Get vehicles
    const groupVehicles = await db
      .select({
        id: vehicles.id,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        color: vehicles.color,
        isActive: vehicles.isActive,
        make: vehicleMake.name,
        model: vehicleModel.model,
        yearModel: vehicleModel.yearModel,
      })
      .from(vehicles)
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .innerJoin(party, eq(vehicles.partyId, party.id))
      .innerJoin(company, eq(party.id, company.partyId))
      .where(eq(company.id, groupId))
      .orderBy(vehicles.id);

    // Get upcoming bookings
    const upcomingBookings = await db
      .select({
        id: bookings.id,
        reference: bookings.reference,
        startDatetime: bookings.startDatetime,
        endDatetime: bookings.endDatetime,
        status: bookings.status,
        vehicleId: vehicles.id,
        make: vehicleMake.name,
        model: vehicleModel.model,
        memberFirstName: individual.firstName,
        memberLastName: individual.lastName,
      })
      .from(bookings)
      .innerJoin(vehicles, eq(bookings.vehicleId, vehicles.id))
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .innerJoin(party, eq(bookings.partyId, party.id))
      .innerJoin(individual, eq(party.id, individual.partyId))
      .innerJoin(company, eq(vehicles.partyId, company.partyId))
      .where(eq(company.id, groupId))
      .orderBy(bookings.startDatetime)
      .limit(10);

    return {
      id: group.id,
      name: group.name || "Unnamed Group",
      description: group.description || "No description available",
      location: group.location || "Unknown",
      members: members.map((member) => ({
        id: member.id,
        partyId: member.partyId,
        name: `${member.firstName} ${member.lastName}`,
        role:
          member.fraction && parseFloat(member.fraction.toString()) > 0.5
            ? "Admin"
            : "Member",
        avatar: "/placeholder.svg?height=40&width=40",
      })),
      vehicles: groupVehicles.map((vehicle) => ({
        id: vehicle.id,
        name: `${vehicle.make} ${vehicle.model} (${vehicle.yearModel})`,
        registration: vehicle.vehicleRegistration,
        status: vehicle.isActive ? "available" : "maintenance",
        image: "/placeholder.svg?height=60&width=100",
      })),
      upcomingBookings: upcomingBookings.map((booking) => ({
        id: booking.id,
        vehicle: `${booking.make} ${booking.model}`,
        member: `${booking.memberFirstName} ${booking.memberLastName}`,
        date: formatBookingDate(booking.startDatetime),
        status: booking.status,
      })),
    };
  } catch (error) {
    console.error("Error fetching group details:", error);
    return null;
  }
}

// Get company vehicles with current booking status
export async function getGroupVehiclesWithStatus(groupId: number) {
  try {
    const groupVehicles = await db
      .select({
        id: vehicles.id,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        color: vehicles.color,
        isActive: vehicles.isActive,
        make: vehicleMake.name,
        model: vehicleModel.model,
        yearModel: vehicleModel.yearModel,
        currentBookingId: sql<number | null>`(
          SELECT b.id 
          FROM ${bookings} b 
          WHERE b.vehicle_id = ${vehicles.id} 
            AND b.status IN ('CONFIRMED', 'PENDING') 
            AND b.start_datetime <= NOW() 
            AND b.end_datetime >= NOW() 
          LIMIT 1
        )`,
      })
      .from(vehicles)
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .innerJoin(party, eq(vehicles.partyId, party.id))
      .innerJoin(company, eq(party.id, company.partyId))
      .where(eq(company.id, groupId))
      .orderBy(vehicles.id);

    return groupVehicles.map((vehicle) => ({
      id: vehicle.id,
      name: `${vehicle.make} ${vehicle.model} (${vehicle.yearModel})`,
      registration: vehicle.vehicleRegistration,
      status: !vehicle.isActive
        ? "maintenance"
        : vehicle.currentBookingId
          ? "in-use"
          : "available",
      image: "/placeholder.svg?height=60&width=100",
    }));
  } catch (error) {
    console.error("Error fetching group vehicles with status:", error);
    return [];
  }
}

// Helper function to format booking dates
function formatBookingDate(dateString: string | null): string {
  if (!dateString) return "Unknown date";

  const date = new Date(dateString);
  const now = new Date();
  const diffTime = date.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return `Today, ${date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`;
  } else if (diffDays === 1) {
    return `Tomorrow, ${date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`;
  } else if (diffDays < 7) {
    return `${date.toLocaleDateString("en-US", { weekday: "short" })}, ${date.toLocaleDateString("en-US", { month: "short", day: "numeric" })}, ${date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`;
  } else {
    return date.toLocaleDateString("en-US", {
      weekday: "short",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }
}

// Get group member details
export async function getGroupMember(memberId: number) {
  try {
    const member = await db
      .select({
        id: individual.id,
        partyId: party.id,
        firstName: individual.firstName,
        lastName: individual.lastName,
        email: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          JOIN contact_point_type cpt ON cp.contact_point_type_id = cpt.id 
          WHERE cp.party_id = ${party.id} 
            AND cpt.name = 'Email' 
            AND cp.is_primary = true 
          LIMIT 1
        )`,
        phone: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          JOIN contact_point_type cpt ON cp.contact_point_type_id = cpt.id 
          WHERE cp.party_id = ${party.id} 
            AND cpt.name = 'Phone' 
            AND cp.is_primary = true 
          LIMIT 1
        )`,
      })
      .from(individual)
      .innerJoin(party, eq(individual.partyId, party.id))
      .where(eq(individual.id, memberId))
      .limit(1);

    if (member.length === 0) {
      return null;
    }

    const memberData = member[0];
    return {
      id: memberData.id,
      name: `${memberData.firstName} ${memberData.lastName}`,
      email: memberData.email || "No email provided",
      phone: memberData.phone || "No phone provided",
      avatar: "/placeholder.svg?height=100&width=100",
    };
  } catch (error) {
    console.error("Error fetching member details:", error);
    return null;
  }
}

// Create a new company (group)
export async function createGroup(
  name: string,
  description: string,
  purpose: string,
  registrationNumber: string,
  registrationCountry: string,
  currentUserPartyId: number,
  currentUserFraction: number = 1.0,
  cityId?: number
) {
  try {
    // First create a party for the company
    const [newParty] = await db
      .insert(party)
      .values({
        partyTypeId: 2, // Assuming party type 2 is for companies
        statusId: 1, // Assuming status 1 is active
      })
      .returning();

    // Then create the company
    const [newCompany] = await db
      .insert(company)
      .values({
        partyId: newParty.id,
        alias: name,
        description: description,
        purpose: purpose,
        registrationNumber: registrationNumber,
        registrationCountry: registrationCountry,
        registrationDate: new Date().toISOString(),
        cityId: cityId,
      })
      .returning();

    // Add the current user as the first member (owner)
    await db.insert(companyOwnership).values({
      companyId: newCompany.id,
      partyId: currentUserPartyId,
      fraction: currentUserFraction.toString(),
      effectiveFrom: new Date().toISOString(),
      isActive: true,
    });

    return {
      success: true,
      groupId: newCompany.id,
      message: "Group created successfully",
    };
  } catch (error) {
    console.error("Error creating group:", error);
    return {
      success: false,
      message: "Failed to create group",
    };
  }
}

// Invite members to a group
export async function inviteMembersToGroup(
  groupId: number,
  invitations: Array<{
    email: string;
    firstName: string;
    lastName: string;
    fraction: number;
  }>
) {
  try {
    // Insert all invitations
    const inviteData = invitations.map((invite) => ({
      companyId: groupId,
      email: invite.email,
      firstName: invite.firstName,
      lastName: invite.lastName,
      fraction: invite.fraction.toString(),
      status: "SENT" as const,
    }));

    await db.insert(companyOwnershipInvite).values(inviteData);

    return {
      success: true,
      message: `${invitations.length} invitation(s) sent successfully`,
    };
  } catch (error) {
    console.error("Error inviting members to group:", error);
    return {
      success: false,
      message: "Failed to send invitations",
    };
  }
}

// Get pending invitations for a group
export async function getGroupInvitations(groupId: number) {
  try {
    const invitations = await db
      .select({
        id: companyOwnershipInvite.id,
        email: companyOwnershipInvite.email,
        firstName: companyOwnershipInvite.firstName,
        lastName: companyOwnershipInvite.lastName,
        fraction: companyOwnershipInvite.fraction,
        status: companyOwnershipInvite.status,
        createdAt: companyOwnershipInvite.createdAt,
      })
      .from(companyOwnershipInvite)
      .where(eq(companyOwnershipInvite.companyId, groupId))
      .orderBy(desc(companyOwnershipInvite.createdAt));

    return invitations.map((invite) => ({
      id: invite.id,
      email: invite.email,
      name: `${invite.firstName} ${invite.lastName}`,
      firstName: invite.firstName,
      lastName: invite.lastName,
      fraction: parseFloat(invite.fraction || "0"),
      status: invite.status,
      sentAt: invite.createdAt,
    }));
  } catch (error) {
    console.error("Error fetching group invitations:", error);
    return [];
  }
}

// Cancel an invitation
export async function cancelInvitation(invitationId: number) {
  try {
    await db
      .update(companyOwnershipInvite)
      .set({
        status: "DECLINED",
        updatedAt: new Date().toISOString(),
      })
      .where(eq(companyOwnershipInvite.id, invitationId));

    return {
      success: true,
      message: "Invitation cancelled successfully",
    };
  } catch (error) {
    console.error("Error cancelling invitation:", error);
    return {
      success: false,
      message: "Failed to cancel invitation",
    };
  }
}

// Get current user's party ID (this would normally be from auth context)
export async function getCurrentUserPartyId(userId: number) {
  try {
    // This is a placeholder - you'll need to implement based on your auth system
    // For now, let's assume we can get it from the users table or session
    const user = await db
      .select({ partyId: sql<number>`1` }) // Placeholder - replace with actual logic
      .from(sql`(SELECT 1) as dummy`)
      .limit(1);

    return user[0]?.partyId || 1; // Default to party ID 1 for now
  } catch (error) {
    console.error("Error getting current user party ID:", error);
    return 1; // Default fallback
  }
}

// Get countries for registration dropdown
export async function getCountries() {
  try {
    const countries = await db
      .select({
        country: cities.country,
      })
      .from(cities)
      .groupBy(cities.country)
      .orderBy(cities.country);

    return countries.map((c) => c.country);
  } catch (error) {
    console.error("Error fetching countries:", error);
    return ["South Africa"]; // Default fallback
  }
}

// Add a member to a group
export async function addMemberToGroup(
  groupId: number,
  individualId: number,
  fraction: number = 0.1
) {
  try {
    // Get the individual's party ID
    const individualData = await db
      .select({ partyId: individual.partyId })
      .from(individual)
      .where(eq(individual.id, individualId))
      .limit(1);

    if (individualData.length === 0) {
      return {
        success: false,
        message: "Individual not found",
      };
    }

    // Check if the individual is already a member of this group
    const existingMembership = await db
      .select()
      .from(companyOwnership)
      .where(
        and(
          eq(companyOwnership.companyId, groupId),
          eq(companyOwnership.partyId, individualData[0].partyId)
        )
      )
      .limit(1);

    if (existingMembership.length > 0) {
      return {
        success: false,
        message: "Individual is already a member of this group",
      };
    }

    // Add the individual to the company ownership
    await db.insert(companyOwnership).values({
      companyId: groupId,
      partyId: individualData[0].partyId,
      fraction: fraction.toString(),
      effectiveFrom: new Date().toISOString(),
      isActive: true,
    });

    return {
      success: true,
      message: "Member added successfully",
    };
  } catch (error) {
    console.error("Error adding member to group:", error);
    return {
      success: false,
      message: "Failed to add member to group",
    };
  }
}

// Remove a member from a group
export async function removeMemberFromGroup(
  groupId: number,
  individualId: number
) {
  try {
    // Get the individual's party ID
    const individualData = await db
      .select({ partyId: individual.partyId })
      .from(individual)
      .where(eq(individual.id, individualId))
      .limit(1);

    if (individualData.length === 0) {
      return {
        success: false,
        message: "Individual not found",
      };
    }

    // Remove the individual from company ownership
    await db
      .update(companyOwnership)
      .set({
        isActive: false,
        effectiveTo: new Date().toISOString(),
      })
      .where(
        and(
          eq(companyOwnership.companyId, groupId),
          eq(companyOwnership.partyId, individualData[0].partyId)
        )
      );

    return {
      success: true,
      message: "Member removed successfully",
    };
  } catch (error) {
    console.error("Error removing member from group:", error);
    return {
      success: false,
      message: "Failed to remove member from group",
    };
  }
}

// Search for individuals to add to a group
export async function searchIndividuals(searchTerm: string) {
  try {
    const individuals = await db
      .select({
        id: individual.id,
        partyId: individual.partyId,
        firstName: individual.firstName,
        lastName: individual.lastName,
        email: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          JOIN contact_point_type cpt ON cp.contact_point_type_id = cpt.id 
          WHERE cp.party_id = ${individual.partyId} 
            AND cpt.name = 'Email' 
            AND cp.is_primary = true 
          LIMIT 1
        )`,
      })
      .from(individual)
      .where(
        sql`LOWER(${individual.firstName} || ' ' || ${individual.lastName}) LIKE LOWER(${"%" + searchTerm + "%"})`
      )
      .limit(20);

    return individuals.map((ind) => ({
      id: ind.id,
      name: `${ind.firstName} ${ind.lastName}`,
      email: ind.email || "No email provided",
      avatar: "/placeholder.svg?height=40&width=40",
    }));
  } catch (error) {
    console.error("Error searching individuals:", error);
    return [];
  }
}

// Get available cities for group creation
export async function getCities() {
  try {
    const cityList = await db
      .select({
        id: cities.id,
        name: cities.name,
        province: cities.province,
        country: cities.country,
      })
      .from(cities)
      .orderBy(cities.name);

    return cityList;
  } catch (error) {
    console.error("Error fetching cities:", error);
    return [];
  }
}

// Update group information
export async function updateGroup(
  groupId: number,
  name?: string,
  description?: string,
  cityId?: number
) {
  try {
    const updateData: any = {};

    if (name !== undefined) updateData.alias = name;
    if (description !== undefined) updateData.description = description;
    if (cityId !== undefined) updateData.cityId = cityId;

    if (Object.keys(updateData).length === 0) {
      return {
        success: false,
        message: "No data to update",
      };
    }

    await db.update(company).set(updateData).where(eq(company.id, groupId));

    return {
      success: true,
      message: "Group updated successfully",
    };
  } catch (error) {
    console.error("Error updating group:", error);
    return {
      success: false,
      message: "Failed to update group",
    };
  }
}
