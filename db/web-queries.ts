"use server"
import { Pool } from 'pg';

const webdbpool = new Pool({
  connectionString: process.env.WEB_DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : undefined,
});

export type FormType = "business" | "co-own" | "management" | "rideshare" | "monetise" | "waitlist";

export interface FormSubmission {
  id: string;
  formType: FormType;
  submittedAt: Date;
  fullName: string;
  email: string;
  companyName: string | null;
  phoneNumber: string | null;
  formData: any;
  emailSent: boolean;
  emailSentAt: Date | null;
  emailError: string | null;
}

/**
 * Get all form submissions with pagination
 */
export async function getFormSubmissions(options?: {
  limit?: number;
  offset?: number;
  formType?: FormType;
  emailSent?: boolean;
}) {
  const { limit = 50, offset = 0, formType, emailSent } = options || {};
  
  let query = `
    SELECT id, form_type, submitted_at, full_name, email, company_name, 
           phone_number, form_data, email_sent, email_sent_at, email_error
    FROM form_submissions
  `;
  
  const conditions: string[] = [];
  const values: any[] = [];
  let paramIndex = 1;
  
  if (formType) {
    conditions.push(`form_type = $${paramIndex++}`);
    values.push(formType);
  }
  
  if (emailSent !== undefined) {
    conditions.push(`email_sent = $${paramIndex++}`);
    values.push(emailSent);
  }
  
  if (conditions.length > 0) {
    query += ` WHERE ${conditions.join(' AND ')}`;
  }
  
  query += ` ORDER BY submitted_at DESC LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
  values.push(limit, offset);
  
  const client = await webdbpool.connect();
  try {
    const result = await client.query(query, values);
    return result.rows.map(row => ({
      id: row.id,
      formType: row.form_type,
      submittedAt: row.submitted_at,
      fullName: row.full_name,
      email: row.email,
      companyName: row.company_name,
      phoneNumber: row.phone_number,
      formData: row.form_data,
      emailSent: row.email_sent,
      emailSentAt: row.email_sent_at,
      emailError: row.email_error,
    })) as FormSubmission[];
  } finally {
    client.release();
  }
}

/**
 * Get form submission by ID
 */
export async function getFormSubmissionById(id: string) {
  const query = `
    SELECT id, form_type, submitted_at, full_name, email, company_name, 
           phone_number, form_data, email_sent, email_sent_at, email_error
    FROM form_submissions
    WHERE id = $1
  `;
  
  const client = await webdbpool.connect();
  try {
    const result = await client.query(query, [id]);
    if (result.rows.length === 0) return null;
    
    const row = result.rows[0];
    return {
      id: row.id,
      formType: row.form_type,
      submittedAt: row.submitted_at,
      fullName: row.full_name,
      email: row.email,
      companyName: row.company_name,
      phoneNumber: row.phone_number,
      formData: row.form_data,
      emailSent: row.email_sent,
      emailSentAt: row.email_sent_at,
      emailError: row.email_error,
    } as FormSubmission;
  } finally {
    client.release();
  }
}

/**
 * Get form submissions count by type
 */
export async function getFormSubmissionStats() {
  const query = `
    SELECT 
      form_type,
      COUNT(*) as count,
      COUNT(CASE WHEN email_sent = true THEN 1 END) as emails_sent
    FROM form_submissions
    GROUP BY form_type
  `;
  
  const client = await webdbpool.connect();
  try {
    const result = await client.query(query);
    return result.rows.map(row => ({
      formType: row.form_type,
      count: parseInt(row.count),
      emailsSent: parseInt(row.emails_sent),
    }));
  } finally {
    client.release();
  }
}

/**
 * Get form submissions by date range
 */
export async function getFormSubmissionsByDateRange(
  startDate: Date,
  endDate: Date,
  formType?: FormType
) {
  let query = `
    SELECT id, form_type, submitted_at, full_name, email, company_name, 
           phone_number, form_data, email_sent, email_sent_at, email_error
    FROM form_submissions
    WHERE submitted_at >= $1 AND submitted_at <= $2
  `;
  
  const values: any[] = [startDate, endDate];
  
  if (formType) {
    query += ` AND form_type = $3`;
    values.push(formType);
  }
  
  query += ` ORDER BY submitted_at DESC`;
  
  const client = await webdbpool.connect();
  try {
    const result = await client.query(query, values);
    return result.rows.map(row => ({
      id: row.id,
      formType: row.form_type,
      submittedAt: row.submitted_at,
      fullName: row.full_name,
      email: row.email,
      companyName: row.company_name,
      phoneNumber: row.phone_number,
      formData: row.form_data,
      emailSent: row.email_sent,
      emailSentAt: row.email_sent_at,
      emailError: row.email_error,
    })) as FormSubmission[];
  } finally {
    client.release();
  }
}

/**
 * Get failed email submissions that need retry
 */
export async function getFailedEmailSubmissions(limit = 100) {
  const query = `
    SELECT id, form_type, submitted_at, full_name, email, company_name, 
           phone_number, form_data, email_sent, email_sent_at, email_error
    FROM form_submissions
    WHERE email_sent = false 
    AND submitted_at <= $1
    ORDER BY submitted_at DESC
    LIMIT $2
  `;
  
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  
  const client = await webdbpool.connect();
  try {
    const result = await client.query(query, [fiveMinutesAgo, limit]);
    return result.rows.map(row => ({
      id: row.id,
      formType: row.form_type,
      submittedAt: row.submitted_at,
      fullName: row.full_name,
      email: row.email,
      companyName: row.company_name,
      phoneNumber: row.phone_number,
      formData: row.form_data,
      emailSent: row.email_sent,
      emailSentAt: row.email_sent_at,
      emailError: row.email_error,
    })) as FormSubmission[];
  } finally {
    client.release();
  }
} 