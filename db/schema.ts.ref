import { pgTable, text, timestamp, integer, boolean, json } from "drizzle-orm/pg-core";

export const formSubmissions = pgTable("form_submissions", {
  // Primary key and metadata
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  formType: text("form_type", { 
    enum: ["business", "co-own", "management", "rideshare", "monetise", "waitlist"] 
  }).notNull(),
  submittedAt: timestamp("submitted_at").defaultNow().notNull(),
  
  // Common customer information fields (always present)
  fullName: text("full_name").notNull(),
  email: text("email").notNull(),
  companyName: text("company_name"), // nullable
  phoneNumber: text("phone_number"), // nullable
  
  // Form-specific data stored as JSON
  formData: json("form_data").notNull(), // All form-specific fields
  
  // Email processing status
  emailSent: boolean("email_sent").default(false).notNull(),
  emailSentAt: timestamp("email_sent_at"), // nullable
  emailError: text("email_error"), // nullable
});

export type FormSubmission = typeof formSubmissions.$inferSelect;
export type InsertFormSubmission = typeof formSubmissions.$inferInsert;